{"version": 3, "sources": ["../../lodash.debounce/index.js"], "sourcesContent": ["/**\r\n * lodash (Custom Build) <https://lodash.com/>\r\n * Build: `lodash modularize exports=\"npm\" -o ./`\r\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\r\n * Released under MIT license <https://lodash.com/license>\r\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\r\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\r\n */\r\n\r\n/** Used as the `TypeError` message for \"Functions\" methods. */\r\nvar FUNC_ERROR_TEXT = 'Expected a function';\r\n\r\n/** Used as references for various `Number` constants. */\r\nvar NAN = 0 / 0;\r\n\r\n/** `Object#toString` result references. */\r\nvar symbolTag = '[object Symbol]';\r\n\r\n/** Used to match leading and trailing whitespace. */\r\nvar reTrim = /^\\s+|\\s+$/g;\r\n\r\n/** Used to detect bad signed hexadecimal string values. */\r\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\r\n\r\n/** Used to detect binary string values. */\r\nvar reIsBinary = /^0b[01]+$/i;\r\n\r\n/** Used to detect octal string values. */\r\nvar reIsOctal = /^0o[0-7]+$/i;\r\n\r\n/** Built-in method references without a dependency on `root`. */\r\nvar freeParseInt = parseInt;\r\n\r\n/** Detect free variable `global` from Node.js. */\r\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\r\n\r\n/** Detect free variable `self`. */\r\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\r\n\r\n/** Used as a reference to the global object. */\r\nvar root = freeGlobal || freeSelf || Function('return this')();\r\n\r\n/** Used for built-in method references. */\r\nvar objectProto = Object.prototype;\r\n\r\n/**\r\n * Used to resolve the\r\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\r\n * of values.\r\n */\r\nvar objectToString = objectProto.toString;\r\n\r\n/* Built-in method references for those with the same name as other `lodash` methods. */\r\nvar nativeMax = Math.max,\r\n    nativeMin = Math.min;\r\n\r\n/**\r\n * Gets the timestamp of the number of milliseconds that have elapsed since\r\n * the Unix epoch (1 January 1970 00:00:00 UTC).\r\n *\r\n * @static\r\n * @memberOf _\r\n * @since 2.4.0\r\n * @category Date\r\n * @returns {number} Returns the timestamp.\r\n * @example\r\n *\r\n * _.defer(function(stamp) {\r\n *   console.log(_.now() - stamp);\r\n * }, _.now());\r\n * // => Logs the number of milliseconds it took for the deferred invocation.\r\n */\r\nvar now = function() {\r\n  return root.Date.now();\r\n};\r\n\r\n/**\r\n * Creates a debounced function that delays invoking `func` until after `wait`\r\n * milliseconds have elapsed since the last time the debounced function was\r\n * invoked. The debounced function comes with a `cancel` method to cancel\r\n * delayed `func` invocations and a `flush` method to immediately invoke them.\r\n * Provide `options` to indicate whether `func` should be invoked on the\r\n * leading and/or trailing edge of the `wait` timeout. The `func` is invoked\r\n * with the last arguments provided to the debounced function. Subsequent\r\n * calls to the debounced function return the result of the last `func`\r\n * invocation.\r\n *\r\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\r\n * invoked on the trailing edge of the timeout only if the debounced function\r\n * is invoked more than once during the `wait` timeout.\r\n *\r\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\r\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\r\n *\r\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\r\n * for details over the differences between `_.debounce` and `_.throttle`.\r\n *\r\n * @static\r\n * @memberOf _\r\n * @since 0.1.0\r\n * @category Function\r\n * @param {Function} func The function to debounce.\r\n * @param {number} [wait=0] The number of milliseconds to delay.\r\n * @param {Object} [options={}] The options object.\r\n * @param {boolean} [options.leading=false]\r\n *  Specify invoking on the leading edge of the timeout.\r\n * @param {number} [options.maxWait]\r\n *  The maximum time `func` is allowed to be delayed before it's invoked.\r\n * @param {boolean} [options.trailing=true]\r\n *  Specify invoking on the trailing edge of the timeout.\r\n * @returns {Function} Returns the new debounced function.\r\n * @example\r\n *\r\n * // Avoid costly calculations while the window size is in flux.\r\n * jQuery(window).on('resize', _.debounce(calculateLayout, 150));\r\n *\r\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\r\n * jQuery(element).on('click', _.debounce(sendMail, 300, {\r\n *   'leading': true,\r\n *   'trailing': false\r\n * }));\r\n *\r\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\r\n * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });\r\n * var source = new EventSource('/stream');\r\n * jQuery(source).on('message', debounced);\r\n *\r\n * // Cancel the trailing debounced invocation.\r\n * jQuery(window).on('popstate', debounced.cancel);\r\n */\r\nfunction debounce(func, wait, options) {\r\n  var lastArgs,\r\n      lastThis,\r\n      maxWait,\r\n      result,\r\n      timerId,\r\n      lastCallTime,\r\n      lastInvokeTime = 0,\r\n      leading = false,\r\n      maxing = false,\r\n      trailing = true;\r\n\r\n  if (typeof func != 'function') {\r\n    throw new TypeError(FUNC_ERROR_TEXT);\r\n  }\r\n  wait = toNumber(wait) || 0;\r\n  if (isObject(options)) {\r\n    leading = !!options.leading;\r\n    maxing = 'maxWait' in options;\r\n    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\r\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\r\n  }\r\n\r\n  function invokeFunc(time) {\r\n    var args = lastArgs,\r\n        thisArg = lastThis;\r\n\r\n    lastArgs = lastThis = undefined;\r\n    lastInvokeTime = time;\r\n    result = func.apply(thisArg, args);\r\n    return result;\r\n  }\r\n\r\n  function leadingEdge(time) {\r\n    // Reset any `maxWait` timer.\r\n    lastInvokeTime = time;\r\n    // Start the timer for the trailing edge.\r\n    timerId = setTimeout(timerExpired, wait);\r\n    // Invoke the leading edge.\r\n    return leading ? invokeFunc(time) : result;\r\n  }\r\n\r\n  function remainingWait(time) {\r\n    var timeSinceLastCall = time - lastCallTime,\r\n        timeSinceLastInvoke = time - lastInvokeTime,\r\n        result = wait - timeSinceLastCall;\r\n\r\n    return maxing ? nativeMin(result, maxWait - timeSinceLastInvoke) : result;\r\n  }\r\n\r\n  function shouldInvoke(time) {\r\n    var timeSinceLastCall = time - lastCallTime,\r\n        timeSinceLastInvoke = time - lastInvokeTime;\r\n\r\n    // Either this is the first call, activity has stopped and we're at the\r\n    // trailing edge, the system time has gone backwards and we're treating\r\n    // it as the trailing edge, or we've hit the `maxWait` limit.\r\n    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||\r\n      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));\r\n  }\r\n\r\n  function timerExpired() {\r\n    var time = now();\r\n    if (shouldInvoke(time)) {\r\n      return trailingEdge(time);\r\n    }\r\n    // Restart the timer.\r\n    timerId = setTimeout(timerExpired, remainingWait(time));\r\n  }\r\n\r\n  function trailingEdge(time) {\r\n    timerId = undefined;\r\n\r\n    // Only invoke if we have `lastArgs` which means `func` has been\r\n    // debounced at least once.\r\n    if (trailing && lastArgs) {\r\n      return invokeFunc(time);\r\n    }\r\n    lastArgs = lastThis = undefined;\r\n    return result;\r\n  }\r\n\r\n  function cancel() {\r\n    if (timerId !== undefined) {\r\n      clearTimeout(timerId);\r\n    }\r\n    lastInvokeTime = 0;\r\n    lastArgs = lastCallTime = lastThis = timerId = undefined;\r\n  }\r\n\r\n  function flush() {\r\n    return timerId === undefined ? result : trailingEdge(now());\r\n  }\r\n\r\n  function debounced() {\r\n    var time = now(),\r\n        isInvoking = shouldInvoke(time);\r\n\r\n    lastArgs = arguments;\r\n    lastThis = this;\r\n    lastCallTime = time;\r\n\r\n    if (isInvoking) {\r\n      if (timerId === undefined) {\r\n        return leadingEdge(lastCallTime);\r\n      }\r\n      if (maxing) {\r\n        // Handle invocations in a tight loop.\r\n        timerId = setTimeout(timerExpired, wait);\r\n        return invokeFunc(lastCallTime);\r\n      }\r\n    }\r\n    if (timerId === undefined) {\r\n      timerId = setTimeout(timerExpired, wait);\r\n    }\r\n    return result;\r\n  }\r\n  debounced.cancel = cancel;\r\n  debounced.flush = flush;\r\n  return debounced;\r\n}\r\n\r\n/**\r\n * Checks if `value` is the\r\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\r\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\r\n *\r\n * @static\r\n * @memberOf _\r\n * @since 0.1.0\r\n * @category Lang\r\n * @param {*} value The value to check.\r\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\r\n * @example\r\n *\r\n * _.isObject({});\r\n * // => true\r\n *\r\n * _.isObject([1, 2, 3]);\r\n * // => true\r\n *\r\n * _.isObject(_.noop);\r\n * // => true\r\n *\r\n * _.isObject(null);\r\n * // => false\r\n */\r\nfunction isObject(value) {\r\n  var type = typeof value;\r\n  return !!value && (type == 'object' || type == 'function');\r\n}\r\n\r\n/**\r\n * Checks if `value` is object-like. A value is object-like if it's not `null`\r\n * and has a `typeof` result of \"object\".\r\n *\r\n * @static\r\n * @memberOf _\r\n * @since 4.0.0\r\n * @category Lang\r\n * @param {*} value The value to check.\r\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\r\n * @example\r\n *\r\n * _.isObjectLike({});\r\n * // => true\r\n *\r\n * _.isObjectLike([1, 2, 3]);\r\n * // => true\r\n *\r\n * _.isObjectLike(_.noop);\r\n * // => false\r\n *\r\n * _.isObjectLike(null);\r\n * // => false\r\n */\r\nfunction isObjectLike(value) {\r\n  return !!value && typeof value == 'object';\r\n}\r\n\r\n/**\r\n * Checks if `value` is classified as a `Symbol` primitive or object.\r\n *\r\n * @static\r\n * @memberOf _\r\n * @since 4.0.0\r\n * @category Lang\r\n * @param {*} value The value to check.\r\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\r\n * @example\r\n *\r\n * _.isSymbol(Symbol.iterator);\r\n * // => true\r\n *\r\n * _.isSymbol('abc');\r\n * // => false\r\n */\r\nfunction isSymbol(value) {\r\n  return typeof value == 'symbol' ||\r\n    (isObjectLike(value) && objectToString.call(value) == symbolTag);\r\n}\r\n\r\n/**\r\n * Converts `value` to a number.\r\n *\r\n * @static\r\n * @memberOf _\r\n * @since 4.0.0\r\n * @category Lang\r\n * @param {*} value The value to process.\r\n * @returns {number} Returns the number.\r\n * @example\r\n *\r\n * _.toNumber(3.2);\r\n * // => 3.2\r\n *\r\n * _.toNumber(Number.MIN_VALUE);\r\n * // => 5e-324\r\n *\r\n * _.toNumber(Infinity);\r\n * // => Infinity\r\n *\r\n * _.toNumber('3.2');\r\n * // => 3.2\r\n */\r\nfunction toNumber(value) {\r\n  if (typeof value == 'number') {\r\n    return value;\r\n  }\r\n  if (isSymbol(value)) {\r\n    return NAN;\r\n  }\r\n  if (isObject(value)) {\r\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\r\n    value = isObject(other) ? (other + '') : other;\r\n  }\r\n  if (typeof value != 'string') {\r\n    return value === 0 ? value : +value;\r\n  }\r\n  value = value.replace(reTrim, '');\r\n  var isBinary = reIsBinary.test(value);\r\n  return (isBinary || reIsOctal.test(value))\r\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\r\n    : (reIsBadHex.test(value) ? NAN : +value);\r\n}\r\n\r\nmodule.exports = debounce;\r\n"], "mappings": ";;;;;AAAA;AAAA;AAUA,QAAI,kBAAkB;AAGtB,QAAI,MAAM,IAAI;AAGd,QAAI,YAAY;AAGhB,QAAI,SAAS;AAGb,QAAI,aAAa;AAGjB,QAAI,aAAa;AAGjB,QAAI,YAAY;AAGhB,QAAI,eAAe;AAGnB,QAAI,aAAa,OAAO,UAAU,YAAY,UAAU,OAAO,WAAW,UAAU;AAGpF,QAAI,WAAW,OAAO,QAAQ,YAAY,QAAQ,KAAK,WAAW,UAAU;AAG5E,QAAI,OAAO,cAAc,YAAY,SAAS,aAAa,EAAE;AAG7D,QAAI,cAAc,OAAO;AAOzB,QAAI,iBAAiB,YAAY;AAGjC,QAAI,YAAY,KAAK;AAArB,QACI,YAAY,KAAK;AAkBrB,QAAI,MAAM,WAAW;AACnB,aAAO,KAAK,KAAK,IAAI;AAAA,IACvB;AAwDA,aAAS,SAAS,MAAM,MAAM,SAAS;AACrC,UAAI,UACA,UACA,SACA,QACA,SACA,cACA,iBAAiB,GACjB,UAAU,OACV,SAAS,OACT,WAAW;AAEf,UAAI,OAAO,QAAQ,YAAY;AAC7B,cAAM,IAAI,UAAU,eAAe;AAAA,MACrC;AACA,aAAO,SAAS,IAAI,KAAK;AACzB,UAAI,SAAS,OAAO,GAAG;AACrB,kBAAU,CAAC,CAAC,QAAQ;AACpB,iBAAS,aAAa;AACtB,kBAAU,SAAS,UAAU,SAAS,QAAQ,OAAO,KAAK,GAAG,IAAI,IAAI;AACrE,mBAAW,cAAc,UAAU,CAAC,CAAC,QAAQ,WAAW;AAAA,MAC1D;AAEA,eAAS,WAAW,MAAM;AACxB,YAAI,OAAO,UACP,UAAU;AAEd,mBAAW,WAAW;AACtB,yBAAiB;AACjB,iBAAS,KAAK,MAAM,SAAS,IAAI;AACjC,eAAO;AAAA,MACT;AAEA,eAAS,YAAY,MAAM;AAEzB,yBAAiB;AAEjB,kBAAU,WAAW,cAAc,IAAI;AAEvC,eAAO,UAAU,WAAW,IAAI,IAAI;AAAA,MACtC;AAEA,eAAS,cAAc,MAAM;AAC3B,YAAI,oBAAoB,OAAO,cAC3B,sBAAsB,OAAO,gBAC7BA,UAAS,OAAO;AAEpB,eAAO,SAAS,UAAUA,SAAQ,UAAU,mBAAmB,IAAIA;AAAA,MACrE;AAEA,eAAS,aAAa,MAAM;AAC1B,YAAI,oBAAoB,OAAO,cAC3B,sBAAsB,OAAO;AAKjC,eAAQ,iBAAiB,UAAc,qBAAqB,QACzD,oBAAoB,KAAO,UAAU,uBAAuB;AAAA,MACjE;AAEA,eAAS,eAAe;AACtB,YAAI,OAAO,IAAI;AACf,YAAI,aAAa,IAAI,GAAG;AACtB,iBAAO,aAAa,IAAI;AAAA,QAC1B;AAEA,kBAAU,WAAW,cAAc,cAAc,IAAI,CAAC;AAAA,MACxD;AAEA,eAAS,aAAa,MAAM;AAC1B,kBAAU;AAIV,YAAI,YAAY,UAAU;AACxB,iBAAO,WAAW,IAAI;AAAA,QACxB;AACA,mBAAW,WAAW;AACtB,eAAO;AAAA,MACT;AAEA,eAAS,SAAS;AAChB,YAAI,YAAY,QAAW;AACzB,uBAAa,OAAO;AAAA,QACtB;AACA,yBAAiB;AACjB,mBAAW,eAAe,WAAW,UAAU;AAAA,MACjD;AAEA,eAAS,QAAQ;AACf,eAAO,YAAY,SAAY,SAAS,aAAa,IAAI,CAAC;AAAA,MAC5D;AAEA,eAAS,YAAY;AACnB,YAAI,OAAO,IAAI,GACX,aAAa,aAAa,IAAI;AAElC,mBAAW;AACX,mBAAW;AACX,uBAAe;AAEf,YAAI,YAAY;AACd,cAAI,YAAY,QAAW;AACzB,mBAAO,YAAY,YAAY;AAAA,UACjC;AACA,cAAI,QAAQ;AAEV,sBAAU,WAAW,cAAc,IAAI;AACvC,mBAAO,WAAW,YAAY;AAAA,UAChC;AAAA,QACF;AACA,YAAI,YAAY,QAAW;AACzB,oBAAU,WAAW,cAAc,IAAI;AAAA,QACzC;AACA,eAAO;AAAA,MACT;AACA,gBAAU,SAAS;AACnB,gBAAU,QAAQ;AAClB,aAAO;AAAA,IACT;AA2BA,aAAS,SAAS,OAAO;AACvB,UAAI,OAAO,OAAO;AAClB,aAAO,CAAC,CAAC,UAAU,QAAQ,YAAY,QAAQ;AAAA,IACjD;AA0BA,aAAS,aAAa,OAAO;AAC3B,aAAO,CAAC,CAAC,SAAS,OAAO,SAAS;AAAA,IACpC;AAmBA,aAAS,SAAS,OAAO;AACvB,aAAO,OAAO,SAAS,YACpB,aAAa,KAAK,KAAK,eAAe,KAAK,KAAK,KAAK;AAAA,IAC1D;AAyBA,aAAS,SAAS,OAAO;AACvB,UAAI,OAAO,SAAS,UAAU;AAC5B,eAAO;AAAA,MACT;AACA,UAAI,SAAS,KAAK,GAAG;AACnB,eAAO;AAAA,MACT;AACA,UAAI,SAAS,KAAK,GAAG;AACnB,YAAI,QAAQ,OAAO,MAAM,WAAW,aAAa,MAAM,QAAQ,IAAI;AACnE,gBAAQ,SAAS,KAAK,IAAK,QAAQ,KAAM;AAAA,MAC3C;AACA,UAAI,OAAO,SAAS,UAAU;AAC5B,eAAO,UAAU,IAAI,QAAQ,CAAC;AAAA,MAChC;AACA,cAAQ,MAAM,QAAQ,QAAQ,EAAE;AAChC,UAAI,WAAW,WAAW,KAAK,KAAK;AACpC,aAAQ,YAAY,UAAU,KAAK,KAAK,IACpC,aAAa,MAAM,MAAM,CAAC,GAAG,WAAW,IAAI,CAAC,IAC5C,WAAW,KAAK,KAAK,IAAI,MAAM,CAAC;AAAA,IACvC;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": ["result"]}