{"version": 3, "sources": ["../../uuid/dist/esm-browser/max.js", "../../uuid/dist/esm-browser/nil.js", "../../uuid/dist/esm-browser/regex.js", "../../uuid/dist/esm-browser/validate.js", "../../uuid/dist/esm-browser/parse.js", "../../uuid/dist/esm-browser/stringify.js", "../../uuid/dist/esm-browser/rng.js", "../../uuid/dist/esm-browser/v1.js", "../../uuid/dist/esm-browser/v1ToV6.js", "../../uuid/dist/esm-browser/md5.js", "../../uuid/dist/esm-browser/v35.js", "../../uuid/dist/esm-browser/v3.js", "../../uuid/dist/esm-browser/native.js", "../../uuid/dist/esm-browser/v4.js", "../../uuid/dist/esm-browser/sha1.js", "../../uuid/dist/esm-browser/v5.js", "../../uuid/dist/esm-browser/v6.js", "../../uuid/dist/esm-browser/v6ToV1.js", "../../uuid/dist/esm-browser/v7.js", "../../uuid/dist/esm-browser/version.js"], "sourcesContent": ["export default 'ffffffff-ffff-ffff-ffff-ffffffffffff';\r\n", "export default '00000000-0000-0000-0000-000000000000';\r\n", "export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i;\r\n", "import REGEX from './regex.js';\r\nfunction validate(uuid) {\r\n    return typeof uuid === 'string' && REGEX.test(uuid);\r\n}\r\nexport default validate;\r\n", "import validate from './validate.js';\r\nfunction parse(uuid) {\r\n    if (!validate(uuid)) {\r\n        throw TypeError('Invalid UUID');\r\n    }\r\n    let v;\r\n    return Uint8Array.of((v = parseInt(uuid.slice(0, 8), 16)) >>> 24, (v >>> 16) & 0xff, (v >>> 8) & 0xff, v & 0xff, (v = parseInt(uuid.slice(9, 13), 16)) >>> 8, v & 0xff, (v = parseInt(uuid.slice(14, 18), 16)) >>> 8, v & 0xff, (v = parseInt(uuid.slice(19, 23), 16)) >>> 8, v & 0xff, ((v = parseInt(uuid.slice(24, 36), 16)) / 0x10000000000) & 0xff, (v / 0x100000000) & 0xff, (v >>> 24) & 0xff, (v >>> 16) & 0xff, (v >>> 8) & 0xff, v & 0xff);\r\n}\r\nexport default parse;\r\n", "import validate from './validate.js';\r\nconst byteToHex = [];\r\nfor (let i = 0; i < 256; ++i) {\r\n    byteToHex.push((i + 0x100).toString(16).slice(1));\r\n}\r\nexport function unsafeStringify(arr, offset = 0) {\r\n    return (byteToHex[arr[offset + 0]] +\r\n        byteToHex[arr[offset + 1]] +\r\n        byteToHex[arr[offset + 2]] +\r\n        byteToHex[arr[offset + 3]] +\r\n        '-' +\r\n        byteToHex[arr[offset + 4]] +\r\n        byteToHex[arr[offset + 5]] +\r\n        '-' +\r\n        byteToHex[arr[offset + 6]] +\r\n        byteToHex[arr[offset + 7]] +\r\n        '-' +\r\n        byteToHex[arr[offset + 8]] +\r\n        byteToHex[arr[offset + 9]] +\r\n        '-' +\r\n        byteToHex[arr[offset + 10]] +\r\n        byteToHex[arr[offset + 11]] +\r\n        byteToHex[arr[offset + 12]] +\r\n        byteToHex[arr[offset + 13]] +\r\n        byteToHex[arr[offset + 14]] +\r\n        byteToHex[arr[offset + 15]]).toLowerCase();\r\n}\r\nfunction stringify(arr, offset = 0) {\r\n    const uuid = unsafeStringify(arr, offset);\r\n    if (!validate(uuid)) {\r\n        throw TypeError('Stringified UUID is invalid');\r\n    }\r\n    return uuid;\r\n}\r\nexport default stringify;\r\n", "let getRandomValues;\r\nconst rnds8 = new Uint8Array(16);\r\nexport default function rng() {\r\n    if (!getRandomValues) {\r\n        if (typeof crypto === 'undefined' || !crypto.getRandomValues) {\r\n            throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\r\n        }\r\n        getRandomValues = crypto.getRandomValues.bind(crypto);\r\n    }\r\n    return getRandomValues(rnds8);\r\n}\r\n", "import rng from './rng.js';\r\nimport { unsafeStringify } from './stringify.js';\r\nconst _state = {};\r\nfunction v1(options, buf, offset) {\r\n    let bytes;\r\n    const isV6 = options?._v6 ?? false;\r\n    if (options) {\r\n        const optionsKeys = Object.keys(options);\r\n        if (optionsKeys.length === 1 && optionsKeys[0] === '_v6') {\r\n            options = undefined;\r\n        }\r\n    }\r\n    if (options) {\r\n        bytes = v1Bytes(options.random ?? options.rng?.() ?? rng(), options.msecs, options.nsecs, options.clockseq, options.node, buf, offset);\r\n    }\r\n    else {\r\n        const now = Date.now();\r\n        const rnds = rng();\r\n        updateV1State(_state, now, rnds);\r\n        bytes = v1Bytes(rnds, _state.msecs, _state.nsecs, isV6 ? undefined : _state.clockseq, isV6 ? undefined : _state.node, buf, offset);\r\n    }\r\n    return buf ?? unsafeStringify(bytes);\r\n}\r\nexport function updateV1State(state, now, rnds) {\r\n    state.msecs ??= -Infinity;\r\n    state.nsecs ??= 0;\r\n    if (now === state.msecs) {\r\n        state.nsecs++;\r\n        if (state.nsecs >= 10000) {\r\n            state.node = undefined;\r\n            state.nsecs = 0;\r\n        }\r\n    }\r\n    else if (now > state.msecs) {\r\n        state.nsecs = 0;\r\n    }\r\n    else if (now < state.msecs) {\r\n        state.node = undefined;\r\n    }\r\n    if (!state.node) {\r\n        state.node = rnds.slice(10, 16);\r\n        state.node[0] |= 0x01;\r\n        state.clockseq = ((rnds[8] << 8) | rnds[9]) & 0x3fff;\r\n    }\r\n    state.msecs = now;\r\n    return state;\r\n}\r\nfunction v1Bytes(rnds, msecs, nsecs, clockseq, node, buf, offset = 0) {\r\n    if (rnds.length < 16) {\r\n        throw new Error('Random bytes length must be >= 16');\r\n    }\r\n    if (!buf) {\r\n        buf = new Uint8Array(16);\r\n        offset = 0;\r\n    }\r\n    else {\r\n        if (offset < 0 || offset + 16 > buf.length) {\r\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\r\n        }\r\n    }\r\n    msecs ??= Date.now();\r\n    nsecs ??= 0;\r\n    clockseq ??= ((rnds[8] << 8) | rnds[9]) & 0x3fff;\r\n    node ??= rnds.slice(10, 16);\r\n    msecs += 12219292800000;\r\n    const tl = ((msecs & 0xfffffff) * 10000 + nsecs) % 0x100000000;\r\n    buf[offset++] = (tl >>> 24) & 0xff;\r\n    buf[offset++] = (tl >>> 16) & 0xff;\r\n    buf[offset++] = (tl >>> 8) & 0xff;\r\n    buf[offset++] = tl & 0xff;\r\n    const tmh = ((msecs / 0x100000000) * 10000) & 0xfffffff;\r\n    buf[offset++] = (tmh >>> 8) & 0xff;\r\n    buf[offset++] = tmh & 0xff;\r\n    buf[offset++] = ((tmh >>> 24) & 0xf) | 0x10;\r\n    buf[offset++] = (tmh >>> 16) & 0xff;\r\n    buf[offset++] = (clockseq >>> 8) | 0x80;\r\n    buf[offset++] = clockseq & 0xff;\r\n    for (let n = 0; n < 6; ++n) {\r\n        buf[offset++] = node[n];\r\n    }\r\n    return buf;\r\n}\r\nexport default v1;\r\n", "import parse from './parse.js';\r\nimport { unsafeStringify } from './stringify.js';\r\nexport default function v1ToV6(uuid) {\r\n    const v1Bytes = typeof uuid === 'string' ? parse(uuid) : uuid;\r\n    const v6Bytes = _v1ToV6(v1Bytes);\r\n    return typeof uuid === 'string' ? unsafeStringify(v6Bytes) : v6Bytes;\r\n}\r\nfunction _v1ToV6(v1Bytes) {\r\n    return Uint8Array.of(((v1Bytes[6] & 0x0f) << 4) | ((v1Bytes[7] >> 4) & 0x0f), ((v1Bytes[7] & 0x0f) << 4) | ((v1Bytes[4] & 0xf0) >> 4), ((v1Bytes[4] & 0x0f) << 4) | ((v1Bytes[5] & 0xf0) >> 4), ((v1Bytes[5] & 0x0f) << 4) | ((v1Bytes[0] & 0xf0) >> 4), ((v1Bytes[0] & 0x0f) << 4) | ((v1Bytes[1] & 0xf0) >> 4), ((v1Bytes[1] & 0x0f) << 4) | ((v1Bytes[2] & 0xf0) >> 4), 0x60 | (v1Bytes[2] & 0x0f), v1Bytes[3], v1Bytes[8], v1Bytes[9], v1Bytes[10], v1Bytes[11], v1Bytes[12], v1Bytes[13], v1Bytes[14], v1Bytes[15]);\r\n}\r\n", "function md5(bytes) {\r\n    const words = uint8ToUint32(bytes);\r\n    const md5Bytes = wordsToMd5(words, bytes.length * 8);\r\n    return uint32ToUint8(md5Bytes);\r\n}\r\nfunction uint32ToUint8(input) {\r\n    const bytes = new Uint8Array(input.length * 4);\r\n    for (let i = 0; i < input.length * 4; i++) {\r\n        bytes[i] = (input[i >> 2] >>> ((i % 4) * 8)) & 0xff;\r\n    }\r\n    return bytes;\r\n}\r\nfunction getOutputLength(inputLength8) {\r\n    return (((inputLength8 + 64) >>> 9) << 4) + 14 + 1;\r\n}\r\nfunction wordsToMd5(x, len) {\r\n    const xpad = new Uint32Array(getOutputLength(len)).fill(0);\r\n    xpad.set(x);\r\n    xpad[len >> 5] |= 0x80 << len % 32;\r\n    xpad[xpad.length - 1] = len;\r\n    x = xpad;\r\n    let a = 1732584193;\r\n    let b = -271733879;\r\n    let c = -1732584194;\r\n    let d = 271733878;\r\n    for (let i = 0; i < x.length; i += 16) {\r\n        const olda = a;\r\n        const oldb = b;\r\n        const oldc = c;\r\n        const oldd = d;\r\n        a = md5ff(a, b, c, d, x[i], 7, -680876936);\r\n        d = md5ff(d, a, b, c, x[i + 1], 12, -389564586);\r\n        c = md5ff(c, d, a, b, x[i + 2], 17, 606105819);\r\n        b = md5ff(b, c, d, a, x[i + 3], 22, -1044525330);\r\n        a = md5ff(a, b, c, d, x[i + 4], 7, -176418897);\r\n        d = md5ff(d, a, b, c, x[i + 5], 12, 1200080426);\r\n        c = md5ff(c, d, a, b, x[i + 6], 17, -1473231341);\r\n        b = md5ff(b, c, d, a, x[i + 7], 22, -45705983);\r\n        a = md5ff(a, b, c, d, x[i + 8], 7, 1770035416);\r\n        d = md5ff(d, a, b, c, x[i + 9], 12, -1958414417);\r\n        c = md5ff(c, d, a, b, x[i + 10], 17, -42063);\r\n        b = md5ff(b, c, d, a, x[i + 11], 22, -1990404162);\r\n        a = md5ff(a, b, c, d, x[i + 12], 7, 1804603682);\r\n        d = md5ff(d, a, b, c, x[i + 13], 12, -40341101);\r\n        c = md5ff(c, d, a, b, x[i + 14], 17, -1502002290);\r\n        b = md5ff(b, c, d, a, x[i + 15], 22, 1236535329);\r\n        a = md5gg(a, b, c, d, x[i + 1], 5, -165796510);\r\n        d = md5gg(d, a, b, c, x[i + 6], 9, -1069501632);\r\n        c = md5gg(c, d, a, b, x[i + 11], 14, 643717713);\r\n        b = md5gg(b, c, d, a, x[i], 20, -373897302);\r\n        a = md5gg(a, b, c, d, x[i + 5], 5, -701558691);\r\n        d = md5gg(d, a, b, c, x[i + 10], 9, 38016083);\r\n        c = md5gg(c, d, a, b, x[i + 15], 14, -660478335);\r\n        b = md5gg(b, c, d, a, x[i + 4], 20, -405537848);\r\n        a = md5gg(a, b, c, d, x[i + 9], 5, 568446438);\r\n        d = md5gg(d, a, b, c, x[i + 14], 9, -1019803690);\r\n        c = md5gg(c, d, a, b, x[i + 3], 14, -187363961);\r\n        b = md5gg(b, c, d, a, x[i + 8], 20, 1163531501);\r\n        a = md5gg(a, b, c, d, x[i + 13], 5, -1444681467);\r\n        d = md5gg(d, a, b, c, x[i + 2], 9, -51403784);\r\n        c = md5gg(c, d, a, b, x[i + 7], 14, 1735328473);\r\n        b = md5gg(b, c, d, a, x[i + 12], 20, -1926607734);\r\n        a = md5hh(a, b, c, d, x[i + 5], 4, -378558);\r\n        d = md5hh(d, a, b, c, x[i + 8], 11, -2022574463);\r\n        c = md5hh(c, d, a, b, x[i + 11], 16, 1839030562);\r\n        b = md5hh(b, c, d, a, x[i + 14], 23, -35309556);\r\n        a = md5hh(a, b, c, d, x[i + 1], 4, -1530992060);\r\n        d = md5hh(d, a, b, c, x[i + 4], 11, 1272893353);\r\n        c = md5hh(c, d, a, b, x[i + 7], 16, -155497632);\r\n        b = md5hh(b, c, d, a, x[i + 10], 23, -1094730640);\r\n        a = md5hh(a, b, c, d, x[i + 13], 4, 681279174);\r\n        d = md5hh(d, a, b, c, x[i], 11, -358537222);\r\n        c = md5hh(c, d, a, b, x[i + 3], 16, -722521979);\r\n        b = md5hh(b, c, d, a, x[i + 6], 23, 76029189);\r\n        a = md5hh(a, b, c, d, x[i + 9], 4, -640364487);\r\n        d = md5hh(d, a, b, c, x[i + 12], 11, -421815835);\r\n        c = md5hh(c, d, a, b, x[i + 15], 16, 530742520);\r\n        b = md5hh(b, c, d, a, x[i + 2], 23, -995338651);\r\n        a = md5ii(a, b, c, d, x[i], 6, -198630844);\r\n        d = md5ii(d, a, b, c, x[i + 7], 10, 1126891415);\r\n        c = md5ii(c, d, a, b, x[i + 14], 15, -1416354905);\r\n        b = md5ii(b, c, d, a, x[i + 5], 21, -57434055);\r\n        a = md5ii(a, b, c, d, x[i + 12], 6, 1700485571);\r\n        d = md5ii(d, a, b, c, x[i + 3], 10, -1894986606);\r\n        c = md5ii(c, d, a, b, x[i + 10], 15, -1051523);\r\n        b = md5ii(b, c, d, a, x[i + 1], 21, -2054922799);\r\n        a = md5ii(a, b, c, d, x[i + 8], 6, 1873313359);\r\n        d = md5ii(d, a, b, c, x[i + 15], 10, -30611744);\r\n        c = md5ii(c, d, a, b, x[i + 6], 15, -1560198380);\r\n        b = md5ii(b, c, d, a, x[i + 13], 21, 1309151649);\r\n        a = md5ii(a, b, c, d, x[i + 4], 6, -145523070);\r\n        d = md5ii(d, a, b, c, x[i + 11], 10, -1120210379);\r\n        c = md5ii(c, d, a, b, x[i + 2], 15, 718787259);\r\n        b = md5ii(b, c, d, a, x[i + 9], 21, -343485551);\r\n        a = safeAdd(a, olda);\r\n        b = safeAdd(b, oldb);\r\n        c = safeAdd(c, oldc);\r\n        d = safeAdd(d, oldd);\r\n    }\r\n    return Uint32Array.of(a, b, c, d);\r\n}\r\nfunction uint8ToUint32(input) {\r\n    if (input.length === 0) {\r\n        return new Uint32Array();\r\n    }\r\n    const output = new Uint32Array(getOutputLength(input.length * 8)).fill(0);\r\n    for (let i = 0; i < input.length; i++) {\r\n        output[i >> 2] |= (input[i] & 0xff) << ((i % 4) * 8);\r\n    }\r\n    return output;\r\n}\r\nfunction safeAdd(x, y) {\r\n    const lsw = (x & 0xffff) + (y & 0xffff);\r\n    const msw = (x >> 16) + (y >> 16) + (lsw >> 16);\r\n    return (msw << 16) | (lsw & 0xffff);\r\n}\r\nfunction bitRotateLeft(num, cnt) {\r\n    return (num << cnt) | (num >>> (32 - cnt));\r\n}\r\nfunction md5cmn(q, a, b, x, s, t) {\r\n    return safeAdd(bitRotateLeft(safeAdd(safeAdd(a, q), safeAdd(x, t)), s), b);\r\n}\r\nfunction md5ff(a, b, c, d, x, s, t) {\r\n    return md5cmn((b & c) | (~b & d), a, b, x, s, t);\r\n}\r\nfunction md5gg(a, b, c, d, x, s, t) {\r\n    return md5cmn((b & d) | (c & ~d), a, b, x, s, t);\r\n}\r\nfunction md5hh(a, b, c, d, x, s, t) {\r\n    return md5cmn(b ^ c ^ d, a, b, x, s, t);\r\n}\r\nfunction md5ii(a, b, c, d, x, s, t) {\r\n    return md5cmn(c ^ (b | ~d), a, b, x, s, t);\r\n}\r\nexport default md5;\r\n", "import parse from './parse.js';\r\nimport { unsafeStringify } from './stringify.js';\r\nexport function stringToBytes(str) {\r\n    str = unescape(encodeURIComponent(str));\r\n    const bytes = new Uint8Array(str.length);\r\n    for (let i = 0; i < str.length; ++i) {\r\n        bytes[i] = str.charCodeAt(i);\r\n    }\r\n    return bytes;\r\n}\r\nexport const DNS = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';\r\nexport const URL = '6ba7b811-9dad-11d1-80b4-00c04fd430c8';\r\nexport default function v35(version, hash, value, namespace, buf, offset) {\r\n    const valueBytes = typeof value === 'string' ? stringToBytes(value) : value;\r\n    const namespaceBytes = typeof namespace === 'string' ? parse(namespace) : namespace;\r\n    if (typeof namespace === 'string') {\r\n        namespace = parse(namespace);\r\n    }\r\n    if (namespace?.length !== 16) {\r\n        throw TypeError('Namespace must be array-like (16 iterable integer values, 0-255)');\r\n    }\r\n    let bytes = new Uint8Array(16 + valueBytes.length);\r\n    bytes.set(namespaceBytes);\r\n    bytes.set(valueBytes, namespaceBytes.length);\r\n    bytes = hash(bytes);\r\n    bytes[6] = (bytes[6] & 0x0f) | version;\r\n    bytes[8] = (bytes[8] & 0x3f) | 0x80;\r\n    if (buf) {\r\n        offset = offset || 0;\r\n        for (let i = 0; i < 16; ++i) {\r\n            buf[offset + i] = bytes[i];\r\n        }\r\n        return buf;\r\n    }\r\n    return unsafeStringify(bytes);\r\n}\r\n", "import md5 from './md5.js';\r\nimport v35, { DNS, URL } from './v35.js';\r\nexport { DNS, URL } from './v35.js';\r\nfunction v3(value, namespace, buf, offset) {\r\n    return v35(0x30, md5, value, namespace, buf, offset);\r\n}\r\nv3.DNS = DNS;\r\nv3.URL = URL;\r\nexport default v3;\r\n", "const randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\r\nexport default { randomUUID };\r\n", "import native from './native.js';\r\nimport rng from './rng.js';\r\nimport { unsafeStringify } from './stringify.js';\r\nfunction v4(options, buf, offset) {\r\n    if (native.randomUUID && !buf && !options) {\r\n        return native.randomUUID();\r\n    }\r\n    options = options || {};\r\n    const rnds = options.random ?? options.rng?.() ?? rng();\r\n    if (rnds.length < 16) {\r\n        throw new Error('Random bytes length must be >= 16');\r\n    }\r\n    rnds[6] = (rnds[6] & 0x0f) | 0x40;\r\n    rnds[8] = (rnds[8] & 0x3f) | 0x80;\r\n    if (buf) {\r\n        offset = offset || 0;\r\n        if (offset < 0 || offset + 16 > buf.length) {\r\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\r\n        }\r\n        for (let i = 0; i < 16; ++i) {\r\n            buf[offset + i] = rnds[i];\r\n        }\r\n        return buf;\r\n    }\r\n    return unsafeStringify(rnds);\r\n}\r\nexport default v4;\r\n", "function f(s, x, y, z) {\r\n    switch (s) {\r\n        case 0:\r\n            return (x & y) ^ (~x & z);\r\n        case 1:\r\n            return x ^ y ^ z;\r\n        case 2:\r\n            return (x & y) ^ (x & z) ^ (y & z);\r\n        case 3:\r\n            return x ^ y ^ z;\r\n    }\r\n}\r\nfunction ROTL(x, n) {\r\n    return (x << n) | (x >>> (32 - n));\r\n}\r\nfunction sha1(bytes) {\r\n    const K = [0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xca62c1d6];\r\n    const H = [0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0];\r\n    const newBytes = new Uint8Array(bytes.length + 1);\r\n    newBytes.set(bytes);\r\n    newBytes[bytes.length] = 0x80;\r\n    bytes = newBytes;\r\n    const l = bytes.length / 4 + 2;\r\n    const N = Math.ceil(l / 16);\r\n    const M = new Array(N);\r\n    for (let i = 0; i < N; ++i) {\r\n        const arr = new Uint32Array(16);\r\n        for (let j = 0; j < 16; ++j) {\r\n            arr[j] =\r\n                (bytes[i * 64 + j * 4] << 24) |\r\n                    (bytes[i * 64 + j * 4 + 1] << 16) |\r\n                    (bytes[i * 64 + j * 4 + 2] << 8) |\r\n                    bytes[i * 64 + j * 4 + 3];\r\n        }\r\n        M[i] = arr;\r\n    }\r\n    M[N - 1][14] = ((bytes.length - 1) * 8) / Math.pow(2, 32);\r\n    M[N - 1][14] = Math.floor(M[N - 1][14]);\r\n    M[N - 1][15] = ((bytes.length - 1) * 8) & 0xffffffff;\r\n    for (let i = 0; i < N; ++i) {\r\n        const W = new Uint32Array(80);\r\n        for (let t = 0; t < 16; ++t) {\r\n            W[t] = M[i][t];\r\n        }\r\n        for (let t = 16; t < 80; ++t) {\r\n            W[t] = ROTL(W[t - 3] ^ W[t - 8] ^ W[t - 14] ^ W[t - 16], 1);\r\n        }\r\n        let a = H[0];\r\n        let b = H[1];\r\n        let c = H[2];\r\n        let d = H[3];\r\n        let e = H[4];\r\n        for (let t = 0; t < 80; ++t) {\r\n            const s = Math.floor(t / 20);\r\n            const T = (ROTL(a, 5) + f(s, b, c, d) + e + K[s] + W[t]) >>> 0;\r\n            e = d;\r\n            d = c;\r\n            c = ROTL(b, 30) >>> 0;\r\n            b = a;\r\n            a = T;\r\n        }\r\n        H[0] = (H[0] + a) >>> 0;\r\n        H[1] = (H[1] + b) >>> 0;\r\n        H[2] = (H[2] + c) >>> 0;\r\n        H[3] = (H[3] + d) >>> 0;\r\n        H[4] = (H[4] + e) >>> 0;\r\n    }\r\n    return Uint8Array.of(H[0] >> 24, H[0] >> 16, H[0] >> 8, H[0], H[1] >> 24, H[1] >> 16, H[1] >> 8, H[1], H[2] >> 24, H[2] >> 16, H[2] >> 8, H[2], H[3] >> 24, H[3] >> 16, H[3] >> 8, H[3], H[4] >> 24, H[4] >> 16, H[4] >> 8, H[4]);\r\n}\r\nexport default sha1;\r\n", "import sha1 from './sha1.js';\r\nimport v35, { DNS, URL } from './v35.js';\r\nexport { DNS, URL } from './v35.js';\r\nfunction v5(value, namespace, buf, offset) {\r\n    return v35(0x50, sha1, value, namespace, buf, offset);\r\n}\r\nv5.DNS = DNS;\r\nv5.URL = URL;\r\nexport default v5;\r\n", "import { unsafeStringify } from './stringify.js';\r\nimport v1 from './v1.js';\r\nimport v1ToV6 from './v1ToV6.js';\r\nfunction v6(options, buf, offset) {\r\n    options ??= {};\r\n    offset ??= 0;\r\n    let bytes = v1({ ...options, _v6: true }, new Uint8Array(16));\r\n    bytes = v1ToV6(bytes);\r\n    if (buf) {\r\n        for (let i = 0; i < 16; i++) {\r\n            buf[offset + i] = bytes[i];\r\n        }\r\n        return buf;\r\n    }\r\n    return unsafeStringify(bytes);\r\n}\r\nexport default v6;\r\n", "import parse from './parse.js';\r\nimport { unsafeStringify } from './stringify.js';\r\nexport default function v6ToV1(uuid) {\r\n    const v6Bytes = typeof uuid === 'string' ? parse(uuid) : uuid;\r\n    const v1Bytes = _v6ToV1(v6Bytes);\r\n    return typeof uuid === 'string' ? unsafeStringify(v1Bytes) : v1Bytes;\r\n}\r\nfunction _v6ToV1(v6Bytes) {\r\n    return Uint8Array.of(((v6Bytes[3] & 0x0f) << 4) | ((v6Bytes[4] >> 4) & 0x0f), ((v6Bytes[4] & 0x0f) << 4) | ((v6Bytes[5] & 0xf0) >> 4), ((v6Bytes[5] & 0x0f) << 4) | (v6Bytes[6] & 0x0f), v6Bytes[7], ((v6Bytes[1] & 0x0f) << 4) | ((v6Bytes[2] & 0xf0) >> 4), ((v6Bytes[2] & 0x0f) << 4) | ((v6Bytes[3] & 0xf0) >> 4), 0x10 | ((v6Bytes[0] & 0xf0) >> 4), ((v6Bytes[0] & 0x0f) << 4) | ((v6Bytes[1] & 0xf0) >> 4), v6Bytes[8], v6Bytes[9], v6Bytes[10], v6Bytes[11], v6Bytes[12], v6Bytes[13], v6Bytes[14], v6Bytes[15]);\r\n}\r\n", "import rng from './rng.js';\r\nimport { unsafeStringify } from './stringify.js';\r\nconst _state = {};\r\nfunction v7(options, buf, offset) {\r\n    let bytes;\r\n    if (options) {\r\n        bytes = v7Bytes(options.random ?? options.rng?.() ?? rng(), options.msecs, options.seq, buf, offset);\r\n    }\r\n    else {\r\n        const now = Date.now();\r\n        const rnds = rng();\r\n        updateV7State(_state, now, rnds);\r\n        bytes = v7Bytes(rnds, _state.msecs, _state.seq, buf, offset);\r\n    }\r\n    return buf ?? unsafeStringify(bytes);\r\n}\r\nexport function updateV7State(state, now, rnds) {\r\n    state.msecs ??= -Infinity;\r\n    state.seq ??= 0;\r\n    if (now > state.msecs) {\r\n        state.seq = (rnds[6] << 23) | (rnds[7] << 16) | (rnds[8] << 8) | rnds[9];\r\n        state.msecs = now;\r\n    }\r\n    else {\r\n        state.seq = (state.seq + 1) | 0;\r\n        if (state.seq === 0) {\r\n            state.msecs++;\r\n        }\r\n    }\r\n    return state;\r\n}\r\nfunction v7Bytes(rnds, msecs, seq, buf, offset = 0) {\r\n    if (rnds.length < 16) {\r\n        throw new Error('Random bytes length must be >= 16');\r\n    }\r\n    if (!buf) {\r\n        buf = new Uint8Array(16);\r\n        offset = 0;\r\n    }\r\n    else {\r\n        if (offset < 0 || offset + 16 > buf.length) {\r\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\r\n        }\r\n    }\r\n    msecs ??= Date.now();\r\n    seq ??= ((rnds[6] * 0x7f) << 24) | (rnds[7] << 16) | (rnds[8] << 8) | rnds[9];\r\n    buf[offset++] = (msecs / 0x10000000000) & 0xff;\r\n    buf[offset++] = (msecs / 0x100000000) & 0xff;\r\n    buf[offset++] = (msecs / 0x1000000) & 0xff;\r\n    buf[offset++] = (msecs / 0x10000) & 0xff;\r\n    buf[offset++] = (msecs / 0x100) & 0xff;\r\n    buf[offset++] = msecs & 0xff;\r\n    buf[offset++] = 0x70 | ((seq >>> 28) & 0x0f);\r\n    buf[offset++] = (seq >>> 20) & 0xff;\r\n    buf[offset++] = 0x80 | ((seq >>> 14) & 0x3f);\r\n    buf[offset++] = (seq >>> 6) & 0xff;\r\n    buf[offset++] = ((seq << 2) & 0xff) | (rnds[10] & 0x03);\r\n    buf[offset++] = rnds[11];\r\n    buf[offset++] = rnds[12];\r\n    buf[offset++] = rnds[13];\r\n    buf[offset++] = rnds[14];\r\n    buf[offset++] = rnds[15];\r\n    return buf;\r\n}\r\nexport default v7;\r\n", "import validate from './validate.js';\r\nfunction version(uuid) {\r\n    if (!validate(uuid)) {\r\n        throw TypeError('Invalid UUID');\r\n    }\r\n    return parseInt(uuid.slice(14, 15), 16);\r\n}\r\nexport default version;\r\n"], "mappings": ";;;AAAA,IAAO,cAAQ;;;ACAf,IAAO,cAAQ;;;ACAf,IAAO,gBAAQ;;;ACCf,SAAS,SAAS,MAAM;AACpB,SAAO,OAAO,SAAS,YAAY,cAAM,KAAK,IAAI;AACtD;AACA,IAAO,mBAAQ;;;ACHf,SAAS,MAAM,MAAM;AACjB,MAAI,CAAC,iBAAS,IAAI,GAAG;AACjB,UAAM,UAAU,cAAc;AAAA,EAClC;AACA,MAAI;AACJ,SAAO,WAAW,IAAI,IAAI,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,EAAE,OAAO,IAAK,MAAM,KAAM,KAAO,MAAM,IAAK,KAAM,IAAI,MAAO,IAAI,SAAS,KAAK,MAAM,GAAG,EAAE,GAAG,EAAE,OAAO,GAAG,IAAI,MAAO,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,OAAO,GAAG,IAAI,MAAO,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,OAAO,GAAG,IAAI,MAAQ,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,KAAK,gBAAiB,KAAO,IAAI,aAAe,KAAO,MAAM,KAAM,KAAO,MAAM,KAAM,KAAO,MAAM,IAAK,KAAM,IAAI,GAAI;AACvb;AACA,IAAO,gBAAQ;;;ACPf,IAAM,YAAY,CAAC;AACnB,SAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC1B,YAAU,MAAM,IAAI,KAAO,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC;AACpD;AACO,SAAS,gBAAgB,KAAK,SAAS,GAAG;AAC7C,UAAQ,UAAU,IAAI,SAAS,CAAC,CAAC,IAC7B,UAAU,IAAI,SAAS,CAAC,CAAC,IACzB,UAAU,IAAI,SAAS,CAAC,CAAC,IACzB,UAAU,IAAI,SAAS,CAAC,CAAC,IACzB,MACA,UAAU,IAAI,SAAS,CAAC,CAAC,IACzB,UAAU,IAAI,SAAS,CAAC,CAAC,IACzB,MACA,UAAU,IAAI,SAAS,CAAC,CAAC,IACzB,UAAU,IAAI,SAAS,CAAC,CAAC,IACzB,MACA,UAAU,IAAI,SAAS,CAAC,CAAC,IACzB,UAAU,IAAI,SAAS,CAAC,CAAC,IACzB,MACA,UAAU,IAAI,SAAS,EAAE,CAAC,IAC1B,UAAU,IAAI,SAAS,EAAE,CAAC,IAC1B,UAAU,IAAI,SAAS,EAAE,CAAC,IAC1B,UAAU,IAAI,SAAS,EAAE,CAAC,IAC1B,UAAU,IAAI,SAAS,EAAE,CAAC,IAC1B,UAAU,IAAI,SAAS,EAAE,CAAC,GAAG,YAAY;AACjD;AACA,SAAS,UAAU,KAAK,SAAS,GAAG;AAChC,QAAM,OAAO,gBAAgB,KAAK,MAAM;AACxC,MAAI,CAAC,iBAAS,IAAI,GAAG;AACjB,UAAM,UAAU,6BAA6B;AAAA,EACjD;AACA,SAAO;AACX;AACA,IAAO,oBAAQ;;;AClCf,IAAI;AACJ,IAAM,QAAQ,IAAI,WAAW,EAAE;AAChB,SAAR,MAAuB;AAC1B,MAAI,CAAC,iBAAiB;AAClB,QAAI,OAAO,WAAW,eAAe,CAAC,OAAO,iBAAiB;AAC1D,YAAM,IAAI,MAAM,0GAA0G;AAAA,IAC9H;AACA,sBAAkB,OAAO,gBAAgB,KAAK,MAAM;AAAA,EACxD;AACA,SAAO,gBAAgB,KAAK;AAChC;;;ACRA,IAAM,SAAS,CAAC;AAChB,SAAS,GAAG,SAAS,KAAK,QAAQ;AAHlC;AAII,MAAI;AACJ,QAAM,QAAO,mCAAS,QAAO;AAC7B,MAAI,SAAS;AACT,UAAM,cAAc,OAAO,KAAK,OAAO;AACvC,QAAI,YAAY,WAAW,KAAK,YAAY,CAAC,MAAM,OAAO;AACtD,gBAAU;AAAA,IACd;AAAA,EACJ;AACA,MAAI,SAAS;AACT,YAAQ,QAAQ,QAAQ,YAAU,aAAQ,QAAR,qCAAmB,IAAI,GAAG,QAAQ,OAAO,QAAQ,OAAO,QAAQ,UAAU,QAAQ,MAAM,KAAK,MAAM;AAAA,EACzI,OACK;AACD,UAAM,MAAM,KAAK,IAAI;AACrB,UAAM,OAAO,IAAI;AACjB,kBAAc,QAAQ,KAAK,IAAI;AAC/B,YAAQ,QAAQ,MAAM,OAAO,OAAO,OAAO,OAAO,OAAO,SAAY,OAAO,UAAU,OAAO,SAAY,OAAO,MAAM,KAAK,MAAM;AAAA,EACrI;AACA,SAAO,OAAO,gBAAgB,KAAK;AACvC;AACO,SAAS,cAAc,OAAO,KAAK,MAAM;AAC5C,QAAM,UAAN,MAAM,QAAU;AAChB,QAAM,UAAN,MAAM,QAAU;AAChB,MAAI,QAAQ,MAAM,OAAO;AACrB,UAAM;AACN,QAAI,MAAM,SAAS,KAAO;AACtB,YAAM,OAAO;AACb,YAAM,QAAQ;AAAA,IAClB;AAAA,EACJ,WACS,MAAM,MAAM,OAAO;AACxB,UAAM,QAAQ;AAAA,EAClB,WACS,MAAM,MAAM,OAAO;AACxB,UAAM,OAAO;AAAA,EACjB;AACA,MAAI,CAAC,MAAM,MAAM;AACb,UAAM,OAAO,KAAK,MAAM,IAAI,EAAE;AAC9B,UAAM,KAAK,CAAC,KAAK;AACjB,UAAM,YAAa,KAAK,CAAC,KAAK,IAAK,KAAK,CAAC,KAAK;AAAA,EAClD;AACA,QAAM,QAAQ;AACd,SAAO;AACX;AACA,SAAS,QAAQ,MAAM,OAAO,OAAO,UAAU,MAAM,KAAK,SAAS,GAAG;AAClE,MAAI,KAAK,SAAS,IAAI;AAClB,UAAM,IAAI,MAAM,mCAAmC;AAAA,EACvD;AACA,MAAI,CAAC,KAAK;AACN,UAAM,IAAI,WAAW,EAAE;AACvB,aAAS;AAAA,EACb,OACK;AACD,QAAI,SAAS,KAAK,SAAS,KAAK,IAAI,QAAQ;AACxC,YAAM,IAAI,WAAW,mBAAmB,MAAM,IAAI,SAAS,EAAE,0BAA0B;AAAA,IAC3F;AAAA,EACJ;AACA,oBAAU,KAAK,IAAI;AACnB,oBAAU;AACV,2BAAe,KAAK,CAAC,KAAK,IAAK,KAAK,CAAC,KAAK;AAC1C,kBAAS,KAAK,MAAM,IAAI,EAAE;AAC1B,WAAS;AACT,QAAM,OAAO,QAAQ,aAAa,MAAQ,SAAS;AACnD,MAAI,QAAQ,IAAK,OAAO,KAAM;AAC9B,MAAI,QAAQ,IAAK,OAAO,KAAM;AAC9B,MAAI,QAAQ,IAAK,OAAO,IAAK;AAC7B,MAAI,QAAQ,IAAI,KAAK;AACrB,QAAM,MAAQ,QAAQ,aAAe,MAAS;AAC9C,MAAI,QAAQ,IAAK,QAAQ,IAAK;AAC9B,MAAI,QAAQ,IAAI,MAAM;AACtB,MAAI,QAAQ,IAAM,QAAQ,KAAM,KAAO;AACvC,MAAI,QAAQ,IAAK,QAAQ,KAAM;AAC/B,MAAI,QAAQ,IAAK,aAAa,IAAK;AACnC,MAAI,QAAQ,IAAI,WAAW;AAC3B,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACxB,QAAI,QAAQ,IAAI,KAAK,CAAC;AAAA,EAC1B;AACA,SAAO;AACX;AACA,IAAO,aAAQ;;;AChFA,SAAR,OAAwB,MAAM;AACjC,QAAMA,WAAU,OAAO,SAAS,WAAW,cAAM,IAAI,IAAI;AACzD,QAAM,UAAU,QAAQA,QAAO;AAC/B,SAAO,OAAO,SAAS,WAAW,gBAAgB,OAAO,IAAI;AACjE;AACA,SAAS,QAAQA,UAAS;AACtB,SAAO,WAAW,IAAKA,SAAQ,CAAC,IAAI,OAAS,IAAOA,SAAQ,CAAC,KAAK,IAAK,KAASA,SAAQ,CAAC,IAAI,OAAS,KAAOA,SAAQ,CAAC,IAAI,QAAS,IAAMA,SAAQ,CAAC,IAAI,OAAS,KAAOA,SAAQ,CAAC,IAAI,QAAS,IAAMA,SAAQ,CAAC,IAAI,OAAS,KAAOA,SAAQ,CAAC,IAAI,QAAS,IAAMA,SAAQ,CAAC,IAAI,OAAS,KAAOA,SAAQ,CAAC,IAAI,QAAS,IAAMA,SAAQ,CAAC,IAAI,OAAS,KAAOA,SAAQ,CAAC,IAAI,QAAS,GAAI,KAAQA,SAAQ,CAAC,IAAI,IAAOA,SAAQ,CAAC,GAAGA,SAAQ,CAAC,GAAGA,SAAQ,CAAC,GAAGA,SAAQ,EAAE,GAAGA,SAAQ,EAAE,GAAGA,SAAQ,EAAE,GAAGA,SAAQ,EAAE,GAAGA,SAAQ,EAAE,GAAGA,SAAQ,EAAE,CAAC;AAC3f;;;ACTA,SAAS,IAAI,OAAO;AAChB,QAAM,QAAQ,cAAc,KAAK;AACjC,QAAM,WAAW,WAAW,OAAO,MAAM,SAAS,CAAC;AACnD,SAAO,cAAc,QAAQ;AACjC;AACA,SAAS,cAAc,OAAO;AAC1B,QAAM,QAAQ,IAAI,WAAW,MAAM,SAAS,CAAC;AAC7C,WAAS,IAAI,GAAG,IAAI,MAAM,SAAS,GAAG,KAAK;AACvC,UAAM,CAAC,IAAK,MAAM,KAAK,CAAC,MAAQ,IAAI,IAAK,IAAM;AAAA,EACnD;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,cAAc;AACnC,UAAU,eAAe,OAAQ,KAAM,KAAK,KAAK;AACrD;AACA,SAAS,WAAW,GAAG,KAAK;AACxB,QAAM,OAAO,IAAI,YAAY,gBAAgB,GAAG,CAAC,EAAE,KAAK,CAAC;AACzD,OAAK,IAAI,CAAC;AACV,OAAK,OAAO,CAAC,KAAK,OAAQ,MAAM;AAChC,OAAK,KAAK,SAAS,CAAC,IAAI;AACxB,MAAI;AACJ,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,IAAI;AACnC,UAAM,OAAO;AACb,UAAM,OAAO;AACb,UAAM,OAAO;AACb,UAAM,OAAO;AACb,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,UAAU;AACzC,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,MAAM;AAC3C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,WAAW;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,UAAU;AAC1C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,QAAQ;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,SAAS;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,SAAS;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,OAAO;AAC1C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,WAAW;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,UAAU;AAC1C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,QAAQ;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,UAAU;AACzC,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,QAAQ;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,QAAQ,GAAG,IAAI;AACnB,QAAI,QAAQ,GAAG,IAAI;AACnB,QAAI,QAAQ,GAAG,IAAI;AACnB,QAAI,QAAQ,GAAG,IAAI;AAAA,EACvB;AACA,SAAO,YAAY,GAAG,GAAG,GAAG,GAAG,CAAC;AACpC;AACA,SAAS,cAAc,OAAO;AAC1B,MAAI,MAAM,WAAW,GAAG;AACpB,WAAO,IAAI,YAAY;AAAA,EAC3B;AACA,QAAM,SAAS,IAAI,YAAY,gBAAgB,MAAM,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC;AACxE,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,WAAO,KAAK,CAAC,MAAM,MAAM,CAAC,IAAI,QAAW,IAAI,IAAK;AAAA,EACtD;AACA,SAAO;AACX;AACA,SAAS,QAAQ,GAAG,GAAG;AACnB,QAAM,OAAO,IAAI,UAAW,IAAI;AAChC,QAAM,OAAO,KAAK,OAAO,KAAK,OAAO,OAAO;AAC5C,SAAQ,OAAO,KAAO,MAAM;AAChC;AACA,SAAS,cAAc,KAAK,KAAK;AAC7B,SAAQ,OAAO,MAAQ,QAAS,KAAK;AACzC;AACA,SAAS,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAC9B,SAAO,QAAQ,cAAc,QAAQ,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AAC7E;AACA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAChC,SAAO,OAAQ,IAAI,IAAM,CAAC,IAAI,GAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AACnD;AACA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAChC,SAAO,OAAQ,IAAI,IAAM,IAAI,CAAC,GAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AACnD;AACA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAChC,SAAO,OAAO,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC1C;AACA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAChC,SAAO,OAAO,KAAK,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AAC7C;AACA,IAAO,cAAQ;;;ACpIR,SAAS,cAAc,KAAK;AAC/B,QAAM,SAAS,mBAAmB,GAAG,CAAC;AACtC,QAAM,QAAQ,IAAI,WAAW,IAAI,MAAM;AACvC,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACjC,UAAM,CAAC,IAAI,IAAI,WAAW,CAAC;AAAA,EAC/B;AACA,SAAO;AACX;AACO,IAAM,MAAM;AACZ,IAAM,MAAM;AACJ,SAAR,IAAqBC,UAAS,MAAM,OAAO,WAAW,KAAK,QAAQ;AACtE,QAAM,aAAa,OAAO,UAAU,WAAW,cAAc,KAAK,IAAI;AACtE,QAAM,iBAAiB,OAAO,cAAc,WAAW,cAAM,SAAS,IAAI;AAC1E,MAAI,OAAO,cAAc,UAAU;AAC/B,gBAAY,cAAM,SAAS;AAAA,EAC/B;AACA,OAAI,uCAAW,YAAW,IAAI;AAC1B,UAAM,UAAU,kEAAkE;AAAA,EACtF;AACA,MAAI,QAAQ,IAAI,WAAW,KAAK,WAAW,MAAM;AACjD,QAAM,IAAI,cAAc;AACxB,QAAM,IAAI,YAAY,eAAe,MAAM;AAC3C,UAAQ,KAAK,KAAK;AAClB,QAAM,CAAC,IAAK,MAAM,CAAC,IAAI,KAAQA;AAC/B,QAAM,CAAC,IAAK,MAAM,CAAC,IAAI,KAAQ;AAC/B,MAAI,KAAK;AACL,aAAS,UAAU;AACnB,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACzB,UAAI,SAAS,CAAC,IAAI,MAAM,CAAC;AAAA,IAC7B;AACA,WAAO;AAAA,EACX;AACA,SAAO,gBAAgB,KAAK;AAChC;;;AChCA,SAAS,GAAG,OAAO,WAAW,KAAK,QAAQ;AACvC,SAAO,IAAI,IAAM,aAAK,OAAO,WAAW,KAAK,MAAM;AACvD;AACA,GAAG,MAAM;AACT,GAAG,MAAM;AACT,IAAO,aAAQ;;;ACRf,IAAM,aAAa,OAAO,WAAW,eAAe,OAAO,cAAc,OAAO,WAAW,KAAK,MAAM;AACtG,IAAO,iBAAQ,EAAE,WAAW;;;ACE5B,SAAS,GAAG,SAAS,KAAK,QAAQ;AAHlC;AAII,MAAI,eAAO,cAAc,CAAC,OAAO,CAAC,SAAS;AACvC,WAAO,eAAO,WAAW;AAAA,EAC7B;AACA,YAAU,WAAW,CAAC;AACtB,QAAM,OAAO,QAAQ,YAAU,aAAQ,QAAR,qCAAmB,IAAI;AACtD,MAAI,KAAK,SAAS,IAAI;AAClB,UAAM,IAAI,MAAM,mCAAmC;AAAA,EACvD;AACA,OAAK,CAAC,IAAK,KAAK,CAAC,IAAI,KAAQ;AAC7B,OAAK,CAAC,IAAK,KAAK,CAAC,IAAI,KAAQ;AAC7B,MAAI,KAAK;AACL,aAAS,UAAU;AACnB,QAAI,SAAS,KAAK,SAAS,KAAK,IAAI,QAAQ;AACxC,YAAM,IAAI,WAAW,mBAAmB,MAAM,IAAI,SAAS,EAAE,0BAA0B;AAAA,IAC3F;AACA,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACzB,UAAI,SAAS,CAAC,IAAI,KAAK,CAAC;AAAA,IAC5B;AACA,WAAO;AAAA,EACX;AACA,SAAO,gBAAgB,IAAI;AAC/B;AACA,IAAO,aAAQ;;;AC1Bf,SAAS,EAAE,GAAG,GAAG,GAAG,GAAG;AACnB,UAAQ,GAAG;AAAA,IACP,KAAK;AACD,aAAQ,IAAI,IAAM,CAAC,IAAI;AAAA,IAC3B,KAAK;AACD,aAAO,IAAI,IAAI;AAAA,IACnB,KAAK;AACD,aAAQ,IAAI,IAAM,IAAI,IAAM,IAAI;AAAA,IACpC,KAAK;AACD,aAAO,IAAI,IAAI;AAAA,EACvB;AACJ;AACA,SAAS,KAAK,GAAG,GAAG;AAChB,SAAQ,KAAK,IAAM,MAAO,KAAK;AACnC;AACA,SAAS,KAAK,OAAO;AACjB,QAAM,IAAI,CAAC,YAAY,YAAY,YAAY,UAAU;AACzD,QAAM,IAAI,CAAC,YAAY,YAAY,YAAY,WAAY,UAAU;AACrE,QAAM,WAAW,IAAI,WAAW,MAAM,SAAS,CAAC;AAChD,WAAS,IAAI,KAAK;AAClB,WAAS,MAAM,MAAM,IAAI;AACzB,UAAQ;AACR,QAAM,IAAI,MAAM,SAAS,IAAI;AAC7B,QAAM,IAAI,KAAK,KAAK,IAAI,EAAE;AAC1B,QAAM,IAAI,IAAI,MAAM,CAAC;AACrB,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACxB,UAAM,MAAM,IAAI,YAAY,EAAE;AAC9B,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACzB,UAAI,CAAC,IACA,MAAM,IAAI,KAAK,IAAI,CAAC,KAAK,KACrB,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,KAC7B,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,IAC9B,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC;AAAA,IACpC;AACA,MAAE,CAAC,IAAI;AAAA,EACX;AACA,IAAE,IAAI,CAAC,EAAE,EAAE,KAAM,MAAM,SAAS,KAAK,IAAK,KAAK,IAAI,GAAG,EAAE;AACxD,IAAE,IAAI,CAAC,EAAE,EAAE,IAAI,KAAK,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;AACtC,IAAE,IAAI,CAAC,EAAE,EAAE,KAAM,MAAM,SAAS,KAAK,IAAK;AAC1C,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACxB,UAAM,IAAI,IAAI,YAAY,EAAE;AAC5B,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACzB,QAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAAA,IACjB;AACA,aAAS,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AAC1B,QAAE,CAAC,IAAI,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;AAAA,IAC9D;AACA,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACzB,YAAM,IAAI,KAAK,MAAM,IAAI,EAAE;AAC3B,YAAM,IAAK,KAAK,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,MAAO;AAC7D,UAAI;AACJ,UAAI;AACJ,UAAI,KAAK,GAAG,EAAE,MAAM;AACpB,UAAI;AACJ,UAAI;AAAA,IACR;AACA,MAAE,CAAC,IAAK,EAAE,CAAC,IAAI,MAAO;AACtB,MAAE,CAAC,IAAK,EAAE,CAAC,IAAI,MAAO;AACtB,MAAE,CAAC,IAAK,EAAE,CAAC,IAAI,MAAO;AACtB,MAAE,CAAC,IAAK,EAAE,CAAC,IAAI,MAAO;AACtB,MAAE,CAAC,IAAK,EAAE,CAAC,IAAI,MAAO;AAAA,EAC1B;AACA,SAAO,WAAW,GAAG,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;AACpO;AACA,IAAO,eAAQ;;;AClEf,SAAS,GAAG,OAAO,WAAW,KAAK,QAAQ;AACvC,SAAO,IAAI,IAAM,cAAM,OAAO,WAAW,KAAK,MAAM;AACxD;AACA,GAAG,MAAM;AACT,GAAG,MAAM;AACT,IAAO,aAAQ;;;ACLf,SAAS,GAAG,SAAS,KAAK,QAAQ;AAC9B,wBAAY,CAAC;AACb,sBAAW;AACX,MAAI,QAAQ,WAAG,EAAE,GAAG,SAAS,KAAK,KAAK,GAAG,IAAI,WAAW,EAAE,CAAC;AAC5D,UAAQ,OAAO,KAAK;AACpB,MAAI,KAAK;AACL,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,UAAI,SAAS,CAAC,IAAI,MAAM,CAAC;AAAA,IAC7B;AACA,WAAO;AAAA,EACX;AACA,SAAO,gBAAgB,KAAK;AAChC;AACA,IAAO,aAAQ;;;ACdA,SAAR,OAAwB,MAAM;AACjC,QAAM,UAAU,OAAO,SAAS,WAAW,cAAM,IAAI,IAAI;AACzD,QAAMC,WAAU,QAAQ,OAAO;AAC/B,SAAO,OAAO,SAAS,WAAW,gBAAgBA,QAAO,IAAIA;AACjE;AACA,SAAS,QAAQ,SAAS;AACtB,SAAO,WAAW,IAAK,QAAQ,CAAC,IAAI,OAAS,IAAO,QAAQ,CAAC,KAAK,IAAK,KAAS,QAAQ,CAAC,IAAI,OAAS,KAAO,QAAQ,CAAC,IAAI,QAAS,IAAM,QAAQ,CAAC,IAAI,OAAS,IAAM,QAAQ,CAAC,IAAI,IAAO,QAAQ,CAAC,IAAK,QAAQ,CAAC,IAAI,OAAS,KAAO,QAAQ,CAAC,IAAI,QAAS,IAAM,QAAQ,CAAC,IAAI,OAAS,KAAO,QAAQ,CAAC,IAAI,QAAS,GAAI,MAAS,QAAQ,CAAC,IAAI,QAAS,IAAM,QAAQ,CAAC,IAAI,OAAS,KAAO,QAAQ,CAAC,IAAI,QAAS,GAAI,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,EAAE,GAAG,QAAQ,EAAE,GAAG,QAAQ,EAAE,GAAG,QAAQ,EAAE,GAAG,QAAQ,EAAE,GAAG,QAAQ,EAAE,CAAC;AAC3f;;;ACPA,IAAMC,UAAS,CAAC;AAChB,SAAS,GAAG,SAAS,KAAK,QAAQ;AAHlC;AAII,MAAI;AACJ,MAAI,SAAS;AACT,YAAQ,QAAQ,QAAQ,YAAU,aAAQ,QAAR,qCAAmB,IAAI,GAAG,QAAQ,OAAO,QAAQ,KAAK,KAAK,MAAM;AAAA,EACvG,OACK;AACD,UAAM,MAAM,KAAK,IAAI;AACrB,UAAM,OAAO,IAAI;AACjB,kBAAcA,SAAQ,KAAK,IAAI;AAC/B,YAAQ,QAAQ,MAAMA,QAAO,OAAOA,QAAO,KAAK,KAAK,MAAM;AAAA,EAC/D;AACA,SAAO,OAAO,gBAAgB,KAAK;AACvC;AACO,SAAS,cAAc,OAAO,KAAK,MAAM;AAC5C,QAAM,UAAN,MAAM,QAAU;AAChB,QAAM,QAAN,MAAM,MAAQ;AACd,MAAI,MAAM,MAAM,OAAO;AACnB,UAAM,MAAO,KAAK,CAAC,KAAK,KAAO,KAAK,CAAC,KAAK,KAAO,KAAK,CAAC,KAAK,IAAK,KAAK,CAAC;AACvE,UAAM,QAAQ;AAAA,EAClB,OACK;AACD,UAAM,MAAO,MAAM,MAAM,IAAK;AAC9B,QAAI,MAAM,QAAQ,GAAG;AACjB,YAAM;AAAA,IACV;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,QAAQ,MAAM,OAAO,KAAK,KAAK,SAAS,GAAG;AAChD,MAAI,KAAK,SAAS,IAAI;AAClB,UAAM,IAAI,MAAM,mCAAmC;AAAA,EACvD;AACA,MAAI,CAAC,KAAK;AACN,UAAM,IAAI,WAAW,EAAE;AACvB,aAAS;AAAA,EACb,OACK;AACD,QAAI,SAAS,KAAK,SAAS,KAAK,IAAI,QAAQ;AACxC,YAAM,IAAI,WAAW,mBAAmB,MAAM,IAAI,SAAS,EAAE,0BAA0B;AAAA,IAC3F;AAAA,EACJ;AACA,oBAAU,KAAK,IAAI;AACnB,gBAAU,KAAK,CAAC,IAAI,OAAS,KAAO,KAAK,CAAC,KAAK,KAAO,KAAK,CAAC,KAAK,IAAK,KAAK,CAAC;AAC5E,MAAI,QAAQ,IAAK,QAAQ,gBAAiB;AAC1C,MAAI,QAAQ,IAAK,QAAQ,aAAe;AACxC,MAAI,QAAQ,IAAK,QAAQ,WAAa;AACtC,MAAI,QAAQ,IAAK,QAAQ,QAAW;AACpC,MAAI,QAAQ,IAAK,QAAQ,MAAS;AAClC,MAAI,QAAQ,IAAI,QAAQ;AACxB,MAAI,QAAQ,IAAI,MAAS,QAAQ,KAAM;AACvC,MAAI,QAAQ,IAAK,QAAQ,KAAM;AAC/B,MAAI,QAAQ,IAAI,MAAS,QAAQ,KAAM;AACvC,MAAI,QAAQ,IAAK,QAAQ,IAAK;AAC9B,MAAI,QAAQ,IAAM,OAAO,IAAK,MAAS,KAAK,EAAE,IAAI;AAClD,MAAI,QAAQ,IAAI,KAAK,EAAE;AACvB,MAAI,QAAQ,IAAI,KAAK,EAAE;AACvB,MAAI,QAAQ,IAAI,KAAK,EAAE;AACvB,MAAI,QAAQ,IAAI,KAAK,EAAE;AACvB,MAAI,QAAQ,IAAI,KAAK,EAAE;AACvB,SAAO;AACX;AACA,IAAO,aAAQ;;;AC/Df,SAAS,QAAQ,MAAM;AACnB,MAAI,CAAC,iBAAS,IAAI,GAAG;AACjB,UAAM,UAAU,cAAc;AAAA,EAClC;AACA,SAAO,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE;AAC1C;AACA,IAAO,kBAAQ;", "names": ["v1Bytes", "version", "v1Bytes", "_state"]}