{"version": 3, "sources": ["../../react-dom/client.js"], "sourcesContent": ["'use strict';\r\n\r\nvar m = require('react-dom');\r\nif (process.env.NODE_ENV === 'production') {\r\n  exports.createRoot = m.createRoot;\r\n  exports.hydrateRoot = m.hydrateRoot;\r\n} else {\r\n  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\r\n  exports.createRoot = function(c, o) {\r\n    i.usingClientEntryPoint = true;\r\n    try {\r\n      return m.createRoot(c, o);\r\n    } finally {\r\n      i.usingClientEntryPoint = false;\r\n    }\r\n  };\r\n  exports.hydrateRoot = function(c, h, o) {\r\n    i.usingClientEntryPoint = true;\r\n    try {\r\n      return m.hydrateRoot(c, h, o);\r\n    } finally {\r\n      i.usingClientEntryPoint = false;\r\n    }\r\n  };\r\n}\r\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AAEA,QAAI,IAAI;AACR,QAAI,OAAuC;AACzC,cAAQ,aAAa,EAAE;AACvB,cAAQ,cAAc,EAAE;AAAA,IAC1B,OAAO;AACD,UAAI,EAAE;AACV,cAAQ,aAAa,SAAS,GAAG,GAAG;AAClC,UAAE,wBAAwB;AAC1B,YAAI;AACF,iBAAO,EAAE,WAAW,GAAG,CAAC;AAAA,QAC1B,UAAE;AACA,YAAE,wBAAwB;AAAA,QAC5B;AAAA,MACF;AACA,cAAQ,cAAc,SAAS,GAAG,GAAG,GAAG;AACtC,UAAE,wBAAwB;AAC1B,YAAI;AACF,iBAAO,EAAE,YAAY,GAAG,GAAG,CAAC;AAAA,QAC9B,UAAE;AACA,YAAE,wBAAwB;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AAjBM;AAAA;AAAA;", "names": []}