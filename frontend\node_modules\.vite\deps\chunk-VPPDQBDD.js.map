{"version": 3, "sources": ["../../@babel/runtime/helpers/esm/typeof.js"], "sourcesContent": ["function _typeof(o) {\r\n  \"@babel/helpers - typeof\";\r\n\r\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\r\n    return typeof o;\r\n  } : function (o) {\r\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\r\n  }, _typeof(o);\r\n}\r\nexport { _typeof as default };"], "mappings": ";;;;;AAAA,SAAS,QAAQ,GAAG;AAClB;AAEA,SAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUA,IAAG;AAChG,WAAO,OAAOA;AAAA,EAChB,IAAI,SAAUA,IAAG;AACf,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EACpH,GAAG,QAAQ,CAAC;AACd;AARA;AAAA;AAAA;AAAA;", "names": ["o"]}