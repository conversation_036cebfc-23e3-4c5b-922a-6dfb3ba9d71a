{"version": 3, "sources": ["../../gsap/utils/paths.js", "../../gsap/utils/matrix.js", "../../gsap/MotionPathPlugin.js"], "sourcesContent": ["/*!\r\n * paths 3.13.0\r\n * https://gsap.com\r\n *\r\n * Copyright 2008-2025, GreenSock. All rights reserved.\r\n * Subject to the terms at https://gsap.com/standard-license\r\n * @author: <PERSON>, <EMAIL>\r\n*/\r\n\r\n/* eslint-disable */\r\nvar _svgPathExp = /[achlmqstvz]|(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\r\n    _numbersExp = /(?:(-)?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\r\n    _scientific = /[\\+\\-]?\\d*\\.?\\d+e[\\+\\-]?\\d+/ig,\r\n    _selectorExp = /(^[#\\.][a-z]|[a-y][a-z])/i,\r\n    _DEG2RAD = Math.PI / 180,\r\n    _RAD2DEG = 180 / Math.PI,\r\n    _sin = Math.sin,\r\n    _cos = Math.cos,\r\n    _abs = Math.abs,\r\n    _sqrt = Math.sqrt,\r\n    _atan2 = Math.atan2,\r\n    _largeNum = 1e8,\r\n    _isString = function _isString(value) {\r\n  return typeof value === \"string\";\r\n},\r\n    _isNumber = function _isNumber(value) {\r\n  return typeof value === \"number\";\r\n},\r\n    _isUndefined = function _isUndefined(value) {\r\n  return typeof value === \"undefined\";\r\n},\r\n    _temp = {},\r\n    _temp2 = {},\r\n    _roundingNum = 1e5,\r\n    _wrapProgress = function _wrapProgress(progress) {\r\n  return Math.round((progress + _largeNum) % 1 * _roundingNum) / _roundingNum || (progress < 0 ? 0 : 1);\r\n},\r\n    //if progress lands on 1, the % will make it 0 which is why we || 1, but not if it's negative because it makes more sense for motion to end at 0 in that case.\r\n_round = function _round(value) {\r\n  return Math.round(value * _roundingNum) / _roundingNum || 0;\r\n},\r\n    _roundPrecise = function _roundPrecise(value) {\r\n  return Math.round(value * 1e10) / 1e10 || 0;\r\n},\r\n    _splitSegment = function _splitSegment(rawPath, segIndex, i, t) {\r\n  var segment = rawPath[segIndex],\r\n      shift = t === 1 ? 6 : subdivideSegment(segment, i, t);\r\n\r\n  if ((shift || !t) && shift + i + 2 < segment.length) {\r\n    rawPath.splice(segIndex, 0, segment.slice(0, i + shift + 2));\r\n    segment.splice(0, i + shift);\r\n    return 1;\r\n  }\r\n},\r\n    _getSampleIndex = function _getSampleIndex(samples, length, progress) {\r\n  // slightly slower way than doing this (when there's no lookup): segment.lookup[progress < 1 ? ~~(length / segment.minLength) : segment.lookup.length - 1] || 0;\r\n  var l = samples.length,\r\n      i = ~~(progress * l);\r\n\r\n  if (samples[i] > length) {\r\n    while (--i && samples[i] > length) {}\r\n\r\n    i < 0 && (i = 0);\r\n  } else {\r\n    while (samples[++i] < length && i < l) {}\r\n  }\r\n\r\n  return i < l ? i : l - 1;\r\n},\r\n    _reverseRawPath = function _reverseRawPath(rawPath, skipOuter) {\r\n  var i = rawPath.length;\r\n  skipOuter || rawPath.reverse();\r\n\r\n  while (i--) {\r\n    rawPath[i].reversed || reverseSegment(rawPath[i]);\r\n  }\r\n},\r\n    _copyMetaData = function _copyMetaData(source, copy) {\r\n  copy.totalLength = source.totalLength;\r\n\r\n  if (source.samples) {\r\n    //segment\r\n    copy.samples = source.samples.slice(0);\r\n    copy.lookup = source.lookup.slice(0);\r\n    copy.minLength = source.minLength;\r\n    copy.resolution = source.resolution;\r\n  } else if (source.totalPoints) {\r\n    //rawPath\r\n    copy.totalPoints = source.totalPoints;\r\n  }\r\n\r\n  return copy;\r\n},\r\n    //pushes a new segment into a rawPath, but if its starting values match the ending values of the last segment, it'll merge it into that same segment (to reduce the number of segments)\r\n_appendOrMerge = function _appendOrMerge(rawPath, segment) {\r\n  var index = rawPath.length,\r\n      prevSeg = rawPath[index - 1] || [],\r\n      l = prevSeg.length;\r\n\r\n  if (index && segment[0] === prevSeg[l - 2] && segment[1] === prevSeg[l - 1]) {\r\n    segment = prevSeg.concat(segment.slice(2));\r\n    index--;\r\n  }\r\n\r\n  rawPath[index] = segment;\r\n},\r\n    _bestDistance;\r\n/* TERMINOLOGY\r\n - RawPath - an array of arrays, one for each Segment. A single RawPath could have multiple \"M\" commands, defining Segments (paths aren't always connected).\r\n - Segment - an array containing a sequence of Cubic Bezier coordinates in alternating x, y, x, y format. Starting anchor, then control point 1, control point 2, and ending anchor, then the next control point 1, control point 2, anchor, etc. Uses less memory than an array with a bunch of {x, y} points.\r\n - Bezier - a single cubic Bezier with a starting anchor, two control points, and an ending anchor.\r\n - the variable \"t\" is typically the position along an individual Bezier path (time) and it's NOT linear, meaning it could accelerate/decelerate based on the control points whereas the \"p\" or \"progress\" value is linearly mapped to the whole path, so it shouldn't really accelerate/decelerate based on control points. So a progress of 0.2 would be almost exactly 20% along the path. \"t\" is ONLY in an individual Bezier piece.\r\n */\r\n//accepts basic selector text, a path instance, a RawPath instance, or a Segment and returns a RawPath (makes it easy to homogenize things). If an element or selector text is passed in, it'll also cache the value so that if it's queried again, it'll just take the path data from there instead of parsing it all over again (as long as the path data itself hasn't changed - it'll check).\r\n\r\n\r\nexport function getRawPath(value) {\r\n  value = _isString(value) && _selectorExp.test(value) ? document.querySelector(value) || value : value;\r\n  var e = value.getAttribute ? value : 0,\r\n      rawPath;\r\n\r\n  if (e && (value = value.getAttribute(\"d\"))) {\r\n    //implements caching\r\n    if (!e._gsPath) {\r\n      e._gsPath = {};\r\n    }\r\n\r\n    rawPath = e._gsPath[value];\r\n    return rawPath && !rawPath._dirty ? rawPath : e._gsPath[value] = stringToRawPath(value);\r\n  }\r\n\r\n  return !value ? console.warn(\"Expecting a <path> element or an SVG path data string\") : _isString(value) ? stringToRawPath(value) : _isNumber(value[0]) ? [value] : value;\r\n} //copies a RawPath WITHOUT the length meta data (for speed)\r\n\r\nexport function copyRawPath(rawPath) {\r\n  var a = [],\r\n      i = 0;\r\n\r\n  for (; i < rawPath.length; i++) {\r\n    a[i] = _copyMetaData(rawPath[i], rawPath[i].slice(0));\r\n  }\r\n\r\n  return _copyMetaData(rawPath, a);\r\n}\r\nexport function reverseSegment(segment) {\r\n  var i = 0,\r\n      y;\r\n  segment.reverse(); //this will invert the order y, x, y, x so we must flip it back.\r\n\r\n  for (; i < segment.length; i += 2) {\r\n    y = segment[i];\r\n    segment[i] = segment[i + 1];\r\n    segment[i + 1] = y;\r\n  }\r\n\r\n  segment.reversed = !segment.reversed;\r\n}\r\n\r\nvar _createPath = function _createPath(e, ignore) {\r\n  var path = document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\"),\r\n      attr = [].slice.call(e.attributes),\r\n      i = attr.length,\r\n      name;\r\n  ignore = \",\" + ignore + \",\";\r\n\r\n  while (--i > -1) {\r\n    name = attr[i].nodeName.toLowerCase(); //in Microsoft Edge, if you don't set the attribute with a lowercase name, it doesn't render correctly! Super weird.\r\n\r\n    if (ignore.indexOf(\",\" + name + \",\") < 0) {\r\n      path.setAttributeNS(null, name, attr[i].nodeValue);\r\n    }\r\n  }\r\n\r\n  return path;\r\n},\r\n    _typeAttrs = {\r\n  rect: \"rx,ry,x,y,width,height\",\r\n  circle: \"r,cx,cy\",\r\n  ellipse: \"rx,ry,cx,cy\",\r\n  line: \"x1,x2,y1,y2\"\r\n},\r\n    _attrToObj = function _attrToObj(e, attrs) {\r\n  var props = attrs ? attrs.split(\",\") : [],\r\n      obj = {},\r\n      i = props.length;\r\n\r\n  while (--i > -1) {\r\n    obj[props[i]] = +e.getAttribute(props[i]) || 0;\r\n  }\r\n\r\n  return obj;\r\n}; //converts an SVG shape like <circle>, <rect>, <polygon>, <polyline>, <ellipse>, etc. to a <path>, swapping it in and copying the attributes to match.\r\n\r\n\r\nexport function convertToPath(element, swap) {\r\n  var type = element.tagName.toLowerCase(),\r\n      circ = 0.552284749831,\r\n      data,\r\n      x,\r\n      y,\r\n      r,\r\n      ry,\r\n      path,\r\n      rcirc,\r\n      rycirc,\r\n      points,\r\n      w,\r\n      h,\r\n      x2,\r\n      x3,\r\n      x4,\r\n      x5,\r\n      x6,\r\n      y2,\r\n      y3,\r\n      y4,\r\n      y5,\r\n      y6,\r\n      attr;\r\n\r\n  if (type === \"path\" || !element.getBBox) {\r\n    return element;\r\n  }\r\n\r\n  path = _createPath(element, \"x,y,width,height,cx,cy,rx,ry,r,x1,x2,y1,y2,points\");\r\n  attr = _attrToObj(element, _typeAttrs[type]);\r\n\r\n  if (type === \"rect\") {\r\n    r = attr.rx;\r\n    ry = attr.ry || r;\r\n    x = attr.x;\r\n    y = attr.y;\r\n    w = attr.width - r * 2;\r\n    h = attr.height - ry * 2;\r\n\r\n    if (r || ry) {\r\n      //if there are rounded corners, render cubic beziers\r\n      x2 = x + r * (1 - circ);\r\n      x3 = x + r;\r\n      x4 = x3 + w;\r\n      x5 = x4 + r * circ;\r\n      x6 = x4 + r;\r\n      y2 = y + ry * (1 - circ);\r\n      y3 = y + ry;\r\n      y4 = y3 + h;\r\n      y5 = y4 + ry * circ;\r\n      y6 = y4 + ry;\r\n      data = \"M\" + x6 + \",\" + y3 + \" V\" + y4 + \" C\" + [x6, y5, x5, y6, x4, y6, x4 - (x4 - x3) / 3, y6, x3 + (x4 - x3) / 3, y6, x3, y6, x2, y6, x, y5, x, y4, x, y4 - (y4 - y3) / 3, x, y3 + (y4 - y3) / 3, x, y3, x, y2, x2, y, x3, y, x3 + (x4 - x3) / 3, y, x4 - (x4 - x3) / 3, y, x4, y, x5, y, x6, y2, x6, y3].join(\",\") + \"z\";\r\n    } else {\r\n      data = \"M\" + (x + w) + \",\" + y + \" v\" + h + \" h\" + -w + \" v\" + -h + \" h\" + w + \"z\";\r\n    }\r\n  } else if (type === \"circle\" || type === \"ellipse\") {\r\n    if (type === \"circle\") {\r\n      r = ry = attr.r;\r\n      rycirc = r * circ;\r\n    } else {\r\n      r = attr.rx;\r\n      ry = attr.ry;\r\n      rycirc = ry * circ;\r\n    }\r\n\r\n    x = attr.cx;\r\n    y = attr.cy;\r\n    rcirc = r * circ;\r\n    data = \"M\" + (x + r) + \",\" + y + \" C\" + [x + r, y + rycirc, x + rcirc, y + ry, x, y + ry, x - rcirc, y + ry, x - r, y + rycirc, x - r, y, x - r, y - rycirc, x - rcirc, y - ry, x, y - ry, x + rcirc, y - ry, x + r, y - rycirc, x + r, y].join(\",\") + \"z\";\r\n  } else if (type === \"line\") {\r\n    data = \"M\" + attr.x1 + \",\" + attr.y1 + \" L\" + attr.x2 + \",\" + attr.y2; //previously, we just converted to \"Mx,y Lx,y\" but Safari has bugs that cause that not to render properly when using a stroke-dasharray that's not fully visible! Using a cubic bezier fixes that issue.\r\n  } else if (type === \"polyline\" || type === \"polygon\") {\r\n    points = (element.getAttribute(\"points\") + \"\").match(_numbersExp) || [];\r\n    x = points.shift();\r\n    y = points.shift();\r\n    data = \"M\" + x + \",\" + y + \" L\" + points.join(\",\");\r\n\r\n    if (type === \"polygon\") {\r\n      data += \",\" + x + \",\" + y + \"z\";\r\n    }\r\n  }\r\n\r\n  path.setAttribute(\"d\", rawPathToString(path._gsRawPath = stringToRawPath(data)));\r\n\r\n  if (swap && element.parentNode) {\r\n    element.parentNode.insertBefore(path, element);\r\n    element.parentNode.removeChild(element);\r\n  }\r\n\r\n  return path;\r\n} //returns the rotation (in degrees) at a particular progress on a rawPath (the slope of the tangent)\r\n\r\nexport function getRotationAtProgress(rawPath, progress) {\r\n  var d = getProgressData(rawPath, progress >= 1 ? 1 - 1e-9 : progress ? progress : 1e-9);\r\n  return getRotationAtBezierT(d.segment, d.i, d.t);\r\n}\r\n\r\nfunction getRotationAtBezierT(segment, i, t) {\r\n  var a = segment[i],\r\n      b = segment[i + 2],\r\n      c = segment[i + 4],\r\n      x;\r\n  a += (b - a) * t;\r\n  b += (c - b) * t;\r\n  a += (b - a) * t;\r\n  x = b + (c + (segment[i + 6] - c) * t - b) * t - a;\r\n  a = segment[i + 1];\r\n  b = segment[i + 3];\r\n  c = segment[i + 5];\r\n  a += (b - a) * t;\r\n  b += (c - b) * t;\r\n  a += (b - a) * t;\r\n  return _round(_atan2(b + (c + (segment[i + 7] - c) * t - b) * t - a, x) * _RAD2DEG);\r\n}\r\n\r\nexport function sliceRawPath(rawPath, start, end) {\r\n  end = _isUndefined(end) ? 1 : _roundPrecise(end) || 0; // we must round to avoid issues like 4.15 / 8 = 0.8300000000000001 instead of 0.83 or 2.8 / 5 = 0.5599999999999999 instead of 0.56 and if someone is doing a loop like start: 2.8 / 0.5, end: 2.8 / 0.5 + 1.\r\n\r\n  start = _roundPrecise(start) || 0;\r\n  var loops = Math.max(0, ~~(_abs(end - start) - 1e-8)),\r\n      path = copyRawPath(rawPath);\r\n\r\n  if (start > end) {\r\n    start = 1 - start;\r\n    end = 1 - end;\r\n\r\n    _reverseRawPath(path);\r\n\r\n    path.totalLength = 0;\r\n  }\r\n\r\n  if (start < 0 || end < 0) {\r\n    var offset = Math.abs(~~Math.min(start, end)) + 1;\r\n    start += offset;\r\n    end += offset;\r\n  }\r\n\r\n  path.totalLength || cacheRawPathMeasurements(path);\r\n  var wrap = end > 1,\r\n      s = getProgressData(path, start, _temp, true),\r\n      e = getProgressData(path, end, _temp2),\r\n      eSeg = e.segment,\r\n      sSeg = s.segment,\r\n      eSegIndex = e.segIndex,\r\n      sSegIndex = s.segIndex,\r\n      ei = e.i,\r\n      si = s.i,\r\n      sameSegment = sSegIndex === eSegIndex,\r\n      sameBezier = ei === si && sameSegment,\r\n      wrapsBehind,\r\n      sShift,\r\n      eShift,\r\n      i,\r\n      copy,\r\n      totalSegments,\r\n      l,\r\n      j;\r\n\r\n  if (wrap || loops) {\r\n    wrapsBehind = eSegIndex < sSegIndex || sameSegment && ei < si || sameBezier && e.t < s.t;\r\n\r\n    if (_splitSegment(path, sSegIndex, si, s.t)) {\r\n      sSegIndex++;\r\n\r\n      if (!wrapsBehind) {\r\n        eSegIndex++;\r\n\r\n        if (sameBezier) {\r\n          e.t = (e.t - s.t) / (1 - s.t);\r\n          ei = 0;\r\n        } else if (sameSegment) {\r\n          ei -= si;\r\n        }\r\n      }\r\n    }\r\n\r\n    if (Math.abs(1 - (end - start)) < 1e-5) {\r\n      eSegIndex = sSegIndex - 1;\r\n    } else if (!e.t && eSegIndex) {\r\n      eSegIndex--;\r\n    } else if (_splitSegment(path, eSegIndex, ei, e.t) && wrapsBehind) {\r\n      sSegIndex++;\r\n    }\r\n\r\n    if (s.t === 1) {\r\n      sSegIndex = (sSegIndex + 1) % path.length;\r\n    }\r\n\r\n    copy = [];\r\n    totalSegments = path.length;\r\n    l = 1 + totalSegments * loops;\r\n    j = sSegIndex;\r\n    l += (totalSegments - sSegIndex + eSegIndex) % totalSegments;\r\n\r\n    for (i = 0; i < l; i++) {\r\n      _appendOrMerge(copy, path[j++ % totalSegments]);\r\n    }\r\n\r\n    path = copy;\r\n  } else {\r\n    eShift = e.t === 1 ? 6 : subdivideSegment(eSeg, ei, e.t);\r\n\r\n    if (start !== end) {\r\n      sShift = subdivideSegment(sSeg, si, sameBezier ? s.t / e.t : s.t);\r\n      sameSegment && (eShift += sShift);\r\n      eSeg.splice(ei + eShift + 2);\r\n      (sShift || si) && sSeg.splice(0, si + sShift);\r\n      i = path.length;\r\n\r\n      while (i--) {\r\n        //chop off any extra segments\r\n        (i < sSegIndex || i > eSegIndex) && path.splice(i, 1);\r\n      }\r\n    } else {\r\n      eSeg.angle = getRotationAtBezierT(eSeg, ei + eShift, 0); //record the value before we chop because it'll be impossible to determine the angle after its length is 0!\r\n\r\n      ei += eShift;\r\n      s = eSeg[ei];\r\n      e = eSeg[ei + 1];\r\n      eSeg.length = eSeg.totalLength = 0;\r\n      eSeg.totalPoints = path.totalPoints = 8;\r\n      eSeg.push(s, e, s, e, s, e, s, e);\r\n    }\r\n  }\r\n\r\n  path.totalLength = 0;\r\n  return path;\r\n} //measures a Segment according to its resolution (so if segment.resolution is 6, for example, it'll take 6 samples equally across each Bezier) and create/populate a \"samples\" Array that has the length up to each of those sample points (always increasing from the start) as well as a \"lookup\" array that's broken up according to the smallest distance between 2 samples. This gives us a very fast way of looking up a progress position rather than looping through all the points/Beziers. You can optionally have it only measure a subset, starting at startIndex and going for a specific number of beziers (remember, there are 3 x/y pairs each, for a total of 6 elements for each Bezier). It will also populate a \"totalLength\" property, but that's not generally super accurate because by default it'll only take 6 samples per Bezier. But for performance reasons, it's perfectly adequate for measuring progress values along the path. If you need a more accurate totalLength, either increase the resolution or use the more advanced bezierToPoints() method which keeps adding points until they don't deviate by more than a certain precision value.\r\n\r\nfunction measureSegment(segment, startIndex, bezierQty) {\r\n  startIndex = startIndex || 0;\r\n\r\n  if (!segment.samples) {\r\n    segment.samples = [];\r\n    segment.lookup = [];\r\n  }\r\n\r\n  var resolution = ~~segment.resolution || 12,\r\n      inc = 1 / resolution,\r\n      endIndex = bezierQty ? startIndex + bezierQty * 6 + 1 : segment.length,\r\n      x1 = segment[startIndex],\r\n      y1 = segment[startIndex + 1],\r\n      samplesIndex = startIndex ? startIndex / 6 * resolution : 0,\r\n      samples = segment.samples,\r\n      lookup = segment.lookup,\r\n      min = (startIndex ? segment.minLength : _largeNum) || _largeNum,\r\n      prevLength = samples[samplesIndex + bezierQty * resolution - 1],\r\n      length = startIndex ? samples[samplesIndex - 1] : 0,\r\n      i,\r\n      j,\r\n      x4,\r\n      x3,\r\n      x2,\r\n      xd,\r\n      xd1,\r\n      y4,\r\n      y3,\r\n      y2,\r\n      yd,\r\n      yd1,\r\n      inv,\r\n      t,\r\n      lengthIndex,\r\n      l,\r\n      segLength;\r\n  samples.length = lookup.length = 0;\r\n\r\n  for (j = startIndex + 2; j < endIndex; j += 6) {\r\n    x4 = segment[j + 4] - x1;\r\n    x3 = segment[j + 2] - x1;\r\n    x2 = segment[j] - x1;\r\n    y4 = segment[j + 5] - y1;\r\n    y3 = segment[j + 3] - y1;\r\n    y2 = segment[j + 1] - y1;\r\n    xd = xd1 = yd = yd1 = 0;\r\n\r\n    if (_abs(x4) < .01 && _abs(y4) < .01 && _abs(x2) + _abs(y2) < .01) {\r\n      //dump points that are sufficiently close (basically right on top of each other, making a bezier super tiny or 0 length)\r\n      if (segment.length > 8) {\r\n        segment.splice(j, 6);\r\n        j -= 6;\r\n        endIndex -= 6;\r\n      }\r\n    } else {\r\n      for (i = 1; i <= resolution; i++) {\r\n        t = inc * i;\r\n        inv = 1 - t;\r\n        xd = xd1 - (xd1 = (t * t * x4 + 3 * inv * (t * x3 + inv * x2)) * t);\r\n        yd = yd1 - (yd1 = (t * t * y4 + 3 * inv * (t * y3 + inv * y2)) * t);\r\n        l = _sqrt(yd * yd + xd * xd);\r\n\r\n        if (l < min) {\r\n          min = l;\r\n        }\r\n\r\n        length += l;\r\n        samples[samplesIndex++] = length;\r\n      }\r\n    }\r\n\r\n    x1 += x4;\r\n    y1 += y4;\r\n  }\r\n\r\n  if (prevLength) {\r\n    prevLength -= length;\r\n\r\n    for (; samplesIndex < samples.length; samplesIndex++) {\r\n      samples[samplesIndex] += prevLength;\r\n    }\r\n  }\r\n\r\n  if (samples.length && min) {\r\n    segment.totalLength = segLength = samples[samples.length - 1] || 0;\r\n    segment.minLength = min;\r\n\r\n    if (segLength / min < 9999) {\r\n      // if the lookup would require too many values (memory problem), we skip this and instead we use a loop to lookup values directly in the samples Array\r\n      l = lengthIndex = 0;\r\n\r\n      for (i = 0; i < segLength; i += min) {\r\n        lookup[l++] = samples[lengthIndex] < i ? ++lengthIndex : lengthIndex;\r\n      }\r\n    }\r\n  } else {\r\n    segment.totalLength = samples[0] = 0;\r\n  }\r\n\r\n  return startIndex ? length - samples[startIndex / 2 - 1] : length;\r\n}\r\n\r\nexport function cacheRawPathMeasurements(rawPath, resolution) {\r\n  var pathLength, points, i;\r\n\r\n  for (i = pathLength = points = 0; i < rawPath.length; i++) {\r\n    rawPath[i].resolution = ~~resolution || 12; //steps per Bezier curve (anchor, 2 control points, to anchor)\r\n\r\n    points += rawPath[i].length;\r\n    pathLength += measureSegment(rawPath[i]);\r\n  }\r\n\r\n  rawPath.totalPoints = points;\r\n  rawPath.totalLength = pathLength;\r\n  return rawPath;\r\n} //divide segment[i] at position t (value between 0 and 1, progress along that particular cubic bezier segment that starts at segment[i]). Returns how many elements were spliced into the segment array (either 0 or 6)\r\n\r\nexport function subdivideSegment(segment, i, t) {\r\n  if (t <= 0 || t >= 1) {\r\n    return 0;\r\n  }\r\n\r\n  var ax = segment[i],\r\n      ay = segment[i + 1],\r\n      cp1x = segment[i + 2],\r\n      cp1y = segment[i + 3],\r\n      cp2x = segment[i + 4],\r\n      cp2y = segment[i + 5],\r\n      bx = segment[i + 6],\r\n      by = segment[i + 7],\r\n      x1a = ax + (cp1x - ax) * t,\r\n      x2 = cp1x + (cp2x - cp1x) * t,\r\n      y1a = ay + (cp1y - ay) * t,\r\n      y2 = cp1y + (cp2y - cp1y) * t,\r\n      x1 = x1a + (x2 - x1a) * t,\r\n      y1 = y1a + (y2 - y1a) * t,\r\n      x2a = cp2x + (bx - cp2x) * t,\r\n      y2a = cp2y + (by - cp2y) * t;\r\n  x2 += (x2a - x2) * t;\r\n  y2 += (y2a - y2) * t;\r\n  segment.splice(i + 2, 4, _round(x1a), //first control point\r\n  _round(y1a), _round(x1), //second control point\r\n  _round(y1), _round(x1 + (x2 - x1) * t), //new fabricated anchor on line\r\n  _round(y1 + (y2 - y1) * t), _round(x2), //third control point\r\n  _round(y2), _round(x2a), //fourth control point\r\n  _round(y2a));\r\n  segment.samples && segment.samples.splice(i / 6 * segment.resolution | 0, 0, 0, 0, 0, 0, 0, 0);\r\n  return 6;\r\n} // returns an object {path, segment, segIndex, i, t}\r\n\r\nfunction getProgressData(rawPath, progress, decoratee, pushToNextIfAtEnd) {\r\n  decoratee = decoratee || {};\r\n  rawPath.totalLength || cacheRawPathMeasurements(rawPath);\r\n\r\n  if (progress < 0 || progress > 1) {\r\n    progress = _wrapProgress(progress);\r\n  }\r\n\r\n  var segIndex = 0,\r\n      segment = rawPath[0],\r\n      samples,\r\n      resolution,\r\n      length,\r\n      min,\r\n      max,\r\n      i,\r\n      t;\r\n\r\n  if (!progress) {\r\n    t = i = segIndex = 0;\r\n    segment = rawPath[0];\r\n  } else if (progress === 1) {\r\n    t = 1;\r\n    segIndex = rawPath.length - 1;\r\n    segment = rawPath[segIndex];\r\n    i = segment.length - 8;\r\n  } else {\r\n    if (rawPath.length > 1) {\r\n      //speed optimization: most of the time, there's only one segment so skip the recursion.\r\n      length = rawPath.totalLength * progress;\r\n      max = i = 0;\r\n\r\n      while ((max += rawPath[i++].totalLength) < length) {\r\n        segIndex = i;\r\n      }\r\n\r\n      segment = rawPath[segIndex];\r\n      min = max - segment.totalLength;\r\n      progress = (length - min) / (max - min) || 0;\r\n    }\r\n\r\n    samples = segment.samples;\r\n    resolution = segment.resolution; //how many samples per cubic bezier chunk\r\n\r\n    length = segment.totalLength * progress;\r\n    i = segment.lookup.length ? segment.lookup[~~(length / segment.minLength)] || 0 : _getSampleIndex(samples, length, progress);\r\n    min = i ? samples[i - 1] : 0;\r\n    max = samples[i];\r\n\r\n    if (max < length) {\r\n      min = max;\r\n      max = samples[++i];\r\n    }\r\n\r\n    t = 1 / resolution * ((length - min) / (max - min) + i % resolution);\r\n    i = ~~(i / resolution) * 6;\r\n\r\n    if (pushToNextIfAtEnd && t === 1) {\r\n      if (i + 6 < segment.length) {\r\n        i += 6;\r\n        t = 0;\r\n      } else if (segIndex + 1 < rawPath.length) {\r\n        i = t = 0;\r\n        segment = rawPath[++segIndex];\r\n      }\r\n    }\r\n  }\r\n\r\n  decoratee.t = t;\r\n  decoratee.i = i;\r\n  decoratee.path = rawPath;\r\n  decoratee.segment = segment;\r\n  decoratee.segIndex = segIndex;\r\n  return decoratee;\r\n}\r\n\r\nexport function getPositionOnPath(rawPath, progress, includeAngle, point) {\r\n  var segment = rawPath[0],\r\n      result = point || {},\r\n      samples,\r\n      resolution,\r\n      length,\r\n      min,\r\n      max,\r\n      i,\r\n      t,\r\n      a,\r\n      inv;\r\n\r\n  if (progress < 0 || progress > 1) {\r\n    progress = _wrapProgress(progress);\r\n  }\r\n\r\n  segment.lookup || cacheRawPathMeasurements(rawPath);\r\n\r\n  if (rawPath.length > 1) {\r\n    //speed optimization: most of the time, there's only one segment so skip the recursion.\r\n    length = rawPath.totalLength * progress;\r\n    max = i = 0;\r\n\r\n    while ((max += rawPath[i++].totalLength) < length) {\r\n      segment = rawPath[i];\r\n    }\r\n\r\n    min = max - segment.totalLength;\r\n    progress = (length - min) / (max - min) || 0;\r\n  }\r\n\r\n  samples = segment.samples;\r\n  resolution = segment.resolution;\r\n  length = segment.totalLength * progress;\r\n  i = segment.lookup.length ? segment.lookup[progress < 1 ? ~~(length / segment.minLength) : segment.lookup.length - 1] || 0 : _getSampleIndex(samples, length, progress);\r\n  min = i ? samples[i - 1] : 0;\r\n  max = samples[i];\r\n\r\n  if (max < length) {\r\n    min = max;\r\n    max = samples[++i];\r\n  }\r\n\r\n  t = 1 / resolution * ((length - min) / (max - min) + i % resolution) || 0;\r\n  inv = 1 - t;\r\n  i = ~~(i / resolution) * 6;\r\n  a = segment[i];\r\n  result.x = _round((t * t * (segment[i + 6] - a) + 3 * inv * (t * (segment[i + 4] - a) + inv * (segment[i + 2] - a))) * t + a);\r\n  result.y = _round((t * t * (segment[i + 7] - (a = segment[i + 1])) + 3 * inv * (t * (segment[i + 5] - a) + inv * (segment[i + 3] - a))) * t + a);\r\n\r\n  if (includeAngle) {\r\n    result.angle = segment.totalLength ? getRotationAtBezierT(segment, i, t >= 1 ? 1 - 1e-9 : t ? t : 1e-9) : segment.angle || 0;\r\n  }\r\n\r\n  return result;\r\n} //applies a matrix transform to RawPath (or a segment in a RawPath) and returns whatever was passed in (it transforms the values in the array(s), not a copy).\r\n\r\nexport function transformRawPath(rawPath, a, b, c, d, tx, ty) {\r\n  var j = rawPath.length,\r\n      segment,\r\n      l,\r\n      i,\r\n      x,\r\n      y;\r\n\r\n  while (--j > -1) {\r\n    segment = rawPath[j];\r\n    l = segment.length;\r\n\r\n    for (i = 0; i < l; i += 2) {\r\n      x = segment[i];\r\n      y = segment[i + 1];\r\n      segment[i] = x * a + y * c + tx;\r\n      segment[i + 1] = x * b + y * d + ty;\r\n    }\r\n  }\r\n\r\n  rawPath._dirty = 1;\r\n  return rawPath;\r\n} // translates SVG arc data into a segment (cubic beziers). Angle is in degrees.\r\n\r\nfunction arcToSegment(lastX, lastY, rx, ry, angle, largeArcFlag, sweepFlag, x, y) {\r\n  if (lastX === x && lastY === y) {\r\n    return;\r\n  }\r\n\r\n  rx = _abs(rx);\r\n  ry = _abs(ry);\r\n\r\n  var angleRad = angle % 360 * _DEG2RAD,\r\n      cosAngle = _cos(angleRad),\r\n      sinAngle = _sin(angleRad),\r\n      PI = Math.PI,\r\n      TWOPI = PI * 2,\r\n      dx2 = (lastX - x) / 2,\r\n      dy2 = (lastY - y) / 2,\r\n      x1 = cosAngle * dx2 + sinAngle * dy2,\r\n      y1 = -sinAngle * dx2 + cosAngle * dy2,\r\n      x1_sq = x1 * x1,\r\n      y1_sq = y1 * y1,\r\n      radiiCheck = x1_sq / (rx * rx) + y1_sq / (ry * ry);\r\n\r\n  if (radiiCheck > 1) {\r\n    rx = _sqrt(radiiCheck) * rx;\r\n    ry = _sqrt(radiiCheck) * ry;\r\n  }\r\n\r\n  var rx_sq = rx * rx,\r\n      ry_sq = ry * ry,\r\n      sq = (rx_sq * ry_sq - rx_sq * y1_sq - ry_sq * x1_sq) / (rx_sq * y1_sq + ry_sq * x1_sq);\r\n\r\n  if (sq < 0) {\r\n    sq = 0;\r\n  }\r\n\r\n  var coef = (largeArcFlag === sweepFlag ? -1 : 1) * _sqrt(sq),\r\n      cx1 = coef * (rx * y1 / ry),\r\n      cy1 = coef * -(ry * x1 / rx),\r\n      sx2 = (lastX + x) / 2,\r\n      sy2 = (lastY + y) / 2,\r\n      cx = sx2 + (cosAngle * cx1 - sinAngle * cy1),\r\n      cy = sy2 + (sinAngle * cx1 + cosAngle * cy1),\r\n      ux = (x1 - cx1) / rx,\r\n      uy = (y1 - cy1) / ry,\r\n      vx = (-x1 - cx1) / rx,\r\n      vy = (-y1 - cy1) / ry,\r\n      temp = ux * ux + uy * uy,\r\n      angleStart = (uy < 0 ? -1 : 1) * Math.acos(ux / _sqrt(temp)),\r\n      angleExtent = (ux * vy - uy * vx < 0 ? -1 : 1) * Math.acos((ux * vx + uy * vy) / _sqrt(temp * (vx * vx + vy * vy)));\r\n\r\n  isNaN(angleExtent) && (angleExtent = PI); //rare edge case. Math.cos(-1) is NaN.\r\n\r\n  if (!sweepFlag && angleExtent > 0) {\r\n    angleExtent -= TWOPI;\r\n  } else if (sweepFlag && angleExtent < 0) {\r\n    angleExtent += TWOPI;\r\n  }\r\n\r\n  angleStart %= TWOPI;\r\n  angleExtent %= TWOPI;\r\n\r\n  var segments = Math.ceil(_abs(angleExtent) / (TWOPI / 4)),\r\n      rawPath = [],\r\n      angleIncrement = angleExtent / segments,\r\n      controlLength = 4 / 3 * _sin(angleIncrement / 2) / (1 + _cos(angleIncrement / 2)),\r\n      ma = cosAngle * rx,\r\n      mb = sinAngle * rx,\r\n      mc = sinAngle * -ry,\r\n      md = cosAngle * ry,\r\n      i;\r\n\r\n  for (i = 0; i < segments; i++) {\r\n    angle = angleStart + i * angleIncrement;\r\n    x1 = _cos(angle);\r\n    y1 = _sin(angle);\r\n    ux = _cos(angle += angleIncrement);\r\n    uy = _sin(angle);\r\n    rawPath.push(x1 - controlLength * y1, y1 + controlLength * x1, ux + controlLength * uy, uy - controlLength * ux, ux, uy);\r\n  } //now transform according to the actual size of the ellipse/arc (the beziers were noramlized, between 0 and 1 on a circle).\r\n\r\n\r\n  for (i = 0; i < rawPath.length; i += 2) {\r\n    x1 = rawPath[i];\r\n    y1 = rawPath[i + 1];\r\n    rawPath[i] = x1 * ma + y1 * mc + cx;\r\n    rawPath[i + 1] = x1 * mb + y1 * md + cy;\r\n  }\r\n\r\n  rawPath[i - 2] = x; //always set the end to exactly where it's supposed to be\r\n\r\n  rawPath[i - 1] = y;\r\n  return rawPath;\r\n} //Spits back a RawPath with absolute coordinates. Each segment starts with a \"moveTo\" command (x coordinate, then y) and then 2 control points (x, y, x, y), then anchor. The goal is to minimize memory and maximize speed.\r\n\r\n\r\nexport function stringToRawPath(d) {\r\n  var a = (d + \"\").replace(_scientific, function (m) {\r\n    var n = +m;\r\n    return n < 0.0001 && n > -0.0001 ? 0 : n;\r\n  }).match(_svgPathExp) || [],\r\n      //some authoring programs spit out very small numbers in scientific notation like \"1e-5\", so make sure we round that down to 0 first.\r\n  path = [],\r\n      relativeX = 0,\r\n      relativeY = 0,\r\n      twoThirds = 2 / 3,\r\n      elements = a.length,\r\n      points = 0,\r\n      errorMessage = \"ERROR: malformed path: \" + d,\r\n      i,\r\n      j,\r\n      x,\r\n      y,\r\n      command,\r\n      isRelative,\r\n      segment,\r\n      startX,\r\n      startY,\r\n      difX,\r\n      difY,\r\n      beziers,\r\n      prevCommand,\r\n      flag1,\r\n      flag2,\r\n      line = function line(sx, sy, ex, ey) {\r\n    difX = (ex - sx) / 3;\r\n    difY = (ey - sy) / 3;\r\n    segment.push(sx + difX, sy + difY, ex - difX, ey - difY, ex, ey);\r\n  };\r\n\r\n  if (!d || !isNaN(a[0]) || isNaN(a[1])) {\r\n    console.log(errorMessage);\r\n    return path;\r\n  }\r\n\r\n  for (i = 0; i < elements; i++) {\r\n    prevCommand = command;\r\n\r\n    if (isNaN(a[i])) {\r\n      command = a[i].toUpperCase();\r\n      isRelative = command !== a[i]; //lower case means relative\r\n    } else {\r\n      //commands like \"C\" can be strung together without any new command characters between.\r\n      i--;\r\n    }\r\n\r\n    x = +a[i + 1];\r\n    y = +a[i + 2];\r\n\r\n    if (isRelative) {\r\n      x += relativeX;\r\n      y += relativeY;\r\n    }\r\n\r\n    if (!i) {\r\n      startX = x;\r\n      startY = y;\r\n    } // \"M\" (move)\r\n\r\n\r\n    if (command === \"M\") {\r\n      if (segment) {\r\n        if (segment.length < 8) {\r\n          //if the path data was funky and just had a M with no actual drawing anywhere, skip it.\r\n          path.length -= 1;\r\n        } else {\r\n          points += segment.length;\r\n        }\r\n      }\r\n\r\n      relativeX = startX = x;\r\n      relativeY = startY = y;\r\n      segment = [x, y];\r\n      path.push(segment);\r\n      i += 2;\r\n      command = \"L\"; //an \"M\" with more than 2 values gets interpreted as \"lineTo\" commands (\"L\").\r\n      // \"C\" (cubic bezier)\r\n    } else if (command === \"C\") {\r\n      if (!segment) {\r\n        segment = [0, 0];\r\n      }\r\n\r\n      if (!isRelative) {\r\n        relativeX = relativeY = 0;\r\n      } //note: \"*1\" is just a fast/short way to cast the value as a Number. WAAAY faster in Chrome, slightly slower in Firefox.\r\n\r\n\r\n      segment.push(x, y, relativeX + a[i + 3] * 1, relativeY + a[i + 4] * 1, relativeX += a[i + 5] * 1, relativeY += a[i + 6] * 1);\r\n      i += 6; // \"S\" (continuation of cubic bezier)\r\n    } else if (command === \"S\") {\r\n      difX = relativeX;\r\n      difY = relativeY;\r\n\r\n      if (prevCommand === \"C\" || prevCommand === \"S\") {\r\n        difX += relativeX - segment[segment.length - 4];\r\n        difY += relativeY - segment[segment.length - 3];\r\n      }\r\n\r\n      if (!isRelative) {\r\n        relativeX = relativeY = 0;\r\n      }\r\n\r\n      segment.push(difX, difY, x, y, relativeX += a[i + 3] * 1, relativeY += a[i + 4] * 1);\r\n      i += 4; // \"Q\" (quadratic bezier)\r\n    } else if (command === \"Q\") {\r\n      difX = relativeX + (x - relativeX) * twoThirds;\r\n      difY = relativeY + (y - relativeY) * twoThirds;\r\n\r\n      if (!isRelative) {\r\n        relativeX = relativeY = 0;\r\n      }\r\n\r\n      relativeX += a[i + 3] * 1;\r\n      relativeY += a[i + 4] * 1;\r\n      segment.push(difX, difY, relativeX + (x - relativeX) * twoThirds, relativeY + (y - relativeY) * twoThirds, relativeX, relativeY);\r\n      i += 4; // \"T\" (continuation of quadratic bezier)\r\n    } else if (command === \"T\") {\r\n      difX = relativeX - segment[segment.length - 4];\r\n      difY = relativeY - segment[segment.length - 3];\r\n      segment.push(relativeX + difX, relativeY + difY, x + (relativeX + difX * 1.5 - x) * twoThirds, y + (relativeY + difY * 1.5 - y) * twoThirds, relativeX = x, relativeY = y);\r\n      i += 2; // \"H\" (horizontal line)\r\n    } else if (command === \"H\") {\r\n      line(relativeX, relativeY, relativeX = x, relativeY);\r\n      i += 1; // \"V\" (vertical line)\r\n    } else if (command === \"V\") {\r\n      //adjust values because the first (and only one) isn't x in this case, it's y.\r\n      line(relativeX, relativeY, relativeX, relativeY = x + (isRelative ? relativeY - relativeX : 0));\r\n      i += 1; // \"L\" (line) or \"Z\" (close)\r\n    } else if (command === \"L\" || command === \"Z\") {\r\n      if (command === \"Z\") {\r\n        x = startX;\r\n        y = startY;\r\n        segment.closed = true;\r\n      }\r\n\r\n      if (command === \"L\" || _abs(relativeX - x) > 0.5 || _abs(relativeY - y) > 0.5) {\r\n        line(relativeX, relativeY, x, y);\r\n\r\n        if (command === \"L\") {\r\n          i += 2;\r\n        }\r\n      }\r\n\r\n      relativeX = x;\r\n      relativeY = y; // \"A\" (arc)\r\n    } else if (command === \"A\") {\r\n      flag1 = a[i + 4];\r\n      flag2 = a[i + 5];\r\n      difX = a[i + 6];\r\n      difY = a[i + 7];\r\n      j = 7;\r\n\r\n      if (flag1.length > 1) {\r\n        // for cases when the flags are merged, like \"a8 8 0 018 8\" (the 0 and 1 flags are WITH the x value of 8, but it could also be \"a8 8 0 01-8 8\" so it may include x or not)\r\n        if (flag1.length < 3) {\r\n          difY = difX;\r\n          difX = flag2;\r\n          j--;\r\n        } else {\r\n          difY = flag2;\r\n          difX = flag1.substr(2);\r\n          j -= 2;\r\n        }\r\n\r\n        flag2 = flag1.charAt(1);\r\n        flag1 = flag1.charAt(0);\r\n      }\r\n\r\n      beziers = arcToSegment(relativeX, relativeY, +a[i + 1], +a[i + 2], +a[i + 3], +flag1, +flag2, (isRelative ? relativeX : 0) + difX * 1, (isRelative ? relativeY : 0) + difY * 1);\r\n      i += j;\r\n\r\n      if (beziers) {\r\n        for (j = 0; j < beziers.length; j++) {\r\n          segment.push(beziers[j]);\r\n        }\r\n      }\r\n\r\n      relativeX = segment[segment.length - 2];\r\n      relativeY = segment[segment.length - 1];\r\n    } else {\r\n      console.log(errorMessage);\r\n    }\r\n  }\r\n\r\n  i = segment.length;\r\n\r\n  if (i < 6) {\r\n    //in case there's odd SVG like a M0,0 command at the very end.\r\n    path.pop();\r\n    i = 0;\r\n  } else if (segment[0] === segment[i - 2] && segment[1] === segment[i - 1]) {\r\n    segment.closed = true;\r\n  }\r\n\r\n  path.totalPoints = points + i;\r\n  return path;\r\n} //populates the points array in alternating x/y values (like [x, y, x, y...] instead of individual point objects [{x, y}, {x, y}...] to conserve memory and stay in line with how we're handling segment arrays\r\n\r\nexport function bezierToPoints(x1, y1, x2, y2, x3, y3, x4, y4, threshold, points, index) {\r\n  var x12 = (x1 + x2) / 2,\r\n      y12 = (y1 + y2) / 2,\r\n      x23 = (x2 + x3) / 2,\r\n      y23 = (y2 + y3) / 2,\r\n      x34 = (x3 + x4) / 2,\r\n      y34 = (y3 + y4) / 2,\r\n      x123 = (x12 + x23) / 2,\r\n      y123 = (y12 + y23) / 2,\r\n      x234 = (x23 + x34) / 2,\r\n      y234 = (y23 + y34) / 2,\r\n      x1234 = (x123 + x234) / 2,\r\n      y1234 = (y123 + y234) / 2,\r\n      dx = x4 - x1,\r\n      dy = y4 - y1,\r\n      d2 = _abs((x2 - x4) * dy - (y2 - y4) * dx),\r\n      d3 = _abs((x3 - x4) * dy - (y3 - y4) * dx),\r\n      length;\r\n\r\n  if (!points) {\r\n    points = [x1, y1, x4, y4];\r\n    index = 2;\r\n  }\r\n\r\n  points.splice(index || points.length - 2, 0, x1234, y1234);\r\n\r\n  if ((d2 + d3) * (d2 + d3) > threshold * (dx * dx + dy * dy)) {\r\n    length = points.length;\r\n    bezierToPoints(x1, y1, x12, y12, x123, y123, x1234, y1234, threshold, points, index);\r\n    bezierToPoints(x1234, y1234, x234, y234, x34, y34, x4, y4, threshold, points, index + 2 + (points.length - length));\r\n  }\r\n\r\n  return points;\r\n}\r\n/*\r\nfunction getAngleBetweenPoints(x0, y0, x1, y1, x2, y2) { //angle between 3 points in radians\r\n\tvar dx1 = x1 - x0,\r\n\t\tdy1 = y1 - y0,\r\n\t\tdx2 = x2 - x1,\r\n\t\tdy2 = y2 - y1,\r\n\t\tdx3 = x2 - x0,\r\n\t\tdy3 = y2 - y0,\r\n\t\ta = dx1 * dx1 + dy1 * dy1,\r\n\t\tb = dx2 * dx2 + dy2 * dy2,\r\n\t\tc = dx3 * dx3 + dy3 * dy3;\r\n\treturn Math.acos( (a + b - c) / _sqrt(4 * a * b) );\r\n},\r\n*/\r\n//pointsToSegment() doesn't handle flat coordinates (where y is always 0) the way we need (the resulting control points are always right on top of the anchors), so this function basically makes the control points go directly up and down, varying in length based on the curviness (more curvy, further control points)\r\n\r\nexport function flatPointsToSegment(points, curviness) {\r\n  if (curviness === void 0) {\r\n    curviness = 1;\r\n  }\r\n\r\n  var x = points[0],\r\n      y = 0,\r\n      segment = [x, y],\r\n      i = 2;\r\n\r\n  for (; i < points.length; i += 2) {\r\n    segment.push(x, y, points[i], y = (points[i] - x) * curviness / 2, x = points[i], -y);\r\n  }\r\n\r\n  return segment;\r\n} //points is an array of x/y points, like [x, y, x, y, x, y]\r\n\r\nexport function pointsToSegment(points, curviness) {\r\n  //points = simplifyPoints(points, tolerance);\r\n  _abs(points[0] - points[2]) < 1e-4 && _abs(points[1] - points[3]) < 1e-4 && (points = points.slice(2)); // if the first two points are super close, dump the first one.\r\n\r\n  var l = points.length - 2,\r\n      x = +points[0],\r\n      y = +points[1],\r\n      nextX = +points[2],\r\n      nextY = +points[3],\r\n      segment = [x, y, x, y],\r\n      dx2 = nextX - x,\r\n      dy2 = nextY - y,\r\n      closed = Math.abs(points[l] - x) < 0.001 && Math.abs(points[l + 1] - y) < 0.001,\r\n      prevX,\r\n      prevY,\r\n      i,\r\n      dx1,\r\n      dy1,\r\n      r1,\r\n      r2,\r\n      r3,\r\n      tl,\r\n      mx1,\r\n      mx2,\r\n      mxm,\r\n      my1,\r\n      my2,\r\n      mym;\r\n\r\n  if (closed) {\r\n    // if the start and end points are basically on top of each other, close the segment by adding the 2nd point to the end, and the 2nd-to-last point to the beginning (we'll remove them at the end, but this allows the curvature to look perfect)\r\n    points.push(nextX, nextY);\r\n    nextX = x;\r\n    nextY = y;\r\n    x = points[l - 2];\r\n    y = points[l - 1];\r\n    points.unshift(x, y);\r\n    l += 4;\r\n  }\r\n\r\n  curviness = curviness || curviness === 0 ? +curviness : 1;\r\n\r\n  for (i = 2; i < l; i += 2) {\r\n    prevX = x;\r\n    prevY = y;\r\n    x = nextX;\r\n    y = nextY;\r\n    nextX = +points[i + 2];\r\n    nextY = +points[i + 3];\r\n\r\n    if (x === nextX && y === nextY) {\r\n      continue;\r\n    }\r\n\r\n    dx1 = dx2;\r\n    dy1 = dy2;\r\n    dx2 = nextX - x;\r\n    dy2 = nextY - y;\r\n    r1 = _sqrt(dx1 * dx1 + dy1 * dy1); // r1, r2, and r3 correlate x and y (and z in the future). Basically 2D or 3D hypotenuse\r\n\r\n    r2 = _sqrt(dx2 * dx2 + dy2 * dy2);\r\n    r3 = _sqrt(Math.pow(dx2 / r2 + dx1 / r1, 2) + Math.pow(dy2 / r2 + dy1 / r1, 2));\r\n    tl = (r1 + r2) * curviness * 0.25 / r3;\r\n    mx1 = x - (x - prevX) * (r1 ? tl / r1 : 0);\r\n    mx2 = x + (nextX - x) * (r2 ? tl / r2 : 0);\r\n    mxm = x - (mx1 + ((mx2 - mx1) * (r1 * 3 / (r1 + r2) + 0.5) / 4 || 0));\r\n    my1 = y - (y - prevY) * (r1 ? tl / r1 : 0);\r\n    my2 = y + (nextY - y) * (r2 ? tl / r2 : 0);\r\n    mym = y - (my1 + ((my2 - my1) * (r1 * 3 / (r1 + r2) + 0.5) / 4 || 0));\r\n\r\n    if (x !== prevX || y !== prevY) {\r\n      segment.push(_round(mx1 + mxm), // first control point\r\n      _round(my1 + mym), _round(x), // anchor\r\n      _round(y), _round(mx2 + mxm), // second control point\r\n      _round(my2 + mym));\r\n    }\r\n  }\r\n\r\n  x !== nextX || y !== nextY || segment.length < 4 ? segment.push(_round(nextX), _round(nextY), _round(nextX), _round(nextY)) : segment.length -= 2;\r\n\r\n  if (segment.length === 2) {\r\n    // only one point!\r\n    segment.push(x, y, x, y, x, y);\r\n  } else if (closed) {\r\n    segment.splice(0, 6);\r\n    segment.length = segment.length - 6;\r\n  }\r\n\r\n  return segment;\r\n} //returns the squared distance between an x/y coordinate and a segment between x1/y1 and x2/y2\r\n\r\nfunction pointToSegDist(x, y, x1, y1, x2, y2) {\r\n  var dx = x2 - x1,\r\n      dy = y2 - y1,\r\n      t;\r\n\r\n  if (dx || dy) {\r\n    t = ((x - x1) * dx + (y - y1) * dy) / (dx * dx + dy * dy);\r\n\r\n    if (t > 1) {\r\n      x1 = x2;\r\n      y1 = y2;\r\n    } else if (t > 0) {\r\n      x1 += dx * t;\r\n      y1 += dy * t;\r\n    }\r\n  }\r\n\r\n  return Math.pow(x - x1, 2) + Math.pow(y - y1, 2);\r\n}\r\n\r\nfunction simplifyStep(points, first, last, tolerance, simplified) {\r\n  var maxSqDist = tolerance,\r\n      firstX = points[first],\r\n      firstY = points[first + 1],\r\n      lastX = points[last],\r\n      lastY = points[last + 1],\r\n      index,\r\n      i,\r\n      d;\r\n\r\n  for (i = first + 2; i < last; i += 2) {\r\n    d = pointToSegDist(points[i], points[i + 1], firstX, firstY, lastX, lastY);\r\n\r\n    if (d > maxSqDist) {\r\n      index = i;\r\n      maxSqDist = d;\r\n    }\r\n  }\r\n\r\n  if (maxSqDist > tolerance) {\r\n    index - first > 2 && simplifyStep(points, first, index, tolerance, simplified);\r\n    simplified.push(points[index], points[index + 1]);\r\n    last - index > 2 && simplifyStep(points, index, last, tolerance, simplified);\r\n  }\r\n} //points is an array of x/y values like [x, y, x, y, x, y]\r\n\r\n\r\nexport function simplifyPoints(points, tolerance) {\r\n  var prevX = parseFloat(points[0]),\r\n      prevY = parseFloat(points[1]),\r\n      temp = [prevX, prevY],\r\n      l = points.length - 2,\r\n      i,\r\n      x,\r\n      y,\r\n      dx,\r\n      dy,\r\n      result,\r\n      last;\r\n  tolerance = Math.pow(tolerance || 1, 2);\r\n\r\n  for (i = 2; i < l; i += 2) {\r\n    x = parseFloat(points[i]);\r\n    y = parseFloat(points[i + 1]);\r\n    dx = prevX - x;\r\n    dy = prevY - y;\r\n\r\n    if (dx * dx + dy * dy > tolerance) {\r\n      temp.push(x, y);\r\n      prevX = x;\r\n      prevY = y;\r\n    }\r\n  }\r\n\r\n  temp.push(parseFloat(points[l]), parseFloat(points[l + 1]));\r\n  last = temp.length - 2;\r\n  result = [temp[0], temp[1]];\r\n  simplifyStep(temp, 0, last, tolerance, result);\r\n  result.push(temp[last], temp[last + 1]);\r\n  return result;\r\n}\r\n\r\nfunction getClosestProgressOnBezier(iterations, px, py, start, end, slices, x0, y0, x1, y1, x2, y2, x3, y3) {\r\n  var inc = (end - start) / slices,\r\n      best = 0,\r\n      t = start,\r\n      x,\r\n      y,\r\n      d,\r\n      dx,\r\n      dy,\r\n      inv;\r\n  _bestDistance = _largeNum;\r\n\r\n  while (t <= end) {\r\n    inv = 1 - t;\r\n    x = inv * inv * inv * x0 + 3 * inv * inv * t * x1 + 3 * inv * t * t * x2 + t * t * t * x3;\r\n    y = inv * inv * inv * y0 + 3 * inv * inv * t * y1 + 3 * inv * t * t * y2 + t * t * t * y3;\r\n    dx = x - px;\r\n    dy = y - py;\r\n    d = dx * dx + dy * dy;\r\n\r\n    if (d < _bestDistance) {\r\n      _bestDistance = d;\r\n      best = t;\r\n    }\r\n\r\n    t += inc;\r\n  }\r\n\r\n  return iterations > 1 ? getClosestProgressOnBezier(iterations - 1, px, py, Math.max(best - inc, 0), Math.min(best + inc, 1), slices, x0, y0, x1, y1, x2, y2, x3, y3) : best;\r\n}\r\n\r\nexport function getClosestData(rawPath, x, y, slices) {\r\n  //returns an object with the closest j, i, and t (j is the segment index, i is the index of the point in that segment, and t is the time/progress along that bezier)\r\n  var closest = {\r\n    j: 0,\r\n    i: 0,\r\n    t: 0\r\n  },\r\n      bestDistance = _largeNum,\r\n      i,\r\n      j,\r\n      t,\r\n      segment;\r\n\r\n  for (j = 0; j < rawPath.length; j++) {\r\n    segment = rawPath[j];\r\n\r\n    for (i = 0; i < segment.length; i += 6) {\r\n      t = getClosestProgressOnBezier(1, x, y, 0, 1, slices || 20, segment[i], segment[i + 1], segment[i + 2], segment[i + 3], segment[i + 4], segment[i + 5], segment[i + 6], segment[i + 7]);\r\n\r\n      if (bestDistance > _bestDistance) {\r\n        bestDistance = _bestDistance;\r\n        closest.j = j;\r\n        closest.i = i;\r\n        closest.t = t;\r\n      }\r\n    }\r\n  }\r\n\r\n  return closest;\r\n} //subdivide a Segment closest to a specific x,y coordinate\r\n\r\nexport function subdivideSegmentNear(x, y, segment, slices, iterations) {\r\n  var l = segment.length,\r\n      bestDistance = _largeNum,\r\n      bestT = 0,\r\n      bestSegmentIndex = 0,\r\n      t,\r\n      i;\r\n  slices = slices || 20;\r\n  iterations = iterations || 3;\r\n\r\n  for (i = 0; i < l; i += 6) {\r\n    t = getClosestProgressOnBezier(1, x, y, 0, 1, slices, segment[i], segment[i + 1], segment[i + 2], segment[i + 3], segment[i + 4], segment[i + 5], segment[i + 6], segment[i + 7]);\r\n\r\n    if (bestDistance > _bestDistance) {\r\n      bestDistance = _bestDistance;\r\n      bestT = t;\r\n      bestSegmentIndex = i;\r\n    }\r\n  }\r\n\r\n  t = getClosestProgressOnBezier(iterations, x, y, bestT - 0.05, bestT + 0.05, slices, segment[bestSegmentIndex], segment[bestSegmentIndex + 1], segment[bestSegmentIndex + 2], segment[bestSegmentIndex + 3], segment[bestSegmentIndex + 4], segment[bestSegmentIndex + 5], segment[bestSegmentIndex + 6], segment[bestSegmentIndex + 7]);\r\n  subdivideSegment(segment, bestSegmentIndex, t);\r\n  return bestSegmentIndex + 6;\r\n}\r\n/*\r\nTakes any of the following and converts it to an all Cubic Bezier SVG data string:\r\n- A <path> data string like \"M0,0 L2,4 v20,15 H100\"\r\n- A RawPath, like [[x, y, x, y, x, y, x, y][[x, y, x, y, x, y, x, y]]\r\n- A Segment, like [x, y, x, y, x, y, x, y]\r\n\r\nNote: all numbers are rounded down to the closest 0.001 to minimize memory, maximize speed, and avoid odd numbers like 1e-13\r\n*/\r\n\r\nexport function rawPathToString(rawPath) {\r\n  if (_isNumber(rawPath[0])) {\r\n    //in case a segment is passed in instead\r\n    rawPath = [rawPath];\r\n  }\r\n\r\n  var result = \"\",\r\n      l = rawPath.length,\r\n      sl,\r\n      s,\r\n      i,\r\n      segment;\r\n\r\n  for (s = 0; s < l; s++) {\r\n    segment = rawPath[s];\r\n    result += \"M\" + _round(segment[0]) + \",\" + _round(segment[1]) + \" C\";\r\n    sl = segment.length;\r\n\r\n    for (i = 2; i < sl; i++) {\r\n      result += _round(segment[i++]) + \",\" + _round(segment[i++]) + \" \" + _round(segment[i++]) + \",\" + _round(segment[i++]) + \" \" + _round(segment[i++]) + \",\" + _round(segment[i]) + \" \";\r\n    }\r\n\r\n    if (segment.closed) {\r\n      result += \"z\";\r\n    }\r\n  }\r\n\r\n  return result;\r\n}\r\n/*\r\n// takes a segment with coordinates [x, y, x, y, ...] and converts the control points into angles and lengths [x, y, angle, length, angle, length, x, y, angle, length, ...] so that it animates more cleanly and avoids odd breaks/kinks. For example, if you animate from 1 o'clock to 6 o'clock, it'd just go directly/linearly rather than around. So the length would be very short in the middle of the tween.\r\nexport function cpCoordsToAngles(segment, copy) {\r\n\tvar result = copy ? segment.slice(0) : segment,\r\n\t\tx, y, i;\r\n\tfor (i = 0; i < segment.length; i+=6) {\r\n\t\tx = segment[i+2] - segment[i];\r\n\t\ty = segment[i+3] - segment[i+1];\r\n\t\tresult[i+2] = Math.atan2(y, x);\r\n\t\tresult[i+3] = Math.sqrt(x * x + y * y);\r\n\t\tx = segment[i+6] - segment[i+4];\r\n\t\ty = segment[i+7] - segment[i+5];\r\n\t\tresult[i+4] = Math.atan2(y, x);\r\n\t\tresult[i+5] = Math.sqrt(x * x + y * y);\r\n\t}\r\n\treturn result;\r\n}\r\n\r\n// takes a segment that was converted with cpCoordsToAngles() to have angles and lengths instead of coordinates for the control points, and converts it BACK into coordinates.\r\nexport function cpAnglesToCoords(segment, copy) {\r\n\tvar result = copy ? segment.slice(0) : segment,\r\n\t\tlength = segment.length,\r\n\t\trnd = 1000,\r\n\t\tangle, l, i, j;\r\n\tfor (i = 0; i < length; i+=6) {\r\n\t\tangle = segment[i+2];\r\n\t\tl = segment[i+3]; //length\r\n\t\tresult[i+2] = (((segment[i] + Math.cos(angle) * l) * rnd) | 0) / rnd;\r\n\t\tresult[i+3] = (((segment[i+1] + Math.sin(angle) * l) * rnd) | 0) / rnd;\r\n\t\tangle = segment[i+4];\r\n\t\tl = segment[i+5]; //length\r\n\t\tresult[i+4] = (((segment[i+6] - Math.cos(angle) * l) * rnd) | 0) / rnd;\r\n\t\tresult[i+5] = (((segment[i+7] - Math.sin(angle) * l) * rnd) | 0) / rnd;\r\n\t}\r\n\treturn result;\r\n}\r\n\r\n//adds an \"isSmooth\" array to each segment and populates it with a boolean value indicating whether or not it's smooth (the control points have basically the same slope). For any smooth control points, it converts the coordinates into angle (x, in radians) and length (y) and puts them into the same index value in a smoothData array.\r\nexport function populateSmoothData(rawPath) {\r\n\tlet j = rawPath.length,\r\n\t\tsmooth, segment, x, y, x2, y2, i, l, a, a2, isSmooth, smoothData;\r\n\twhile (--j > -1) {\r\n\t\tsegment = rawPath[j];\r\n\t\tisSmooth = segment.isSmooth = segment.isSmooth || [0, 0, 0, 0];\r\n\t\tsmoothData = segment.smoothData = segment.smoothData || [0, 0, 0, 0];\r\n\t\tisSmooth.length = 4;\r\n\t\tl = segment.length - 2;\r\n\t\tfor (i = 6; i < l; i += 6) {\r\n\t\t\tx = segment[i] - segment[i - 2];\r\n\t\t\ty = segment[i + 1] - segment[i - 1];\r\n\t\t\tx2 = segment[i + 2] - segment[i];\r\n\t\t\ty2 = segment[i + 3] - segment[i + 1];\r\n\t\t\ta = _atan2(y, x);\r\n\t\t\ta2 = _atan2(y2, x2);\r\n\t\t\tsmooth = (Math.abs(a - a2) < 0.09);\r\n\t\t\tif (smooth) {\r\n\t\t\t\tsmoothData[i - 2] = a;\r\n\t\t\t\tsmoothData[i + 2] = a2;\r\n\t\t\t\tsmoothData[i - 1] = _sqrt(x * x + y * y);\r\n\t\t\t\tsmoothData[i + 3] = _sqrt(x2 * x2 + y2 * y2);\r\n\t\t\t}\r\n\t\t\tisSmooth.push(smooth, smooth, 0, 0, smooth, smooth);\r\n\t\t}\r\n\t\t//if the first and last points are identical, check to see if there's a smooth transition. We must handle this a bit differently due to their positions in the array.\r\n\t\tif (segment[l] === segment[0] && segment[l+1] === segment[1]) {\r\n\t\t\tx = segment[0] - segment[l-2];\r\n\t\t\ty = segment[1] - segment[l-1];\r\n\t\t\tx2 = segment[2] - segment[0];\r\n\t\t\ty2 = segment[3] - segment[1];\r\n\t\t\ta = _atan2(y, x);\r\n\t\t\ta2 = _atan2(y2, x2);\r\n\t\t\tif (Math.abs(a - a2) < 0.09) {\r\n\t\t\t\tsmoothData[l-2] = a;\r\n\t\t\t\tsmoothData[2] = a2;\r\n\t\t\t\tsmoothData[l-1] = _sqrt(x * x + y * y);\r\n\t\t\t\tsmoothData[3] = _sqrt(x2 * x2 + y2 * y2);\r\n\t\t\t\tisSmooth[l-2] = isSmooth[l-1] = true; //don't change indexes 2 and 3 because we'll trigger everything from the END, and this will optimize file size a bit.\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\treturn rawPath;\r\n}\r\nexport function pointToScreen(svgElement, point) {\r\n\tif (arguments.length < 2) { //by default, take the first set of coordinates in the path as the point\r\n\t\tlet rawPath = getRawPath(svgElement);\r\n\t\tpoint = svgElement.ownerSVGElement.createSVGPoint();\r\n\t\tpoint.x = rawPath[0][0];\r\n\t\tpoint.y = rawPath[0][1];\r\n\t}\r\n\treturn point.matrixTransform(svgElement.getScreenCTM());\r\n}\r\n// takes a <path> and normalizes all of its coordinates to values between 0 and 1\r\nexport function normalizePath(path) {\r\n  path = gsap.utils.toArray(path);\r\n  if (!path[0].hasAttribute(\"d\")) {\r\n    path = gsap.utils.toArray(path[0].children);\r\n  }\r\n  if (path.length > 1) {\r\n    path.forEach(normalizePath);\r\n    return path;\r\n  }\r\n  let _svgPathExp = /[achlmqstvz]|(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\r\n      _scientific = /[\\+\\-]?\\d*\\.?\\d+e[\\+\\-]?\\d+/ig,\r\n      d = path[0].getAttribute(\"d\"),\r\n      a = d.replace(_scientific, m => { let n = +m; return (n < 0.0001 && n > -0.0001) ? 0 : n; }).match(_svgPathExp),\r\n      nums = a.filter(n => !isNaN(n)).map(n => +n),\r\n      normalize = gsap.utils.normalize(Math.min(...nums), Math.max(...nums)),\r\n      finals = a.map(val => isNaN(val) ? val : normalize(+val)),\r\n      s = \"\",\r\n      prevWasCommand;\r\n  finals.forEach((value, i) => {\r\n    let isCommand = isNaN(value)\r\n    s += (isCommand && i ? \" \" : prevWasCommand || !i ? \"\" : \",\") + value;\r\n    prevWasCommand = isCommand;\r\n  });\r\n  path[0].setAttribute(\"d\", s);\r\n}\r\n*/", "/*!\r\n * matrix 3.13.0\r\n * https://gsap.com\r\n *\r\n * Copyright 2008-2025, GreenSock. All rights reserved.\r\n * Subject to the terms at https://gsap.com/standard-license\r\n * @author: <PERSON>, <EMAIL>\r\n*/\r\n\r\n/* eslint-disable */\r\nvar _doc,\r\n    _win,\r\n    _doc<PERSON><PERSON>,\r\n    _body,\r\n    _div<PERSON><PERSON>r,\r\n    _svg<PERSON><PERSON>r,\r\n    _identityMatrix,\r\n    _gEl,\r\n    _transformProp = \"transform\",\r\n    _transformOriginProp = _transformProp + \"Origin\",\r\n    _hasOffsetBug,\r\n    _setDoc = function _setDoc(element) {\r\n  var doc = element.ownerDocument || element;\r\n\r\n  if (!(_transformProp in element.style) && \"msTransform\" in element.style) {\r\n    //to improve compatibility with old Microsoft browsers\r\n    _transformProp = \"msTransform\";\r\n    _transformOriginProp = _transformProp + \"Origin\";\r\n  }\r\n\r\n  while (doc.parentNode && (doc = doc.parentNode)) {}\r\n\r\n  _win = window;\r\n  _identityMatrix = new Matrix2D();\r\n\r\n  if (doc) {\r\n    _doc = doc;\r\n    _docElement = doc.documentElement;\r\n    _body = doc.body;\r\n    _gEl = _doc.createElementNS(\"http://www.w3.org/2000/svg\", \"g\"); // prevent any existing CSS from transforming it\r\n\r\n    _gEl.style.transform = \"none\"; // now test for the offset reporting bug. Use feature detection instead of browser sniffing to make things more bulletproof and future-proof. Hopefully Safari will fix their bug soon.\r\n\r\n    var d1 = doc.createElement(\"div\"),\r\n        d2 = doc.createElement(\"div\"),\r\n        root = doc && (doc.body || doc.firstElementChild);\r\n\r\n    if (root && root.appendChild) {\r\n      root.appendChild(d1);\r\n      d1.appendChild(d2);\r\n      d1.setAttribute(\"style\", \"position:static;transform:translate3d(0,0,1px)\");\r\n      _hasOffsetBug = d2.offsetParent !== d1;\r\n      root.removeChild(d1);\r\n    }\r\n  }\r\n\r\n  return doc;\r\n},\r\n    _forceNonZeroScale = function _forceNonZeroScale(e) {\r\n  // walks up the element's ancestors and finds any that had their scale set to 0 via GSAP, and changes them to 0.0001 to ensure that measurements work. Firefox has a bug that causes it to incorrectly report getBoundingClientRect() when scale is 0.\r\n  var a, cache;\r\n\r\n  while (e && e !== _body) {\r\n    cache = e._gsap;\r\n    cache && cache.uncache && cache.get(e, \"x\"); // force re-parsing of transforms if necessary\r\n\r\n    if (cache && !cache.scaleX && !cache.scaleY && cache.renderTransform) {\r\n      cache.scaleX = cache.scaleY = 1e-4;\r\n      cache.renderTransform(1, cache);\r\n      a ? a.push(cache) : a = [cache];\r\n    }\r\n\r\n    e = e.parentNode;\r\n  }\r\n\r\n  return a;\r\n},\r\n    // possible future addition: pass an element to _forceDisplay() and it'll walk up all its ancestors and make sure anything with display: none is set to display: block, and if there's no parentNode, it'll add it to the body. It returns an Array that you can then feed to _revertDisplay() to have it revert all the changes it made.\r\n// _forceDisplay = e => {\r\n// \tlet a = [],\r\n// \t\tparent;\r\n// \twhile (e && e !== _body) {\r\n// \t\tparent = e.parentNode;\r\n// \t\t(_win.getComputedStyle(e).display === \"none\" || !parent) && a.push(e, e.style.display, parent) && (e.style.display = \"block\");\r\n// \t\tparent || _body.appendChild(e);\r\n// \t\te = parent;\r\n// \t}\r\n// \treturn a;\r\n// },\r\n// _revertDisplay = a => {\r\n// \tfor (let i = 0; i < a.length; i+=3) {\r\n// \t\ta[i+1] ? (a[i].style.display = a[i+1]) : a[i].style.removeProperty(\"display\");\r\n// \t\ta[i+2] || a[i].parentNode.removeChild(a[i]);\r\n// \t}\r\n// },\r\n_svgTemps = [],\r\n    //we create 3 elements for SVG, and 3 for other DOM elements and cache them for performance reasons. They get nested in _divContainer and _svgContainer so that just one element is added to the DOM on each successive attempt. Again, performance is key.\r\n_divTemps = [],\r\n    _getDocScrollTop = function _getDocScrollTop() {\r\n  return _win.pageYOffset || _doc.scrollTop || _docElement.scrollTop || _body.scrollTop || 0;\r\n},\r\n    _getDocScrollLeft = function _getDocScrollLeft() {\r\n  return _win.pageXOffset || _doc.scrollLeft || _docElement.scrollLeft || _body.scrollLeft || 0;\r\n},\r\n    _svgOwner = function _svgOwner(element) {\r\n  return element.ownerSVGElement || ((element.tagName + \"\").toLowerCase() === \"svg\" ? element : null);\r\n},\r\n    _isFixed = function _isFixed(element) {\r\n  if (_win.getComputedStyle(element).position === \"fixed\") {\r\n    return true;\r\n  }\r\n\r\n  element = element.parentNode;\r\n\r\n  if (element && element.nodeType === 1) {\r\n    // avoid document fragments which will throw an error.\r\n    return _isFixed(element);\r\n  }\r\n},\r\n    _createSibling = function _createSibling(element, i) {\r\n  if (element.parentNode && (_doc || _setDoc(element))) {\r\n    var svg = _svgOwner(element),\r\n        ns = svg ? svg.getAttribute(\"xmlns\") || \"http://www.w3.org/2000/svg\" : \"http://www.w3.org/1999/xhtml\",\r\n        type = svg ? i ? \"rect\" : \"g\" : \"div\",\r\n        x = i !== 2 ? 0 : 100,\r\n        y = i === 3 ? 100 : 0,\r\n        css = \"position:absolute;display:block;pointer-events:none;margin:0;padding:0;\",\r\n        e = _doc.createElementNS ? _doc.createElementNS(ns.replace(/^https/, \"http\"), type) : _doc.createElement(type);\r\n\r\n    if (i) {\r\n      if (!svg) {\r\n        if (!_divContainer) {\r\n          _divContainer = _createSibling(element);\r\n          _divContainer.style.cssText = css;\r\n        }\r\n\r\n        e.style.cssText = css + \"width:0.1px;height:0.1px;top:\" + y + \"px;left:\" + x + \"px\";\r\n\r\n        _divContainer.appendChild(e);\r\n      } else {\r\n        _svgContainer || (_svgContainer = _createSibling(element));\r\n        e.setAttribute(\"width\", 0.01);\r\n        e.setAttribute(\"height\", 0.01);\r\n        e.setAttribute(\"transform\", \"translate(\" + x + \",\" + y + \")\");\r\n\r\n        _svgContainer.appendChild(e);\r\n      }\r\n    }\r\n\r\n    return e;\r\n  }\r\n\r\n  throw \"Need document and parent.\";\r\n},\r\n    _consolidate = function _consolidate(m) {\r\n  // replaces SVGTransformList.consolidate() because a bug in Firefox causes it to break pointer events. See https://gsap.com/forums/topic/23248-touch-is-not-working-on-draggable-in-firefox-windows-v324/?tab=comments#comment-109800\r\n  var c = new Matrix2D(),\r\n      i = 0;\r\n\r\n  for (; i < m.numberOfItems; i++) {\r\n    c.multiply(m.getItem(i).matrix);\r\n  }\r\n\r\n  return c;\r\n},\r\n    _getCTM = function _getCTM(svg) {\r\n  var m = svg.getCTM(),\r\n      transform;\r\n\r\n  if (!m) {\r\n    // Firefox returns null for getCTM() on root <svg> elements, so this is a workaround using a <g> that we temporarily append.\r\n    transform = svg.style[_transformProp];\r\n    svg.style[_transformProp] = \"none\"; // a bug in Firefox causes css transforms to contaminate the getCTM()\r\n\r\n    svg.appendChild(_gEl);\r\n    m = _gEl.getCTM();\r\n    svg.removeChild(_gEl);\r\n    transform ? svg.style[_transformProp] = transform : svg.style.removeProperty(_transformProp.replace(/([A-Z])/g, \"-$1\").toLowerCase());\r\n  }\r\n\r\n  return m || _identityMatrix.clone(); // Firefox will still return null if the <svg> has a width/height of 0 in the browser.\r\n},\r\n    _placeSiblings = function _placeSiblings(element, adjustGOffset) {\r\n  var svg = _svgOwner(element),\r\n      isRootSVG = element === svg,\r\n      siblings = svg ? _svgTemps : _divTemps,\r\n      parent = element.parentNode,\r\n      appendToEl = parent && !svg && parent.shadowRoot && parent.shadowRoot.appendChild ? parent.shadowRoot : parent,\r\n      container,\r\n      m,\r\n      b,\r\n      x,\r\n      y,\r\n      cs;\r\n\r\n  if (element === _win) {\r\n    return element;\r\n  }\r\n\r\n  siblings.length || siblings.push(_createSibling(element, 1), _createSibling(element, 2), _createSibling(element, 3));\r\n  container = svg ? _svgContainer : _divContainer;\r\n\r\n  if (svg) {\r\n    if (isRootSVG) {\r\n      b = _getCTM(element);\r\n      x = -b.e / b.a;\r\n      y = -b.f / b.d;\r\n      m = _identityMatrix;\r\n    } else if (element.getBBox) {\r\n      b = element.getBBox();\r\n      m = element.transform ? element.transform.baseVal : {}; // IE11 doesn't follow the spec.\r\n\r\n      m = !m.numberOfItems ? _identityMatrix : m.numberOfItems > 1 ? _consolidate(m) : m.getItem(0).matrix; // don't call m.consolidate().matrix because a bug in Firefox makes pointer events not work when consolidate() is called on the same tick as getBoundingClientRect()! See https://gsap.com/forums/topic/23248-touch-is-not-working-on-draggable-in-firefox-windows-v324/?tab=comments#comment-109800\r\n\r\n      x = m.a * b.x + m.c * b.y;\r\n      y = m.b * b.x + m.d * b.y;\r\n    } else {\r\n      // may be a <mask> which has no getBBox() so just use defaults instead of throwing errors.\r\n      m = new Matrix2D();\r\n      x = y = 0;\r\n    }\r\n\r\n    if (adjustGOffset && element.tagName.toLowerCase() === \"g\") {\r\n      x = y = 0;\r\n    }\r\n\r\n    (isRootSVG ? svg : parent).appendChild(container);\r\n    container.setAttribute(\"transform\", \"matrix(\" + m.a + \",\" + m.b + \",\" + m.c + \",\" + m.d + \",\" + (m.e + x) + \",\" + (m.f + y) + \")\");\r\n  } else {\r\n    x = y = 0;\r\n\r\n    if (_hasOffsetBug) {\r\n      // some browsers (like Safari) have a bug that causes them to misreport offset values. When an ancestor element has a transform applied, it's supposed to treat it as if it's position: relative (new context). Safari botches this, so we need to find the closest ancestor (between the element and its offsetParent) that has a transform applied and if one is found, grab its offsetTop/Left and subtract them to compensate.\r\n      m = element.offsetParent;\r\n      b = element;\r\n\r\n      while (b && (b = b.parentNode) && b !== m && b.parentNode) {\r\n        if ((_win.getComputedStyle(b)[_transformProp] + \"\").length > 4) {\r\n          x = b.offsetLeft;\r\n          y = b.offsetTop;\r\n          b = 0;\r\n        }\r\n      }\r\n    }\r\n\r\n    cs = _win.getComputedStyle(element);\r\n\r\n    if (cs.position !== \"absolute\" && cs.position !== \"fixed\") {\r\n      m = element.offsetParent;\r\n\r\n      while (parent && parent !== m) {\r\n        // if there's an ancestor element between the element and its offsetParent that's scrolled, we must factor that in.\r\n        x += parent.scrollLeft || 0;\r\n        y += parent.scrollTop || 0;\r\n        parent = parent.parentNode;\r\n      }\r\n    }\r\n\r\n    b = container.style;\r\n    b.top = element.offsetTop - y + \"px\";\r\n    b.left = element.offsetLeft - x + \"px\";\r\n    b[_transformProp] = cs[_transformProp];\r\n    b[_transformOriginProp] = cs[_transformOriginProp]; // b.border = m.border;\r\n    // b.borderLeftStyle = m.borderLeftStyle;\r\n    // b.borderTopStyle = m.borderTopStyle;\r\n    // b.borderLeftWidth = m.borderLeftWidth;\r\n    // b.borderTopWidth = m.borderTopWidth;\r\n\r\n    b.position = cs.position === \"fixed\" ? \"fixed\" : \"absolute\";\r\n    appendToEl.appendChild(container);\r\n  }\r\n\r\n  return container;\r\n},\r\n    _setMatrix = function _setMatrix(m, a, b, c, d, e, f) {\r\n  m.a = a;\r\n  m.b = b;\r\n  m.c = c;\r\n  m.d = d;\r\n  m.e = e;\r\n  m.f = f;\r\n  return m;\r\n};\r\n\r\nexport var Matrix2D = /*#__PURE__*/function () {\r\n  function Matrix2D(a, b, c, d, e, f) {\r\n    if (a === void 0) {\r\n      a = 1;\r\n    }\r\n\r\n    if (b === void 0) {\r\n      b = 0;\r\n    }\r\n\r\n    if (c === void 0) {\r\n      c = 0;\r\n    }\r\n\r\n    if (d === void 0) {\r\n      d = 1;\r\n    }\r\n\r\n    if (e === void 0) {\r\n      e = 0;\r\n    }\r\n\r\n    if (f === void 0) {\r\n      f = 0;\r\n    }\r\n\r\n    _setMatrix(this, a, b, c, d, e, f);\r\n  }\r\n\r\n  var _proto = Matrix2D.prototype;\r\n\r\n  _proto.inverse = function inverse() {\r\n    var a = this.a,\r\n        b = this.b,\r\n        c = this.c,\r\n        d = this.d,\r\n        e = this.e,\r\n        f = this.f,\r\n        determinant = a * d - b * c || 1e-10;\r\n    return _setMatrix(this, d / determinant, -b / determinant, -c / determinant, a / determinant, (c * f - d * e) / determinant, -(a * f - b * e) / determinant);\r\n  };\r\n\r\n  _proto.multiply = function multiply(matrix) {\r\n    var a = this.a,\r\n        b = this.b,\r\n        c = this.c,\r\n        d = this.d,\r\n        e = this.e,\r\n        f = this.f,\r\n        a2 = matrix.a,\r\n        b2 = matrix.c,\r\n        c2 = matrix.b,\r\n        d2 = matrix.d,\r\n        e2 = matrix.e,\r\n        f2 = matrix.f;\r\n    return _setMatrix(this, a2 * a + c2 * c, a2 * b + c2 * d, b2 * a + d2 * c, b2 * b + d2 * d, e + e2 * a + f2 * c, f + e2 * b + f2 * d);\r\n  };\r\n\r\n  _proto.clone = function clone() {\r\n    return new Matrix2D(this.a, this.b, this.c, this.d, this.e, this.f);\r\n  };\r\n\r\n  _proto.equals = function equals(matrix) {\r\n    var a = this.a,\r\n        b = this.b,\r\n        c = this.c,\r\n        d = this.d,\r\n        e = this.e,\r\n        f = this.f;\r\n    return a === matrix.a && b === matrix.b && c === matrix.c && d === matrix.d && e === matrix.e && f === matrix.f;\r\n  };\r\n\r\n  _proto.apply = function apply(point, decoratee) {\r\n    if (decoratee === void 0) {\r\n      decoratee = {};\r\n    }\r\n\r\n    var x = point.x,\r\n        y = point.y,\r\n        a = this.a,\r\n        b = this.b,\r\n        c = this.c,\r\n        d = this.d,\r\n        e = this.e,\r\n        f = this.f;\r\n    decoratee.x = x * a + y * c + e || 0;\r\n    decoratee.y = x * b + y * d + f || 0;\r\n    return decoratee;\r\n  };\r\n\r\n  return Matrix2D;\r\n}(); // Feed in an element and it'll return a 2D matrix (optionally inverted) so that you can translate between coordinate spaces.\r\n// Inverting lets you translate a global point into a local coordinate space. No inverting lets you go the other way.\r\n// We needed this to work around various browser bugs, like Firefox doesn't accurately report getScreenCTM() when there\r\n// are transforms applied to ancestor elements.\r\n// The matrix math to convert any x/y coordinate is as follows, which is wrapped in a convenient apply() method of Matrix2D above:\r\n//     tx = m.a * x + m.c * y + m.e\r\n//     ty = m.b * x + m.d * y + m.f\r\n\r\nexport function getGlobalMatrix(element, inverse, adjustGOffset, includeScrollInFixed) {\r\n  // adjustGOffset is typically used only when grabbing an element's PARENT's global matrix, and it ignores the x/y offset of any SVG <g> elements because they behave in a special way.\r\n  if (!element || !element.parentNode || (_doc || _setDoc(element)).documentElement === element) {\r\n    return new Matrix2D();\r\n  }\r\n\r\n  var zeroScales = _forceNonZeroScale(element),\r\n      svg = _svgOwner(element),\r\n      temps = svg ? _svgTemps : _divTemps,\r\n      container = _placeSiblings(element, adjustGOffset),\r\n      b1 = temps[0].getBoundingClientRect(),\r\n      b2 = temps[1].getBoundingClientRect(),\r\n      b3 = temps[2].getBoundingClientRect(),\r\n      parent = container.parentNode,\r\n      isFixed = !includeScrollInFixed && _isFixed(element),\r\n      m = new Matrix2D((b2.left - b1.left) / 100, (b2.top - b1.top) / 100, (b3.left - b1.left) / 100, (b3.top - b1.top) / 100, b1.left + (isFixed ? 0 : _getDocScrollLeft()), b1.top + (isFixed ? 0 : _getDocScrollTop()));\r\n\r\n  parent.removeChild(container);\r\n\r\n  if (zeroScales) {\r\n    b1 = zeroScales.length;\r\n\r\n    while (b1--) {\r\n      b2 = zeroScales[b1];\r\n      b2.scaleX = b2.scaleY = 0;\r\n      b2.renderTransform(1, b2);\r\n    }\r\n  }\r\n\r\n  return inverse ? m.inverse() : m;\r\n}\r\nexport { _getDocScrollTop, _getDocScrollLeft, _setDoc, _isFixed, _getCTM }; // export function getMatrix(element) {\r\n// \t_doc || _setDoc(element);\r\n// \tlet m = (_win.getComputedStyle(element)[_transformProp] + \"\").substr(7).match(/[-.]*\\d+[.e\\-+]*\\d*[e\\-\\+]*\\d*/g),\r\n// \t\tis2D = m && m.length === 6;\r\n// \treturn !m || m.length < 6 ? new Matrix2D() : new Matrix2D(+m[0], +m[1], +m[is2D ? 2 : 4], +m[is2D ? 3 : 5], +m[is2D ? 4 : 12], +m[is2D ? 5 : 13]);\r\n// }", "/*!\r\n * MotionPathPlugin 3.13.0\r\n * https://gsap.com\r\n *\r\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\r\n * Subject to the terms at https://gsap.com/standard-license\r\n * @author: <PERSON>, <EMAIL>\r\n*/\r\n\r\n/* eslint-disable */\r\nimport { getRawPath, cacheRawPathMeasurements, getPositionOnPath, pointsToSegment, flatPointsToSegment, sliceRawPath, stringToRawPath, rawPathToString, transformRawPath, convertToPath as _convertToPath } from \"./utils/paths.js\";\r\nimport { getGlobalMatrix } from \"./utils/matrix.js\";\r\n\r\nvar _xProps = \"x,translateX,left,marginLeft,xPercent\".split(\",\"),\r\n    _yProps = \"y,translateY,top,marginTop,yPercent\".split(\",\"),\r\n    _DEG2RAD = Math.PI / 180,\r\n    gsap,\r\n    PropTween,\r\n    _getUnit,\r\n    _toArray,\r\n    _getStyleSaver,\r\n    _reverting,\r\n    _getGSAP = function _getGSAP() {\r\n  return gsap || typeof window !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap;\r\n},\r\n    _populateSegmentFromArray = function _populateSegmentFromArray(segment, values, property, mode) {\r\n  //mode: 0 = x but don't fill y yet, 1 = y, 2 = x and fill y with 0.\r\n  var l = values.length,\r\n      si = mode === 2 ? 0 : mode,\r\n      i = 0,\r\n      v;\r\n\r\n  for (; i < l; i++) {\r\n    segment[si] = v = parseFloat(values[i][property]);\r\n    mode === 2 && (segment[si + 1] = 0);\r\n    si += 2;\r\n  }\r\n\r\n  return segment;\r\n},\r\n    _getPropNum = function _getPropNum(target, prop, unit) {\r\n  return parseFloat(target._gsap.get(target, prop, unit || \"px\")) || 0;\r\n},\r\n    _relativize = function _relativize(segment) {\r\n  var x = segment[0],\r\n      y = segment[1],\r\n      i;\r\n\r\n  for (i = 2; i < segment.length; i += 2) {\r\n    x = segment[i] += x;\r\n    y = segment[i + 1] += y;\r\n  }\r\n},\r\n    // feed in an array of quadratic bezier points like [{x: 0, y: 0}, ...] and it'll convert it to cubic bezier\r\n// _quadToCubic = points => {\r\n// \tlet cubic = [],\r\n// \t\tl = points.length - 1,\r\n// \t\ti = 1,\r\n// \t\ta, b, c;\r\n// \tfor (; i < l; i+=2) {\r\n// \t\ta = points[i-1];\r\n// \t\tb = points[i];\r\n// \t\tc = points[i+1];\r\n// \t\tcubic.push(a, {x: (2 * b.x + a.x) / 3, y: (2 * b.y + a.y) / 3}, {x: (2 * b.x + c.x) / 3, y: (2 * b.y + c.y) / 3});\r\n// \t}\r\n// \tcubic.push(points[l]);\r\n// \treturn cubic;\r\n// },\r\n_segmentToRawPath = function _segmentToRawPath(plugin, segment, target, x, y, slicer, vars, unitX, unitY) {\r\n  if (vars.type === \"cubic\") {\r\n    segment = [segment];\r\n  } else {\r\n    vars.fromCurrent !== false && segment.unshift(_getPropNum(target, x, unitX), y ? _getPropNum(target, y, unitY) : 0);\r\n    vars.relative && _relativize(segment);\r\n    var pointFunc = y ? pointsToSegment : flatPointsToSegment;\r\n    segment = [pointFunc(segment, vars.curviness)];\r\n  }\r\n\r\n  segment = slicer(_align(segment, target, vars));\r\n\r\n  _addDimensionalPropTween(plugin, target, x, segment, \"x\", unitX);\r\n\r\n  y && _addDimensionalPropTween(plugin, target, y, segment, \"y\", unitY);\r\n  return cacheRawPathMeasurements(segment, vars.resolution || (vars.curviness === 0 ? 20 : 12)); //when curviness is 0, it creates control points right on top of the anchors which makes it more sensitive to resolution, thus we change the default accordingly.\r\n},\r\n    _emptyFunc = function _emptyFunc(v) {\r\n  return v;\r\n},\r\n    _numExp = /[-+\\.]*\\d+\\.?(?:e-|e\\+)?\\d*/g,\r\n    _originToPoint = function _originToPoint(element, origin, parentMatrix) {\r\n  // origin is an array of normalized values (0-1) in relation to the width/height, so [0.5, 0.5] would be the center. It can also be \"auto\" in which case it will be the top left unless it's a <path>, when it will start at the beginning of the path itself.\r\n  var m = getGlobalMatrix(element),\r\n      x = 0,\r\n      y = 0,\r\n      svg;\r\n\r\n  if ((element.tagName + \"\").toLowerCase() === \"svg\") {\r\n    svg = element.viewBox.baseVal;\r\n    svg.width || (svg = {\r\n      width: +element.getAttribute(\"width\"),\r\n      height: +element.getAttribute(\"height\")\r\n    });\r\n  } else {\r\n    svg = origin && element.getBBox && element.getBBox();\r\n  }\r\n\r\n  if (origin && origin !== \"auto\") {\r\n    x = origin.push ? origin[0] * (svg ? svg.width : element.offsetWidth || 0) : origin.x;\r\n    y = origin.push ? origin[1] * (svg ? svg.height : element.offsetHeight || 0) : origin.y;\r\n  }\r\n\r\n  return parentMatrix.apply(x || y ? m.apply({\r\n    x: x,\r\n    y: y\r\n  }) : {\r\n    x: m.e,\r\n    y: m.f\r\n  });\r\n},\r\n    _getAlignMatrix = function _getAlignMatrix(fromElement, toElement, fromOrigin, toOrigin) {\r\n  var parentMatrix = getGlobalMatrix(fromElement.parentNode, true, true),\r\n      m = parentMatrix.clone().multiply(getGlobalMatrix(toElement)),\r\n      fromPoint = _originToPoint(fromElement, fromOrigin, parentMatrix),\r\n      _originToPoint2 = _originToPoint(toElement, toOrigin, parentMatrix),\r\n      x = _originToPoint2.x,\r\n      y = _originToPoint2.y,\r\n      p;\r\n\r\n  m.e = m.f = 0;\r\n\r\n  if (toOrigin === \"auto\" && toElement.getTotalLength && toElement.tagName.toLowerCase() === \"path\") {\r\n    p = toElement.getAttribute(\"d\").match(_numExp) || [];\r\n    p = m.apply({\r\n      x: +p[0],\r\n      y: +p[1]\r\n    });\r\n    x += p.x;\r\n    y += p.y;\r\n  } //if (p || (toElement.getBBox && fromElement.getBBox && toElement.ownerSVGElement === fromElement.ownerSVGElement)) {\r\n\r\n\r\n  if (p) {\r\n    p = m.apply(toElement.getBBox());\r\n    x -= p.x;\r\n    y -= p.y;\r\n  }\r\n\r\n  m.e = x - fromPoint.x;\r\n  m.f = y - fromPoint.y;\r\n  return m;\r\n},\r\n    _align = function _align(rawPath, target, _ref) {\r\n  var align = _ref.align,\r\n      matrix = _ref.matrix,\r\n      offsetX = _ref.offsetX,\r\n      offsetY = _ref.offsetY,\r\n      alignOrigin = _ref.alignOrigin;\r\n\r\n  var x = rawPath[0][0],\r\n      y = rawPath[0][1],\r\n      curX = _getPropNum(target, \"x\"),\r\n      curY = _getPropNum(target, \"y\"),\r\n      alignTarget,\r\n      m,\r\n      p;\r\n\r\n  if (!rawPath || !rawPath.length) {\r\n    return getRawPath(\"M0,0L0,0\");\r\n  }\r\n\r\n  if (align) {\r\n    if (align === \"self\" || (alignTarget = _toArray(align)[0] || target) === target) {\r\n      transformRawPath(rawPath, 1, 0, 0, 1, curX - x, curY - y);\r\n    } else {\r\n      if (alignOrigin && alignOrigin[2] !== false) {\r\n        gsap.set(target, {\r\n          transformOrigin: alignOrigin[0] * 100 + \"% \" + alignOrigin[1] * 100 + \"%\"\r\n        });\r\n      } else {\r\n        alignOrigin = [_getPropNum(target, \"xPercent\") / -100, _getPropNum(target, \"yPercent\") / -100];\r\n      }\r\n\r\n      m = _getAlignMatrix(target, alignTarget, alignOrigin, \"auto\");\r\n      p = m.apply({\r\n        x: x,\r\n        y: y\r\n      });\r\n      transformRawPath(rawPath, m.a, m.b, m.c, m.d, curX + m.e - (p.x - m.e), curY + m.f - (p.y - m.f));\r\n    }\r\n  }\r\n\r\n  if (matrix) {\r\n    transformRawPath(rawPath, matrix.a, matrix.b, matrix.c, matrix.d, matrix.e, matrix.f);\r\n  } else if (offsetX || offsetY) {\r\n    transformRawPath(rawPath, 1, 0, 0, 1, offsetX || 0, offsetY || 0);\r\n  }\r\n\r\n  return rawPath;\r\n},\r\n    _addDimensionalPropTween = function _addDimensionalPropTween(plugin, target, property, rawPath, pathProperty, forceUnit) {\r\n  var cache = target._gsap,\r\n      harness = cache.harness,\r\n      alias = harness && harness.aliases && harness.aliases[property],\r\n      prop = alias && alias.indexOf(\",\") < 0 ? alias : property,\r\n      pt = plugin._pt = new PropTween(plugin._pt, target, prop, 0, 0, _emptyFunc, 0, cache.set(target, prop, plugin));\r\n  pt.u = _getUnit(cache.get(target, prop, forceUnit)) || 0;\r\n  pt.path = rawPath;\r\n  pt.pp = pathProperty;\r\n\r\n  plugin._props.push(prop);\r\n},\r\n    _sliceModifier = function _sliceModifier(start, end) {\r\n  return function (rawPath) {\r\n    return start || end !== 1 ? sliceRawPath(rawPath, start, end) : rawPath;\r\n  };\r\n};\r\n\r\nexport var MotionPathPlugin = {\r\n  version: \"3.13.0\",\r\n  name: \"motionPath\",\r\n  register: function register(core, Plugin, propTween) {\r\n    gsap = core;\r\n    _getUnit = gsap.utils.getUnit;\r\n    _toArray = gsap.utils.toArray;\r\n    _getStyleSaver = gsap.core.getStyleSaver;\r\n\r\n    _reverting = gsap.core.reverting || function () {};\r\n\r\n    PropTween = propTween;\r\n  },\r\n  init: function init(target, vars, tween) {\r\n    if (!gsap) {\r\n      console.warn(\"Please gsap.registerPlugin(MotionPathPlugin)\");\r\n      return false;\r\n    }\r\n\r\n    if (!(typeof vars === \"object\" && !vars.style) || !vars.path) {\r\n      vars = {\r\n        path: vars\r\n      };\r\n    }\r\n\r\n    var rawPaths = [],\r\n        _vars = vars,\r\n        path = _vars.path,\r\n        autoRotate = _vars.autoRotate,\r\n        unitX = _vars.unitX,\r\n        unitY = _vars.unitY,\r\n        x = _vars.x,\r\n        y = _vars.y,\r\n        firstObj = path[0],\r\n        slicer = _sliceModifier(vars.start, \"end\" in vars ? vars.end : 1),\r\n        rawPath,\r\n        p;\r\n\r\n    this.rawPaths = rawPaths;\r\n    this.target = target;\r\n    this.tween = tween;\r\n    this.styles = _getStyleSaver && _getStyleSaver(target, \"transform\");\r\n\r\n    if (this.rotate = autoRotate || autoRotate === 0) {\r\n      //get the rotational data FIRST so that the setTransform() method is called in the correct order in the render() loop - rotation gets set last.\r\n      this.rOffset = parseFloat(autoRotate) || 0;\r\n      this.radians = !!vars.useRadians;\r\n      this.rProp = vars.rotation || \"rotation\"; // rotation property\r\n\r\n      this.rSet = target._gsap.set(target, this.rProp, this); // rotation setter\r\n\r\n      this.ru = _getUnit(target._gsap.get(target, this.rProp)) || 0; // rotation units\r\n    }\r\n\r\n    if (Array.isArray(path) && !(\"closed\" in path) && typeof firstObj !== \"number\") {\r\n      for (p in firstObj) {\r\n        if (!x && ~_xProps.indexOf(p)) {\r\n          x = p;\r\n        } else if (!y && ~_yProps.indexOf(p)) {\r\n          y = p;\r\n        }\r\n      }\r\n\r\n      if (x && y) {\r\n        //correlated values\r\n        rawPaths.push(_segmentToRawPath(this, _populateSegmentFromArray(_populateSegmentFromArray([], path, x, 0), path, y, 1), target, x, y, slicer, vars, unitX || _getUnit(path[0][x]), unitY || _getUnit(path[0][y])));\r\n      } else {\r\n        x = y = 0;\r\n      }\r\n\r\n      for (p in firstObj) {\r\n        p !== x && p !== y && rawPaths.push(_segmentToRawPath(this, _populateSegmentFromArray([], path, p, 2), target, p, 0, slicer, vars, _getUnit(path[0][p])));\r\n      }\r\n    } else {\r\n      rawPath = slicer(_align(getRawPath(vars.path), target, vars));\r\n      cacheRawPathMeasurements(rawPath, vars.resolution);\r\n      rawPaths.push(rawPath);\r\n\r\n      _addDimensionalPropTween(this, target, vars.x || \"x\", rawPath, \"x\", vars.unitX || \"px\");\r\n\r\n      _addDimensionalPropTween(this, target, vars.y || \"y\", rawPath, \"y\", vars.unitY || \"px\");\r\n    }\r\n\r\n    tween.vars.immediateRender && this.render(tween.progress(), this);\r\n  },\r\n  render: function render(ratio, data) {\r\n    var rawPaths = data.rawPaths,\r\n        i = rawPaths.length,\r\n        pt = data._pt;\r\n\r\n    if (data.tween._time || !_reverting()) {\r\n      if (ratio > 1) {\r\n        ratio = 1;\r\n      } else if (ratio < 0) {\r\n        ratio = 0;\r\n      }\r\n\r\n      while (i--) {\r\n        getPositionOnPath(rawPaths[i], ratio, !i && data.rotate, rawPaths[i]);\r\n      }\r\n\r\n      while (pt) {\r\n        pt.set(pt.t, pt.p, pt.path[pt.pp] + pt.u, pt.d, ratio);\r\n        pt = pt._next;\r\n      }\r\n\r\n      data.rotate && data.rSet(data.target, data.rProp, rawPaths[0].angle * (data.radians ? _DEG2RAD : 1) + data.rOffset + data.ru, data, ratio);\r\n    } else {\r\n      data.styles.revert();\r\n    }\r\n  },\r\n  getLength: function getLength(path) {\r\n    return cacheRawPathMeasurements(getRawPath(path)).totalLength;\r\n  },\r\n  sliceRawPath: sliceRawPath,\r\n  getRawPath: getRawPath,\r\n  pointsToSegment: pointsToSegment,\r\n  stringToRawPath: stringToRawPath,\r\n  rawPathToString: rawPathToString,\r\n  transformRawPath: transformRawPath,\r\n  getGlobalMatrix: getGlobalMatrix,\r\n  getPositionOnPath: getPositionOnPath,\r\n  cacheRawPathMeasurements: cacheRawPathMeasurements,\r\n  convertToPath: function convertToPath(targets, swap) {\r\n    return _toArray(targets).map(function (target) {\r\n      return _convertToPath(target, swap !== false);\r\n    });\r\n  },\r\n  convertCoordinates: function convertCoordinates(fromElement, toElement, point) {\r\n    var m = getGlobalMatrix(toElement, true, true).multiply(getGlobalMatrix(fromElement));\r\n    return point ? m.apply(point) : m;\r\n  },\r\n  getAlignMatrix: _getAlignMatrix,\r\n  getRelativePosition: function getRelativePosition(fromElement, toElement, fromOrigin, toOrigin) {\r\n    var m = _getAlignMatrix(fromElement, toElement, fromOrigin, toOrigin);\r\n\r\n    return {\r\n      x: m.e,\r\n      y: m.f\r\n    };\r\n  },\r\n  arrayToRawPath: function arrayToRawPath(value, vars) {\r\n    vars = vars || {};\r\n\r\n    var segment = _populateSegmentFromArray(_populateSegmentFromArray([], value, vars.x || \"x\", 0), value, vars.y || \"y\", 1);\r\n\r\n    vars.relative && _relativize(segment);\r\n    return [vars.type === \"cubic\" ? segment : pointsToSegment(segment, vars.curviness)];\r\n  }\r\n};\r\n_getGSAP() && gsap.registerPlugin(MotionPathPlugin);\r\nexport { MotionPathPlugin as default };"], "mappings": ";;;AAUA,IAAI,cAAc;AAAlB,IACI,cAAc;AADlB,IAEI,cAAc;AAFlB,IAGI,eAAe;AAHnB,IAII,WAAW,KAAK,KAAK;AAJzB,IAKI,WAAW,MAAM,KAAK;AAL1B,IAMI,OAAO,KAAK;AANhB,IAOI,OAAO,KAAK;AAPhB,IAQI,OAAO,KAAK;AARhB,IASI,QAAQ,KAAK;AATjB,IAUI,SAAS,KAAK;AAVlB,IAWI,YAAY;AAXhB,IAYI,YAAY,SAASA,WAAU,OAAO;AACxC,SAAO,OAAO,UAAU;AAC1B;AAdA,IAeI,YAAY,SAASC,WAAU,OAAO;AACxC,SAAO,OAAO,UAAU;AAC1B;AAjBA,IAkBI,eAAe,SAASC,cAAa,OAAO;AAC9C,SAAO,OAAO,UAAU;AAC1B;AApBA,IAqBI,QAAQ,CAAC;AArBb,IAsBI,SAAS,CAAC;AAtBd,IAuBI,eAAe;AAvBnB,IAwBI,gBAAgB,SAASC,eAAc,UAAU;AACnD,SAAO,KAAK,OAAO,WAAW,aAAa,IAAI,YAAY,IAAI,iBAAiB,WAAW,IAAI,IAAI;AACrG;AA1BA,IA4BA,SAAS,SAASC,QAAO,OAAO;AAC9B,SAAO,KAAK,MAAM,QAAQ,YAAY,IAAI,gBAAgB;AAC5D;AA9BA,IA+BI,gBAAgB,SAASC,eAAc,OAAO;AAChD,SAAO,KAAK,MAAM,QAAQ,IAAI,IAAI,QAAQ;AAC5C;AAjCA,IAkCI,gBAAgB,SAASC,eAAc,SAAS,UAAU,GAAG,GAAG;AAClE,MAAI,UAAU,QAAQ,QAAQ,GAC1B,QAAQ,MAAM,IAAI,IAAI,iBAAiB,SAAS,GAAG,CAAC;AAExD,OAAK,SAAS,CAAC,MAAM,QAAQ,IAAI,IAAI,QAAQ,QAAQ;AACnD,YAAQ,OAAO,UAAU,GAAG,QAAQ,MAAM,GAAG,IAAI,QAAQ,CAAC,CAAC;AAC3D,YAAQ,OAAO,GAAG,IAAI,KAAK;AAC3B,WAAO;AAAA,EACT;AACF;AA3CA,IA4CI,kBAAkB,SAASC,iBAAgB,SAAS,QAAQ,UAAU;AAExE,MAAI,IAAI,QAAQ,QACZ,IAAI,CAAC,EAAE,WAAW;AAEtB,MAAI,QAAQ,CAAC,IAAI,QAAQ;AACvB,WAAO,EAAE,KAAK,QAAQ,CAAC,IAAI,QAAQ;AAAA,IAAC;AAEpC,QAAI,MAAM,IAAI;AAAA,EAChB,OAAO;AACL,WAAO,QAAQ,EAAE,CAAC,IAAI,UAAU,IAAI,GAAG;AAAA,IAAC;AAAA,EAC1C;AAEA,SAAO,IAAI,IAAI,IAAI,IAAI;AACzB;AA1DA,IA2DI,kBAAkB,SAASC,iBAAgB,SAAS,WAAW;AACjE,MAAI,IAAI,QAAQ;AAChB,eAAa,QAAQ,QAAQ;AAE7B,SAAO,KAAK;AACV,YAAQ,CAAC,EAAE,YAAY,eAAe,QAAQ,CAAC,CAAC;AAAA,EAClD;AACF;AAlEA,IAmEI,gBAAgB,SAASC,eAAc,QAAQ,MAAM;AACvD,OAAK,cAAc,OAAO;AAE1B,MAAI,OAAO,SAAS;AAElB,SAAK,UAAU,OAAO,QAAQ,MAAM,CAAC;AACrC,SAAK,SAAS,OAAO,OAAO,MAAM,CAAC;AACnC,SAAK,YAAY,OAAO;AACxB,SAAK,aAAa,OAAO;AAAA,EAC3B,WAAW,OAAO,aAAa;AAE7B,SAAK,cAAc,OAAO;AAAA,EAC5B;AAEA,SAAO;AACT;AAlFA,IAoFA,iBAAiB,SAASC,gBAAe,SAAS,SAAS;AACzD,MAAI,QAAQ,QAAQ,QAChB,UAAU,QAAQ,QAAQ,CAAC,KAAK,CAAC,GACjC,IAAI,QAAQ;AAEhB,MAAI,SAAS,QAAQ,CAAC,MAAM,QAAQ,IAAI,CAAC,KAAK,QAAQ,CAAC,MAAM,QAAQ,IAAI,CAAC,GAAG;AAC3E,cAAU,QAAQ,OAAO,QAAQ,MAAM,CAAC,CAAC;AACzC;AAAA,EACF;AAEA,UAAQ,KAAK,IAAI;AACnB;AAWO,SAAS,WAAW,OAAO;AAChC,UAAQ,UAAU,KAAK,KAAK,aAAa,KAAK,KAAK,IAAI,SAAS,cAAc,KAAK,KAAK,QAAQ;AAChG,MAAI,IAAI,MAAM,eAAe,QAAQ,GACjC;AAEJ,MAAI,MAAM,QAAQ,MAAM,aAAa,GAAG,IAAI;AAE1C,QAAI,CAAC,EAAE,SAAS;AACd,QAAE,UAAU,CAAC;AAAA,IACf;AAEA,cAAU,EAAE,QAAQ,KAAK;AACzB,WAAO,WAAW,CAAC,QAAQ,SAAS,UAAU,EAAE,QAAQ,KAAK,IAAI,gBAAgB,KAAK;AAAA,EACxF;AAEA,SAAO,CAAC,QAAQ,QAAQ,KAAK,uDAAuD,IAAI,UAAU,KAAK,IAAI,gBAAgB,KAAK,IAAI,UAAU,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI;AACtK;AAEO,SAAS,YAAY,SAAS;AACnC,MAAI,IAAI,CAAC,GACL,IAAI;AAER,SAAO,IAAI,QAAQ,QAAQ,KAAK;AAC9B,MAAE,CAAC,IAAI,cAAc,QAAQ,CAAC,GAAG,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC;AAAA,EACtD;AAEA,SAAO,cAAc,SAAS,CAAC;AACjC;AACO,SAAS,eAAe,SAAS;AACtC,MAAI,IAAI,GACJ;AACJ,UAAQ,QAAQ;AAEhB,SAAO,IAAI,QAAQ,QAAQ,KAAK,GAAG;AACjC,QAAI,QAAQ,CAAC;AACb,YAAQ,CAAC,IAAI,QAAQ,IAAI,CAAC;AAC1B,YAAQ,IAAI,CAAC,IAAI;AAAA,EACnB;AAEA,UAAQ,WAAW,CAAC,QAAQ;AAC9B;AAEA,IAAI,cAAc,SAASC,aAAY,GAAG,QAAQ;AAChD,MAAI,OAAO,SAAS,gBAAgB,8BAA8B,MAAM,GACpE,OAAO,CAAC,EAAE,MAAM,KAAK,EAAE,UAAU,GACjC,IAAI,KAAK,QACT;AACJ,WAAS,MAAM,SAAS;AAExB,SAAO,EAAE,IAAI,IAAI;AACf,WAAO,KAAK,CAAC,EAAE,SAAS,YAAY;AAEpC,QAAI,OAAO,QAAQ,MAAM,OAAO,GAAG,IAAI,GAAG;AACxC,WAAK,eAAe,MAAM,MAAM,KAAK,CAAC,EAAE,SAAS;AAAA,IACnD;AAAA,EACF;AAEA,SAAO;AACT;AAhBA,IAiBI,aAAa;AAAA,EACf,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,MAAM;AACR;AAtBA,IAuBI,aAAa,SAASC,YAAW,GAAG,OAAO;AAC7C,MAAI,QAAQ,QAAQ,MAAM,MAAM,GAAG,IAAI,CAAC,GACpC,MAAM,CAAC,GACP,IAAI,MAAM;AAEd,SAAO,EAAE,IAAI,IAAI;AACf,QAAI,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,aAAa,MAAM,CAAC,CAAC,KAAK;AAAA,EAC/C;AAEA,SAAO;AACT;AAGO,SAAS,cAAc,SAAS,MAAM;AAC3C,MAAI,OAAO,QAAQ,QAAQ,YAAY,GACnC,OAAO,gBACP,MACA,GACA,GACA,GACA,IACA,MACA,OACA,QACA,QACA,GACA,GACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA;AAEJ,MAAI,SAAS,UAAU,CAAC,QAAQ,SAAS;AACvC,WAAO;AAAA,EACT;AAEA,SAAO,YAAY,SAAS,mDAAmD;AAC/E,SAAO,WAAW,SAAS,WAAW,IAAI,CAAC;AAE3C,MAAI,SAAS,QAAQ;AACnB,QAAI,KAAK;AACT,SAAK,KAAK,MAAM;AAChB,QAAI,KAAK;AACT,QAAI,KAAK;AACT,QAAI,KAAK,QAAQ,IAAI;AACrB,QAAI,KAAK,SAAS,KAAK;AAEvB,QAAI,KAAK,IAAI;AAEX,WAAK,IAAI,KAAK,IAAI;AAClB,WAAK,IAAI;AACT,WAAK,KAAK;AACV,WAAK,KAAK,IAAI;AACd,WAAK,KAAK;AACV,WAAK,IAAI,MAAM,IAAI;AACnB,WAAK,IAAI;AACT,WAAK,KAAK;AACV,WAAK,KAAK,KAAK;AACf,WAAK,KAAK;AACV,aAAO,MAAM,KAAK,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,KAAK,MAAM,GAAG,IAAI,MAAM,KAAK,MAAM,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,MAAM,KAAK,MAAM,GAAG,GAAG,MAAM,KAAK,MAAM,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,KAAK,MAAM,GAAG,GAAG,MAAM,KAAK,MAAM,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,EAAE,EAAE,KAAK,GAAG,IAAI;AAAA,IAC3T,OAAO;AACL,aAAO,OAAO,IAAI,KAAK,MAAM,IAAI,OAAO,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,OAAO,IAAI;AAAA,IACjF;AAAA,EACF,WAAW,SAAS,YAAY,SAAS,WAAW;AAClD,QAAI,SAAS,UAAU;AACrB,UAAI,KAAK,KAAK;AACd,eAAS,IAAI;AAAA,IACf,OAAO;AACL,UAAI,KAAK;AACT,WAAK,KAAK;AACV,eAAS,KAAK;AAAA,IAChB;AAEA,QAAI,KAAK;AACT,QAAI,KAAK;AACT,YAAQ,IAAI;AACZ,WAAO,OAAO,IAAI,KAAK,MAAM,IAAI,OAAO,CAAC,IAAI,GAAG,IAAI,QAAQ,IAAI,OAAO,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,QAAQ,IAAI,OAAO,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAI,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI;AAAA,EACzP,WAAW,SAAS,QAAQ;AAC1B,WAAO,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,OAAO,KAAK,KAAK,MAAM,KAAK;AAAA,EACrE,WAAW,SAAS,cAAc,SAAS,WAAW;AACpD,cAAU,QAAQ,aAAa,QAAQ,IAAI,IAAI,MAAM,WAAW,KAAK,CAAC;AACtE,QAAI,OAAO,MAAM;AACjB,QAAI,OAAO,MAAM;AACjB,WAAO,MAAM,IAAI,MAAM,IAAI,OAAO,OAAO,KAAK,GAAG;AAEjD,QAAI,SAAS,WAAW;AACtB,cAAQ,MAAM,IAAI,MAAM,IAAI;AAAA,IAC9B;AAAA,EACF;AAEA,OAAK,aAAa,KAAK,gBAAgB,KAAK,aAAa,gBAAgB,IAAI,CAAC,CAAC;AAE/E,MAAI,QAAQ,QAAQ,YAAY;AAC9B,YAAQ,WAAW,aAAa,MAAM,OAAO;AAC7C,YAAQ,WAAW,YAAY,OAAO;AAAA,EACxC;AAEA,SAAO;AACT;AAOA,SAAS,qBAAqB,SAAS,GAAG,GAAG;AAC3C,MAAI,IAAI,QAAQ,CAAC,GACb,IAAI,QAAQ,IAAI,CAAC,GACjB,IAAI,QAAQ,IAAI,CAAC,GACjB;AACJ,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,KAAK;AACf,MAAI,KAAK,KAAK,QAAQ,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI;AACjD,MAAI,QAAQ,IAAI,CAAC;AACjB,MAAI,QAAQ,IAAI,CAAC;AACjB,MAAI,QAAQ,IAAI,CAAC;AACjB,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,KAAK;AACf,SAAO,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI,QAAQ;AACpF;AAEO,SAAS,aAAa,SAAS,OAAO,KAAK;AAChD,QAAM,aAAa,GAAG,IAAI,IAAI,cAAc,GAAG,KAAK;AAEpD,UAAQ,cAAc,KAAK,KAAK;AAChC,MAAI,QAAQ,KAAK,IAAI,GAAG,CAAC,EAAE,KAAK,MAAM,KAAK,IAAI,KAAK,GAChD,OAAO,YAAY,OAAO;AAE9B,MAAI,QAAQ,KAAK;AACf,YAAQ,IAAI;AACZ,UAAM,IAAI;AAEV,oBAAgB,IAAI;AAEpB,SAAK,cAAc;AAAA,EACrB;AAEA,MAAI,QAAQ,KAAK,MAAM,GAAG;AACxB,QAAI,SAAS,KAAK,IAAI,CAAC,CAAC,KAAK,IAAI,OAAO,GAAG,CAAC,IAAI;AAChD,aAAS;AACT,WAAO;AAAA,EACT;AAEA,OAAK,eAAe,yBAAyB,IAAI;AACjD,MAAI,OAAO,MAAM,GACb,IAAI,gBAAgB,MAAM,OAAO,OAAO,IAAI,GAC5C,IAAI,gBAAgB,MAAM,KAAK,MAAM,GACrC,OAAO,EAAE,SACT,OAAO,EAAE,SACT,YAAY,EAAE,UACd,YAAY,EAAE,UACd,KAAK,EAAE,GACP,KAAK,EAAE,GACP,cAAc,cAAc,WAC5B,aAAa,OAAO,MAAM,aAC1B,aACA,QACA,QACA,GACA,MACA,eACA,GACA;AAEJ,MAAI,QAAQ,OAAO;AACjB,kBAAc,YAAY,aAAa,eAAe,KAAK,MAAM,cAAc,EAAE,IAAI,EAAE;AAEvF,QAAI,cAAc,MAAM,WAAW,IAAI,EAAE,CAAC,GAAG;AAC3C;AAEA,UAAI,CAAC,aAAa;AAChB;AAEA,YAAI,YAAY;AACd,YAAE,KAAK,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE;AAC3B,eAAK;AAAA,QACP,WAAW,aAAa;AACtB,gBAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,QAAI,KAAK,IAAI,KAAK,MAAM,MAAM,IAAI,MAAM;AACtC,kBAAY,YAAY;AAAA,IAC1B,WAAW,CAAC,EAAE,KAAK,WAAW;AAC5B;AAAA,IACF,WAAW,cAAc,MAAM,WAAW,IAAI,EAAE,CAAC,KAAK,aAAa;AACjE;AAAA,IACF;AAEA,QAAI,EAAE,MAAM,GAAG;AACb,mBAAa,YAAY,KAAK,KAAK;AAAA,IACrC;AAEA,WAAO,CAAC;AACR,oBAAgB,KAAK;AACrB,QAAI,IAAI,gBAAgB;AACxB,QAAI;AACJ,UAAM,gBAAgB,YAAY,aAAa;AAE/C,SAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,qBAAe,MAAM,KAAK,MAAM,aAAa,CAAC;AAAA,IAChD;AAEA,WAAO;AAAA,EACT,OAAO;AACL,aAAS,EAAE,MAAM,IAAI,IAAI,iBAAiB,MAAM,IAAI,EAAE,CAAC;AAEvD,QAAI,UAAU,KAAK;AACjB,eAAS,iBAAiB,MAAM,IAAI,aAAa,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AAChE,sBAAgB,UAAU;AAC1B,WAAK,OAAO,KAAK,SAAS,CAAC;AAC3B,OAAC,UAAU,OAAO,KAAK,OAAO,GAAG,KAAK,MAAM;AAC5C,UAAI,KAAK;AAET,aAAO,KAAK;AAEV,SAAC,IAAI,aAAa,IAAI,cAAc,KAAK,OAAO,GAAG,CAAC;AAAA,MACtD;AAAA,IACF,OAAO;AACL,WAAK,QAAQ,qBAAqB,MAAM,KAAK,QAAQ,CAAC;AAEtD,YAAM;AACN,UAAI,KAAK,EAAE;AACX,UAAI,KAAK,KAAK,CAAC;AACf,WAAK,SAAS,KAAK,cAAc;AACjC,WAAK,cAAc,KAAK,cAAc;AACtC,WAAK,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,IAClC;AAAA,EACF;AAEA,OAAK,cAAc;AACnB,SAAO;AACT;AAEA,SAAS,eAAe,SAAS,YAAY,WAAW;AACtD,eAAa,cAAc;AAE3B,MAAI,CAAC,QAAQ,SAAS;AACpB,YAAQ,UAAU,CAAC;AACnB,YAAQ,SAAS,CAAC;AAAA,EACpB;AAEA,MAAI,aAAa,CAAC,CAAC,QAAQ,cAAc,IACrC,MAAM,IAAI,YACV,WAAW,YAAY,aAAa,YAAY,IAAI,IAAI,QAAQ,QAChE,KAAK,QAAQ,UAAU,GACvB,KAAK,QAAQ,aAAa,CAAC,GAC3B,eAAe,aAAa,aAAa,IAAI,aAAa,GAC1D,UAAU,QAAQ,SAClB,SAAS,QAAQ,QACjB,OAAO,aAAa,QAAQ,YAAY,cAAc,WACtD,aAAa,QAAQ,eAAe,YAAY,aAAa,CAAC,GAC9D,SAAS,aAAa,QAAQ,eAAe,CAAC,IAAI,GAClD,GACA,GACA,IACA,IACA,IACA,IACA,KACA,IACA,IACA,IACA,IACA,KACA,KACA,GACA,aACA,GACA;AACJ,UAAQ,SAAS,OAAO,SAAS;AAEjC,OAAK,IAAI,aAAa,GAAG,IAAI,UAAU,KAAK,GAAG;AAC7C,SAAK,QAAQ,IAAI,CAAC,IAAI;AACtB,SAAK,QAAQ,IAAI,CAAC,IAAI;AACtB,SAAK,QAAQ,CAAC,IAAI;AAClB,SAAK,QAAQ,IAAI,CAAC,IAAI;AACtB,SAAK,QAAQ,IAAI,CAAC,IAAI;AACtB,SAAK,QAAQ,IAAI,CAAC,IAAI;AACtB,SAAK,MAAM,KAAK,MAAM;AAEtB,QAAI,KAAK,EAAE,IAAI,QAAO,KAAK,EAAE,IAAI,QAAO,KAAK,EAAE,IAAI,KAAK,EAAE,IAAI,MAAK;AAEjE,UAAI,QAAQ,SAAS,GAAG;AACtB,gBAAQ,OAAO,GAAG,CAAC;AACnB,aAAK;AACL,oBAAY;AAAA,MACd;AAAA,IACF,OAAO;AACL,WAAK,IAAI,GAAG,KAAK,YAAY,KAAK;AAChC,YAAI,MAAM;AACV,cAAM,IAAI;AACV,aAAK,OAAO,OAAO,IAAI,IAAI,KAAK,IAAI,OAAO,IAAI,KAAK,MAAM,OAAO;AACjE,aAAK,OAAO,OAAO,IAAI,IAAI,KAAK,IAAI,OAAO,IAAI,KAAK,MAAM,OAAO;AACjE,YAAI,MAAM,KAAK,KAAK,KAAK,EAAE;AAE3B,YAAI,IAAI,KAAK;AACX,gBAAM;AAAA,QACR;AAEA,kBAAU;AACV,gBAAQ,cAAc,IAAI;AAAA,MAC5B;AAAA,IACF;AAEA,UAAM;AACN,UAAM;AAAA,EACR;AAEA,MAAI,YAAY;AACd,kBAAc;AAEd,WAAO,eAAe,QAAQ,QAAQ,gBAAgB;AACpD,cAAQ,YAAY,KAAK;AAAA,IAC3B;AAAA,EACF;AAEA,MAAI,QAAQ,UAAU,KAAK;AACzB,YAAQ,cAAc,YAAY,QAAQ,QAAQ,SAAS,CAAC,KAAK;AACjE,YAAQ,YAAY;AAEpB,QAAI,YAAY,MAAM,MAAM;AAE1B,UAAI,cAAc;AAElB,WAAK,IAAI,GAAG,IAAI,WAAW,KAAK,KAAK;AACnC,eAAO,GAAG,IAAI,QAAQ,WAAW,IAAI,IAAI,EAAE,cAAc;AAAA,MAC3D;AAAA,IACF;AAAA,EACF,OAAO;AACL,YAAQ,cAAc,QAAQ,CAAC,IAAI;AAAA,EACrC;AAEA,SAAO,aAAa,SAAS,QAAQ,aAAa,IAAI,CAAC,IAAI;AAC7D;AAEO,SAAS,yBAAyB,SAAS,YAAY;AAC5D,MAAI,YAAY,QAAQ;AAExB,OAAK,IAAI,aAAa,SAAS,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACzD,YAAQ,CAAC,EAAE,aAAa,CAAC,CAAC,cAAc;AAExC,cAAU,QAAQ,CAAC,EAAE;AACrB,kBAAc,eAAe,QAAQ,CAAC,CAAC;AAAA,EACzC;AAEA,UAAQ,cAAc;AACtB,UAAQ,cAAc;AACtB,SAAO;AACT;AAEO,SAAS,iBAAiB,SAAS,GAAG,GAAG;AAC9C,MAAI,KAAK,KAAK,KAAK,GAAG;AACpB,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,QAAQ,CAAC,GACd,KAAK,QAAQ,IAAI,CAAC,GAClB,OAAO,QAAQ,IAAI,CAAC,GACpB,OAAO,QAAQ,IAAI,CAAC,GACpB,OAAO,QAAQ,IAAI,CAAC,GACpB,OAAO,QAAQ,IAAI,CAAC,GACpB,KAAK,QAAQ,IAAI,CAAC,GAClB,KAAK,QAAQ,IAAI,CAAC,GAClB,MAAM,MAAM,OAAO,MAAM,GACzB,KAAK,QAAQ,OAAO,QAAQ,GAC5B,MAAM,MAAM,OAAO,MAAM,GACzB,KAAK,QAAQ,OAAO,QAAQ,GAC5B,KAAK,OAAO,KAAK,OAAO,GACxB,KAAK,OAAO,KAAK,OAAO,GACxB,MAAM,QAAQ,KAAK,QAAQ,GAC3B,MAAM,QAAQ,KAAK,QAAQ;AAC/B,SAAO,MAAM,MAAM;AACnB,SAAO,MAAM,MAAM;AACnB,UAAQ;AAAA,IAAO,IAAI;AAAA,IAAG;AAAA,IAAG,OAAO,GAAG;AAAA;AAAA,IACnC,OAAO,GAAG;AAAA,IAAG,OAAO,EAAE;AAAA;AAAA,IACtB,OAAO,EAAE;AAAA,IAAG,OAAO,MAAM,KAAK,MAAM,CAAC;AAAA;AAAA,IACrC,OAAO,MAAM,KAAK,MAAM,CAAC;AAAA,IAAG,OAAO,EAAE;AAAA;AAAA,IACrC,OAAO,EAAE;AAAA,IAAG,OAAO,GAAG;AAAA;AAAA,IACtB,OAAO,GAAG;AAAA,EAAC;AACX,UAAQ,WAAW,QAAQ,QAAQ,OAAO,IAAI,IAAI,QAAQ,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC7F,SAAO;AACT;AAEA,SAAS,gBAAgB,SAAS,UAAU,WAAW,mBAAmB;AACxE,cAAY,aAAa,CAAC;AAC1B,UAAQ,eAAe,yBAAyB,OAAO;AAEvD,MAAI,WAAW,KAAK,WAAW,GAAG;AAChC,eAAW,cAAc,QAAQ;AAAA,EACnC;AAEA,MAAI,WAAW,GACX,UAAU,QAAQ,CAAC,GACnB,SACA,YACA,QACA,KACA,KACA,GACA;AAEJ,MAAI,CAAC,UAAU;AACb,QAAI,IAAI,WAAW;AACnB,cAAU,QAAQ,CAAC;AAAA,EACrB,WAAW,aAAa,GAAG;AACzB,QAAI;AACJ,eAAW,QAAQ,SAAS;AAC5B,cAAU,QAAQ,QAAQ;AAC1B,QAAI,QAAQ,SAAS;AAAA,EACvB,OAAO;AACL,QAAI,QAAQ,SAAS,GAAG;AAEtB,eAAS,QAAQ,cAAc;AAC/B,YAAM,IAAI;AAEV,cAAQ,OAAO,QAAQ,GAAG,EAAE,eAAe,QAAQ;AACjD,mBAAW;AAAA,MACb;AAEA,gBAAU,QAAQ,QAAQ;AAC1B,YAAM,MAAM,QAAQ;AACpB,kBAAY,SAAS,QAAQ,MAAM,QAAQ;AAAA,IAC7C;AAEA,cAAU,QAAQ;AAClB,iBAAa,QAAQ;AAErB,aAAS,QAAQ,cAAc;AAC/B,QAAI,QAAQ,OAAO,SAAS,QAAQ,OAAO,CAAC,EAAE,SAAS,QAAQ,UAAU,KAAK,IAAI,gBAAgB,SAAS,QAAQ,QAAQ;AAC3H,UAAM,IAAI,QAAQ,IAAI,CAAC,IAAI;AAC3B,UAAM,QAAQ,CAAC;AAEf,QAAI,MAAM,QAAQ;AAChB,YAAM;AACN,YAAM,QAAQ,EAAE,CAAC;AAAA,IACnB;AAEA,QAAI,IAAI,eAAe,SAAS,QAAQ,MAAM,OAAO,IAAI;AACzD,QAAI,CAAC,EAAE,IAAI,cAAc;AAEzB,QAAI,qBAAqB,MAAM,GAAG;AAChC,UAAI,IAAI,IAAI,QAAQ,QAAQ;AAC1B,aAAK;AACL,YAAI;AAAA,MACN,WAAW,WAAW,IAAI,QAAQ,QAAQ;AACxC,YAAI,IAAI;AACR,kBAAU,QAAQ,EAAE,QAAQ;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAEA,YAAU,IAAI;AACd,YAAU,IAAI;AACd,YAAU,OAAO;AACjB,YAAU,UAAU;AACpB,YAAU,WAAW;AACrB,SAAO;AACT;AAEO,SAAS,kBAAkB,SAAS,UAAU,cAAc,OAAO;AACxE,MAAI,UAAU,QAAQ,CAAC,GACnB,SAAS,SAAS,CAAC,GACnB,SACA,YACA,QACA,KACA,KACA,GACA,GACA,GACA;AAEJ,MAAI,WAAW,KAAK,WAAW,GAAG;AAChC,eAAW,cAAc,QAAQ;AAAA,EACnC;AAEA,UAAQ,UAAU,yBAAyB,OAAO;AAElD,MAAI,QAAQ,SAAS,GAAG;AAEtB,aAAS,QAAQ,cAAc;AAC/B,UAAM,IAAI;AAEV,YAAQ,OAAO,QAAQ,GAAG,EAAE,eAAe,QAAQ;AACjD,gBAAU,QAAQ,CAAC;AAAA,IACrB;AAEA,UAAM,MAAM,QAAQ;AACpB,gBAAY,SAAS,QAAQ,MAAM,QAAQ;AAAA,EAC7C;AAEA,YAAU,QAAQ;AAClB,eAAa,QAAQ;AACrB,WAAS,QAAQ,cAAc;AAC/B,MAAI,QAAQ,OAAO,SAAS,QAAQ,OAAO,WAAW,IAAI,CAAC,EAAE,SAAS,QAAQ,aAAa,QAAQ,OAAO,SAAS,CAAC,KAAK,IAAI,gBAAgB,SAAS,QAAQ,QAAQ;AACtK,QAAM,IAAI,QAAQ,IAAI,CAAC,IAAI;AAC3B,QAAM,QAAQ,CAAC;AAEf,MAAI,MAAM,QAAQ;AAChB,UAAM;AACN,UAAM,QAAQ,EAAE,CAAC;AAAA,EACnB;AAEA,MAAI,IAAI,eAAe,SAAS,QAAQ,MAAM,OAAO,IAAI,eAAe;AACxE,QAAM,IAAI;AACV,MAAI,CAAC,EAAE,IAAI,cAAc;AACzB,MAAI,QAAQ,CAAC;AACb,SAAO,IAAI,QAAQ,IAAI,KAAK,QAAQ,IAAI,CAAC,IAAI,KAAK,IAAI,OAAO,KAAK,QAAQ,IAAI,CAAC,IAAI,KAAK,OAAO,QAAQ,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC;AAC5H,SAAO,IAAI,QAAQ,IAAI,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,QAAQ,IAAI,CAAC,MAAM,IAAI,OAAO,KAAK,QAAQ,IAAI,CAAC,IAAI,KAAK,OAAO,QAAQ,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC;AAE/I,MAAI,cAAc;AAChB,WAAO,QAAQ,QAAQ,cAAc,qBAAqB,SAAS,GAAG,KAAK,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI,IAAI,QAAQ,SAAS;AAAA,EAC7H;AAEA,SAAO;AACT;AAEO,SAAS,iBAAiB,SAAS,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI;AAC5D,MAAI,IAAI,QAAQ,QACZ,SACA,GACA,GACA,GACA;AAEJ,SAAO,EAAE,IAAI,IAAI;AACf,cAAU,QAAQ,CAAC;AACnB,QAAI,QAAQ;AAEZ,SAAK,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AACzB,UAAI,QAAQ,CAAC;AACb,UAAI,QAAQ,IAAI,CAAC;AACjB,cAAQ,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI;AAC7B,cAAQ,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,IACnC;AAAA,EACF;AAEA,UAAQ,SAAS;AACjB,SAAO;AACT;AAEA,SAAS,aAAa,OAAO,OAAO,IAAI,IAAI,OAAO,cAAc,WAAW,GAAG,GAAG;AAChF,MAAI,UAAU,KAAK,UAAU,GAAG;AAC9B;AAAA,EACF;AAEA,OAAK,KAAK,EAAE;AACZ,OAAK,KAAK,EAAE;AAEZ,MAAI,WAAW,QAAQ,MAAM,UACzB,WAAW,KAAK,QAAQ,GACxB,WAAW,KAAK,QAAQ,GACxB,KAAK,KAAK,IACV,QAAQ,KAAK,GACb,OAAO,QAAQ,KAAK,GACpB,OAAO,QAAQ,KAAK,GACpB,KAAK,WAAW,MAAM,WAAW,KACjC,KAAK,CAAC,WAAW,MAAM,WAAW,KAClC,QAAQ,KAAK,IACb,QAAQ,KAAK,IACb,aAAa,SAAS,KAAK,MAAM,SAAS,KAAK;AAEnD,MAAI,aAAa,GAAG;AAClB,SAAK,MAAM,UAAU,IAAI;AACzB,SAAK,MAAM,UAAU,IAAI;AAAA,EAC3B;AAEA,MAAI,QAAQ,KAAK,IACb,QAAQ,KAAK,IACb,MAAM,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,UAAU,QAAQ,QAAQ,QAAQ;AAEpF,MAAI,KAAK,GAAG;AACV,SAAK;AAAA,EACP;AAEA,MAAI,QAAQ,iBAAiB,YAAY,KAAK,KAAK,MAAM,EAAE,GACvD,MAAM,QAAQ,KAAK,KAAK,KACxB,MAAM,OAAO,EAAE,KAAK,KAAK,KACzB,OAAO,QAAQ,KAAK,GACpB,OAAO,QAAQ,KAAK,GACpB,KAAK,OAAO,WAAW,MAAM,WAAW,MACxC,KAAK,OAAO,WAAW,MAAM,WAAW,MACxC,MAAM,KAAK,OAAO,IAClB,MAAM,KAAK,OAAO,IAClB,MAAM,CAAC,KAAK,OAAO,IACnB,MAAM,CAAC,KAAK,OAAO,IACnB,OAAO,KAAK,KAAK,KAAK,IACtB,cAAc,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,IAAI,CAAC,GAC3D,eAAe,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,MAAM,QAAQ,KAAK,KAAK,KAAK,GAAG,CAAC;AAEtH,QAAM,WAAW,MAAM,cAAc;AAErC,MAAI,CAAC,aAAa,cAAc,GAAG;AACjC,mBAAe;AAAA,EACjB,WAAW,aAAa,cAAc,GAAG;AACvC,mBAAe;AAAA,EACjB;AAEA,gBAAc;AACd,iBAAe;AAEf,MAAI,WAAW,KAAK,KAAK,KAAK,WAAW,KAAK,QAAQ,EAAE,GACpD,UAAU,CAAC,GACX,iBAAiB,cAAc,UAC/B,gBAAgB,IAAI,IAAI,KAAK,iBAAiB,CAAC,KAAK,IAAI,KAAK,iBAAiB,CAAC,IAC/E,KAAK,WAAW,IAChB,KAAK,WAAW,IAChB,KAAK,WAAW,CAAC,IACjB,KAAK,WAAW,IAChB;AAEJ,OAAK,IAAI,GAAG,IAAI,UAAU,KAAK;AAC7B,YAAQ,aAAa,IAAI;AACzB,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,SAAS,cAAc;AACjC,SAAK,KAAK,KAAK;AACf,YAAQ,KAAK,KAAK,gBAAgB,IAAI,KAAK,gBAAgB,IAAI,KAAK,gBAAgB,IAAI,KAAK,gBAAgB,IAAI,IAAI,EAAE;AAAA,EACzH;AAGA,OAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK,GAAG;AACtC,SAAK,QAAQ,CAAC;AACd,SAAK,QAAQ,IAAI,CAAC;AAClB,YAAQ,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK;AACjC,YAAQ,IAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK;AAAA,EACvC;AAEA,UAAQ,IAAI,CAAC,IAAI;AAEjB,UAAQ,IAAI,CAAC,IAAI;AACjB,SAAO;AACT;AAGO,SAAS,gBAAgB,GAAG;AACjC,MAAI,KAAK,IAAI,IAAI,QAAQ,aAAa,SAAU,GAAG;AACjD,QAAI,IAAI,CAAC;AACT,WAAO,IAAI,QAAU,IAAI,QAAU,IAAI;AAAA,EACzC,CAAC,EAAE,MAAM,WAAW,KAAK,CAAC,GAE1B,OAAO,CAAC,GACJ,YAAY,GACZ,YAAY,GACZ,YAAY,IAAI,GAChB,WAAW,EAAE,QACb,SAAS,GACT,eAAe,4BAA4B,GAC3C,GACA,GACA,GACA,GACA,SACA,YACA,SACA,QACA,QACA,MACA,MACA,SACA,aACA,OACA,OACA,OAAO,SAASC,MAAK,IAAI,IAAI,IAAI,IAAI;AACvC,YAAQ,KAAK,MAAM;AACnB,YAAQ,KAAK,MAAM;AACnB,YAAQ,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,IAAI,EAAE;AAAA,EACjE;AAEA,MAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,KAAK,MAAM,EAAE,CAAC,CAAC,GAAG;AACrC,YAAQ,IAAI,YAAY;AACxB,WAAO;AAAA,EACT;AAEA,OAAK,IAAI,GAAG,IAAI,UAAU,KAAK;AAC7B,kBAAc;AAEd,QAAI,MAAM,EAAE,CAAC,CAAC,GAAG;AACf,gBAAU,EAAE,CAAC,EAAE,YAAY;AAC3B,mBAAa,YAAY,EAAE,CAAC;AAAA,IAC9B,OAAO;AAEL;AAAA,IACF;AAEA,QAAI,CAAC,EAAE,IAAI,CAAC;AACZ,QAAI,CAAC,EAAE,IAAI,CAAC;AAEZ,QAAI,YAAY;AACd,WAAK;AACL,WAAK;AAAA,IACP;AAEA,QAAI,CAAC,GAAG;AACN,eAAS;AACT,eAAS;AAAA,IACX;AAGA,QAAI,YAAY,KAAK;AACnB,UAAI,SAAS;AACX,YAAI,QAAQ,SAAS,GAAG;AAEtB,eAAK,UAAU;AAAA,QACjB,OAAO;AACL,oBAAU,QAAQ;AAAA,QACpB;AAAA,MACF;AAEA,kBAAY,SAAS;AACrB,kBAAY,SAAS;AACrB,gBAAU,CAAC,GAAG,CAAC;AACf,WAAK,KAAK,OAAO;AACjB,WAAK;AACL,gBAAU;AAAA,IAEZ,WAAW,YAAY,KAAK;AAC1B,UAAI,CAAC,SAAS;AACZ,kBAAU,CAAC,GAAG,CAAC;AAAA,MACjB;AAEA,UAAI,CAAC,YAAY;AACf,oBAAY,YAAY;AAAA,MAC1B;AAGA,cAAQ,KAAK,GAAG,GAAG,YAAY,EAAE,IAAI,CAAC,IAAI,GAAG,YAAY,EAAE,IAAI,CAAC,IAAI,GAAG,aAAa,EAAE,IAAI,CAAC,IAAI,GAAG,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC;AAC3H,WAAK;AAAA,IACP,WAAW,YAAY,KAAK;AAC1B,aAAO;AACP,aAAO;AAEP,UAAI,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,gBAAQ,YAAY,QAAQ,QAAQ,SAAS,CAAC;AAC9C,gBAAQ,YAAY,QAAQ,QAAQ,SAAS,CAAC;AAAA,MAChD;AAEA,UAAI,CAAC,YAAY;AACf,oBAAY,YAAY;AAAA,MAC1B;AAEA,cAAQ,KAAK,MAAM,MAAM,GAAG,GAAG,aAAa,EAAE,IAAI,CAAC,IAAI,GAAG,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC;AACnF,WAAK;AAAA,IACP,WAAW,YAAY,KAAK;AAC1B,aAAO,aAAa,IAAI,aAAa;AACrC,aAAO,aAAa,IAAI,aAAa;AAErC,UAAI,CAAC,YAAY;AACf,oBAAY,YAAY;AAAA,MAC1B;AAEA,mBAAa,EAAE,IAAI,CAAC,IAAI;AACxB,mBAAa,EAAE,IAAI,CAAC,IAAI;AACxB,cAAQ,KAAK,MAAM,MAAM,aAAa,IAAI,aAAa,WAAW,aAAa,IAAI,aAAa,WAAW,WAAW,SAAS;AAC/H,WAAK;AAAA,IACP,WAAW,YAAY,KAAK;AAC1B,aAAO,YAAY,QAAQ,QAAQ,SAAS,CAAC;AAC7C,aAAO,YAAY,QAAQ,QAAQ,SAAS,CAAC;AAC7C,cAAQ,KAAK,YAAY,MAAM,YAAY,MAAM,KAAK,YAAY,OAAO,MAAM,KAAK,WAAW,KAAK,YAAY,OAAO,MAAM,KAAK,WAAW,YAAY,GAAG,YAAY,CAAC;AACzK,WAAK;AAAA,IACP,WAAW,YAAY,KAAK;AAC1B,WAAK,WAAW,WAAW,YAAY,GAAG,SAAS;AACnD,WAAK;AAAA,IACP,WAAW,YAAY,KAAK;AAE1B,WAAK,WAAW,WAAW,WAAW,YAAY,KAAK,aAAa,YAAY,YAAY,EAAE;AAC9F,WAAK;AAAA,IACP,WAAW,YAAY,OAAO,YAAY,KAAK;AAC7C,UAAI,YAAY,KAAK;AACnB,YAAI;AACJ,YAAI;AACJ,gBAAQ,SAAS;AAAA,MACnB;AAEA,UAAI,YAAY,OAAO,KAAK,YAAY,CAAC,IAAI,OAAO,KAAK,YAAY,CAAC,IAAI,KAAK;AAC7E,aAAK,WAAW,WAAW,GAAG,CAAC;AAE/B,YAAI,YAAY,KAAK;AACnB,eAAK;AAAA,QACP;AAAA,MACF;AAEA,kBAAY;AACZ,kBAAY;AAAA,IACd,WAAW,YAAY,KAAK;AAC1B,cAAQ,EAAE,IAAI,CAAC;AACf,cAAQ,EAAE,IAAI,CAAC;AACf,aAAO,EAAE,IAAI,CAAC;AACd,aAAO,EAAE,IAAI,CAAC;AACd,UAAI;AAEJ,UAAI,MAAM,SAAS,GAAG;AAEpB,YAAI,MAAM,SAAS,GAAG;AACpB,iBAAO;AACP,iBAAO;AACP;AAAA,QACF,OAAO;AACL,iBAAO;AACP,iBAAO,MAAM,OAAO,CAAC;AACrB,eAAK;AAAA,QACP;AAEA,gBAAQ,MAAM,OAAO,CAAC;AACtB,gBAAQ,MAAM,OAAO,CAAC;AAAA,MACxB;AAEA,gBAAU,aAAa,WAAW,WAAW,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,aAAa,YAAY,KAAK,OAAO,IAAI,aAAa,YAAY,KAAK,OAAO,CAAC;AAC9K,WAAK;AAEL,UAAI,SAAS;AACX,aAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACnC,kBAAQ,KAAK,QAAQ,CAAC,CAAC;AAAA,QACzB;AAAA,MACF;AAEA,kBAAY,QAAQ,QAAQ,SAAS,CAAC;AACtC,kBAAY,QAAQ,QAAQ,SAAS,CAAC;AAAA,IACxC,OAAO;AACL,cAAQ,IAAI,YAAY;AAAA,IAC1B;AAAA,EACF;AAEA,MAAI,QAAQ;AAEZ,MAAI,IAAI,GAAG;AAET,SAAK,IAAI;AACT,QAAI;AAAA,EACN,WAAW,QAAQ,CAAC,MAAM,QAAQ,IAAI,CAAC,KAAK,QAAQ,CAAC,MAAM,QAAQ,IAAI,CAAC,GAAG;AACzE,YAAQ,SAAS;AAAA,EACnB;AAEA,OAAK,cAAc,SAAS;AAC5B,SAAO;AACT;AAoDO,SAAS,oBAAoB,QAAQ,WAAW;AACrD,MAAI,cAAc,QAAQ;AACxB,gBAAY;AAAA,EACd;AAEA,MAAI,IAAI,OAAO,CAAC,GACZ,IAAI,GACJ,UAAU,CAAC,GAAG,CAAC,GACf,IAAI;AAER,SAAO,IAAI,OAAO,QAAQ,KAAK,GAAG;AAChC,YAAQ,KAAK,GAAG,GAAG,OAAO,CAAC,GAAG,KAAK,OAAO,CAAC,IAAI,KAAK,YAAY,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC;AAAA,EACtF;AAEA,SAAO;AACT;AAEO,SAAS,gBAAgB,QAAQ,WAAW;AAEjD,OAAK,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC,IAAI,QAAQ,KAAK,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC,IAAI,SAAS,SAAS,OAAO,MAAM,CAAC;AAEpG,MAAI,IAAI,OAAO,SAAS,GACpB,IAAI,CAAC,OAAO,CAAC,GACb,IAAI,CAAC,OAAO,CAAC,GACb,QAAQ,CAAC,OAAO,CAAC,GACjB,QAAQ,CAAC,OAAO,CAAC,GACjB,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,GACrB,MAAM,QAAQ,GACd,MAAM,QAAQ,GACd,SAAS,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,QAAS,KAAK,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,MAC1E,OACA,OACA,GACA,KACA,KACA,IACA,IACA,IACA,IACA,KACA,KACA,KACA,KACA,KACA;AAEJ,MAAI,QAAQ;AAEV,WAAO,KAAK,OAAO,KAAK;AACxB,YAAQ;AACR,YAAQ;AACR,QAAI,OAAO,IAAI,CAAC;AAChB,QAAI,OAAO,IAAI,CAAC;AAChB,WAAO,QAAQ,GAAG,CAAC;AACnB,SAAK;AAAA,EACP;AAEA,cAAY,aAAa,cAAc,IAAI,CAAC,YAAY;AAExD,OAAK,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AACzB,YAAQ;AACR,YAAQ;AACR,QAAI;AACJ,QAAI;AACJ,YAAQ,CAAC,OAAO,IAAI,CAAC;AACrB,YAAQ,CAAC,OAAO,IAAI,CAAC;AAErB,QAAI,MAAM,SAAS,MAAM,OAAO;AAC9B;AAAA,IACF;AAEA,UAAM;AACN,UAAM;AACN,UAAM,QAAQ;AACd,UAAM,QAAQ;AACd,SAAK,MAAM,MAAM,MAAM,MAAM,GAAG;AAEhC,SAAK,MAAM,MAAM,MAAM,MAAM,GAAG;AAChC,SAAK,MAAM,KAAK,IAAI,MAAM,KAAK,MAAM,IAAI,CAAC,IAAI,KAAK,IAAI,MAAM,KAAK,MAAM,IAAI,CAAC,CAAC;AAC9E,UAAM,KAAK,MAAM,YAAY,OAAO;AACpC,UAAM,KAAK,IAAI,UAAU,KAAK,KAAK,KAAK;AACxC,UAAM,KAAK,QAAQ,MAAM,KAAK,KAAK,KAAK;AACxC,UAAM,KAAK,QAAQ,MAAM,QAAQ,KAAK,KAAK,KAAK,MAAM,OAAO,KAAK;AAClE,UAAM,KAAK,IAAI,UAAU,KAAK,KAAK,KAAK;AACxC,UAAM,KAAK,QAAQ,MAAM,KAAK,KAAK,KAAK;AACxC,UAAM,KAAK,QAAQ,MAAM,QAAQ,KAAK,KAAK,KAAK,MAAM,OAAO,KAAK;AAElE,QAAI,MAAM,SAAS,MAAM,OAAO;AAC9B,cAAQ;AAAA,QAAK,OAAO,MAAM,GAAG;AAAA;AAAA,QAC7B,OAAO,MAAM,GAAG;AAAA,QAAG,OAAO,CAAC;AAAA;AAAA,QAC3B,OAAO,CAAC;AAAA,QAAG,OAAO,MAAM,GAAG;AAAA;AAAA,QAC3B,OAAO,MAAM,GAAG;AAAA,MAAC;AAAA,IACnB;AAAA,EACF;AAEA,QAAM,SAAS,MAAM,SAAS,QAAQ,SAAS,IAAI,QAAQ,KAAK,OAAO,KAAK,GAAG,OAAO,KAAK,GAAG,OAAO,KAAK,GAAG,OAAO,KAAK,CAAC,IAAI,QAAQ,UAAU;AAEhJ,MAAI,QAAQ,WAAW,GAAG;AAExB,YAAQ,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EAC/B,WAAW,QAAQ;AACjB,YAAQ,OAAO,GAAG,CAAC;AACnB,YAAQ,SAAS,QAAQ,SAAS;AAAA,EACpC;AAEA,SAAO;AACT;AAmLO,SAAS,gBAAgB,SAAS;AACvC,MAAI,UAAU,QAAQ,CAAC,CAAC,GAAG;AAEzB,cAAU,CAAC,OAAO;AAAA,EACpB;AAEA,MAAI,SAAS,IACT,IAAI,QAAQ,QACZ,IACA,GACA,GACA;AAEJ,OAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,cAAU,QAAQ,CAAC;AACnB,cAAU,MAAM,OAAO,QAAQ,CAAC,CAAC,IAAI,MAAM,OAAO,QAAQ,CAAC,CAAC,IAAI;AAChE,SAAK,QAAQ;AAEb,SAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACvB,gBAAU,OAAO,QAAQ,GAAG,CAAC,IAAI,MAAM,OAAO,QAAQ,GAAG,CAAC,IAAI,MAAM,OAAO,QAAQ,GAAG,CAAC,IAAI,MAAM,OAAO,QAAQ,GAAG,CAAC,IAAI,MAAM,OAAO,QAAQ,GAAG,CAAC,IAAI,MAAM,OAAO,QAAQ,CAAC,CAAC,IAAI;AAAA,IAClL;AAEA,QAAI,QAAQ,QAAQ;AAClB,gBAAU;AAAA,IACZ;AAAA,EACF;AAEA,SAAO;AACT;;;ACt2CA,IAAI;AAAJ,IACI;AADJ,IAEI;AAFJ,IAGI;AAHJ,IAII;AAJJ,IAKI;AALJ,IAMI;AANJ,IAOI;AAPJ,IAQI,iBAAiB;AARrB,IASI,uBAAuB,iBAAiB;AAT5C,IAUI;AAVJ,IAWI,UAAU,SAASC,SAAQ,SAAS;AACtC,MAAI,MAAM,QAAQ,iBAAiB;AAEnC,MAAI,EAAE,kBAAkB,QAAQ,UAAU,iBAAiB,QAAQ,OAAO;AAExE,qBAAiB;AACjB,2BAAuB,iBAAiB;AAAA,EAC1C;AAEA,SAAO,IAAI,eAAe,MAAM,IAAI,aAAa;AAAA,EAAC;AAElD,SAAO;AACP,oBAAkB,IAAI,SAAS;AAE/B,MAAI,KAAK;AACP,WAAO;AACP,kBAAc,IAAI;AAClB,YAAQ,IAAI;AACZ,WAAO,KAAK,gBAAgB,8BAA8B,GAAG;AAE7D,SAAK,MAAM,YAAY;AAEvB,QAAI,KAAK,IAAI,cAAc,KAAK,GAC5B,KAAK,IAAI,cAAc,KAAK,GAC5B,OAAO,QAAQ,IAAI,QAAQ,IAAI;AAEnC,QAAI,QAAQ,KAAK,aAAa;AAC5B,WAAK,YAAY,EAAE;AACnB,SAAG,YAAY,EAAE;AACjB,SAAG,aAAa,SAAS,gDAAgD;AACzE,sBAAgB,GAAG,iBAAiB;AACpC,WAAK,YAAY,EAAE;AAAA,IACrB;AAAA,EACF;AAEA,SAAO;AACT;AA/CA,IAgDI,qBAAqB,SAASC,oBAAmB,GAAG;AAEtD,MAAI,GAAG;AAEP,SAAO,KAAK,MAAM,OAAO;AACvB,YAAQ,EAAE;AACV,aAAS,MAAM,WAAW,MAAM,IAAI,GAAG,GAAG;AAE1C,QAAI,SAAS,CAAC,MAAM,UAAU,CAAC,MAAM,UAAU,MAAM,iBAAiB;AACpE,YAAM,SAAS,MAAM,SAAS;AAC9B,YAAM,gBAAgB,GAAG,KAAK;AAC9B,UAAI,EAAE,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK;AAAA,IAChC;AAEA,QAAI,EAAE;AAAA,EACR;AAEA,SAAO;AACT;AAlEA,IAqFA,YAAY,CAAC;AArFb,IAuFA,YAAY,CAAC;AAvFb,IAwFI,mBAAmB,SAASC,oBAAmB;AACjD,SAAO,KAAK,eAAe,KAAK,aAAa,YAAY,aAAa,MAAM,aAAa;AAC3F;AA1FA,IA2FI,oBAAoB,SAASC,qBAAoB;AACnD,SAAO,KAAK,eAAe,KAAK,cAAc,YAAY,cAAc,MAAM,cAAc;AAC9F;AA7FA,IA8FI,YAAY,SAASC,WAAU,SAAS;AAC1C,SAAO,QAAQ,qBAAqB,QAAQ,UAAU,IAAI,YAAY,MAAM,QAAQ,UAAU;AAChG;AAhGA,IAiGI,WAAW,SAASC,UAAS,SAAS;AACxC,MAAI,KAAK,iBAAiB,OAAO,EAAE,aAAa,SAAS;AACvD,WAAO;AAAA,EACT;AAEA,YAAU,QAAQ;AAElB,MAAI,WAAW,QAAQ,aAAa,GAAG;AAErC,WAAOA,UAAS,OAAO;AAAA,EACzB;AACF;AA5GA,IA6GI,iBAAiB,SAASC,gBAAe,SAAS,GAAG;AACvD,MAAI,QAAQ,eAAe,QAAQ,QAAQ,OAAO,IAAI;AACpD,QAAI,MAAM,UAAU,OAAO,GACvB,KAAK,MAAM,IAAI,aAAa,OAAO,KAAK,+BAA+B,gCACvE,OAAO,MAAM,IAAI,SAAS,MAAM,OAChC,IAAI,MAAM,IAAI,IAAI,KAClB,IAAI,MAAM,IAAI,MAAM,GACpB,MAAM,2EACN,IAAI,KAAK,kBAAkB,KAAK,gBAAgB,GAAG,QAAQ,UAAU,MAAM,GAAG,IAAI,IAAI,KAAK,cAAc,IAAI;AAEjH,QAAI,GAAG;AACL,UAAI,CAAC,KAAK;AACR,YAAI,CAAC,eAAe;AAClB,0BAAgBA,gBAAe,OAAO;AACtC,wBAAc,MAAM,UAAU;AAAA,QAChC;AAEA,UAAE,MAAM,UAAU,MAAM,kCAAkC,IAAI,aAAa,IAAI;AAE/E,sBAAc,YAAY,CAAC;AAAA,MAC7B,OAAO;AACL,0BAAkB,gBAAgBA,gBAAe,OAAO;AACxD,UAAE,aAAa,SAAS,IAAI;AAC5B,UAAE,aAAa,UAAU,IAAI;AAC7B,UAAE,aAAa,aAAa,eAAe,IAAI,MAAM,IAAI,GAAG;AAE5D,sBAAc,YAAY,CAAC;AAAA,MAC7B;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,QAAM;AACR;AA/IA,IAgJI,eAAe,SAASC,cAAa,GAAG;AAE1C,MAAI,IAAI,IAAI,SAAS,GACjB,IAAI;AAER,SAAO,IAAI,EAAE,eAAe,KAAK;AAC/B,MAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,MAAM;AAAA,EAChC;AAEA,SAAO;AACT;AA1JA,IA2JI,UAAU,SAASC,SAAQ,KAAK;AAClC,MAAI,IAAI,IAAI,OAAO,GACf;AAEJ,MAAI,CAAC,GAAG;AAEN,gBAAY,IAAI,MAAM,cAAc;AACpC,QAAI,MAAM,cAAc,IAAI;AAE5B,QAAI,YAAY,IAAI;AACpB,QAAI,KAAK,OAAO;AAChB,QAAI,YAAY,IAAI;AACpB,gBAAY,IAAI,MAAM,cAAc,IAAI,YAAY,IAAI,MAAM,eAAe,eAAe,QAAQ,YAAY,KAAK,EAAE,YAAY,CAAC;AAAA,EACtI;AAEA,SAAO,KAAK,gBAAgB,MAAM;AACpC;AA3KA,IA4KI,iBAAiB,SAASC,gBAAe,SAAS,eAAe;AACnE,MAAI,MAAM,UAAU,OAAO,GACvB,YAAY,YAAY,KACxB,WAAW,MAAM,YAAY,WAC7B,SAAS,QAAQ,YACjB,aAAa,UAAU,CAAC,OAAO,OAAO,cAAc,OAAO,WAAW,cAAc,OAAO,aAAa,QACxG,WACA,GACA,GACA,GACA,GACA;AAEJ,MAAI,YAAY,MAAM;AACpB,WAAO;AAAA,EACT;AAEA,WAAS,UAAU,SAAS,KAAK,eAAe,SAAS,CAAC,GAAG,eAAe,SAAS,CAAC,GAAG,eAAe,SAAS,CAAC,CAAC;AACnH,cAAY,MAAM,gBAAgB;AAElC,MAAI,KAAK;AACP,QAAI,WAAW;AACb,UAAI,QAAQ,OAAO;AACnB,UAAI,CAAC,EAAE,IAAI,EAAE;AACb,UAAI,CAAC,EAAE,IAAI,EAAE;AACb,UAAI;AAAA,IACN,WAAW,QAAQ,SAAS;AAC1B,UAAI,QAAQ,QAAQ;AACpB,UAAI,QAAQ,YAAY,QAAQ,UAAU,UAAU,CAAC;AAErD,UAAI,CAAC,EAAE,gBAAgB,kBAAkB,EAAE,gBAAgB,IAAI,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE;AAE9F,UAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AACxB,UAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,IAC1B,OAAO;AAEL,UAAI,IAAI,SAAS;AACjB,UAAI,IAAI;AAAA,IACV;AAEA,QAAI,iBAAiB,QAAQ,QAAQ,YAAY,MAAM,KAAK;AAC1D,UAAI,IAAI;AAAA,IACV;AAEA,KAAC,YAAY,MAAM,QAAQ,YAAY,SAAS;AAChD,cAAU,aAAa,aAAa,YAAY,EAAE,IAAI,MAAM,EAAE,IAAI,MAAM,EAAE,IAAI,MAAM,EAAE,IAAI,OAAO,EAAE,IAAI,KAAK,OAAO,EAAE,IAAI,KAAK,GAAG;AAAA,EACnI,OAAO;AACL,QAAI,IAAI;AAER,QAAI,eAAe;AAEjB,UAAI,QAAQ;AACZ,UAAI;AAEJ,aAAO,MAAM,IAAI,EAAE,eAAe,MAAM,KAAK,EAAE,YAAY;AACzD,aAAK,KAAK,iBAAiB,CAAC,EAAE,cAAc,IAAI,IAAI,SAAS,GAAG;AAC9D,cAAI,EAAE;AACN,cAAI,EAAE;AACN,cAAI;AAAA,QACN;AAAA,MACF;AAAA,IACF;AAEA,SAAK,KAAK,iBAAiB,OAAO;AAElC,QAAI,GAAG,aAAa,cAAc,GAAG,aAAa,SAAS;AACzD,UAAI,QAAQ;AAEZ,aAAO,UAAU,WAAW,GAAG;AAE7B,aAAK,OAAO,cAAc;AAC1B,aAAK,OAAO,aAAa;AACzB,iBAAS,OAAO;AAAA,MAClB;AAAA,IACF;AAEA,QAAI,UAAU;AACd,MAAE,MAAM,QAAQ,YAAY,IAAI;AAChC,MAAE,OAAO,QAAQ,aAAa,IAAI;AAClC,MAAE,cAAc,IAAI,GAAG,cAAc;AACrC,MAAE,oBAAoB,IAAI,GAAG,oBAAoB;AAMjD,MAAE,WAAW,GAAG,aAAa,UAAU,UAAU;AACjD,eAAW,YAAY,SAAS;AAAA,EAClC;AAEA,SAAO;AACT;AAvQA,IAwQI,aAAa,SAASC,YAAW,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACxD,IAAE,IAAI;AACN,IAAE,IAAI;AACN,IAAE,IAAI;AACN,IAAE,IAAI;AACN,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAEO,IAAI,WAAwB,WAAY;AAC7C,WAASC,UAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,QAAI,MAAM,QAAQ;AAChB,UAAI;AAAA,IACN;AAEA,QAAI,MAAM,QAAQ;AAChB,UAAI;AAAA,IACN;AAEA,QAAI,MAAM,QAAQ;AAChB,UAAI;AAAA,IACN;AAEA,QAAI,MAAM,QAAQ;AAChB,UAAI;AAAA,IACN;AAEA,QAAI,MAAM,QAAQ;AAChB,UAAI;AAAA,IACN;AAEA,QAAI,MAAM,QAAQ;AAChB,UAAI;AAAA,IACN;AAEA,eAAW,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EACnC;AAEA,MAAI,SAASA,UAAS;AAEtB,SAAO,UAAU,SAAS,UAAU;AAClC,QAAI,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,cAAc,IAAI,IAAI,IAAI,KAAK;AACnC,WAAO,WAAW,MAAM,IAAI,aAAa,CAAC,IAAI,aAAa,CAAC,IAAI,aAAa,IAAI,cAAc,IAAI,IAAI,IAAI,KAAK,aAAa,EAAE,IAAI,IAAI,IAAI,KAAK,WAAW;AAAA,EAC7J;AAEA,SAAO,WAAW,SAAS,SAAS,QAAQ;AAC1C,QAAI,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,KAAK,OAAO,GACZ,KAAK,OAAO,GACZ,KAAK,OAAO,GACZ,KAAK,OAAO,GACZ,KAAK,OAAO,GACZ,KAAK,OAAO;AAChB,WAAO,WAAW,MAAM,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,IAAI,KAAK,CAAC;AAAA,EACtI;AAEA,SAAO,QAAQ,SAAS,QAAQ;AAC9B,WAAO,IAAIA,UAAS,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAAA,EACpE;AAEA,SAAO,SAAS,SAAS,OAAO,QAAQ;AACtC,QAAI,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK;AACb,WAAO,MAAM,OAAO,KAAK,MAAM,OAAO,KAAK,MAAM,OAAO,KAAK,MAAM,OAAO,KAAK,MAAM,OAAO,KAAK,MAAM,OAAO;AAAA,EAChH;AAEA,SAAO,QAAQ,SAAS,MAAM,OAAO,WAAW;AAC9C,QAAI,cAAc,QAAQ;AACxB,kBAAY,CAAC;AAAA,IACf;AAEA,QAAI,IAAI,MAAM,GACV,IAAI,MAAM,GACV,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK;AACb,cAAU,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK;AACnC,cAAU,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK;AACnC,WAAO;AAAA,EACT;AAEA,SAAOA;AACT,EAAE;AAQK,SAAS,gBAAgB,SAAS,SAAS,eAAe,sBAAsB;AAErF,MAAI,CAAC,WAAW,CAAC,QAAQ,eAAe,QAAQ,QAAQ,OAAO,GAAG,oBAAoB,SAAS;AAC7F,WAAO,IAAI,SAAS;AAAA,EACtB;AAEA,MAAI,aAAa,mBAAmB,OAAO,GACvC,MAAM,UAAU,OAAO,GACvB,QAAQ,MAAM,YAAY,WAC1B,YAAY,eAAe,SAAS,aAAa,GACjD,KAAK,MAAM,CAAC,EAAE,sBAAsB,GACpC,KAAK,MAAM,CAAC,EAAE,sBAAsB,GACpC,KAAK,MAAM,CAAC,EAAE,sBAAsB,GACpC,SAAS,UAAU,YACnB,UAAU,CAAC,wBAAwB,SAAS,OAAO,GACnD,IAAI,IAAI,UAAU,GAAG,OAAO,GAAG,QAAQ,MAAM,GAAG,MAAM,GAAG,OAAO,MAAM,GAAG,OAAO,GAAG,QAAQ,MAAM,GAAG,MAAM,GAAG,OAAO,KAAK,GAAG,QAAQ,UAAU,IAAI,kBAAkB,IAAI,GAAG,OAAO,UAAU,IAAI,iBAAiB,EAAE;AAEvN,SAAO,YAAY,SAAS;AAE5B,MAAI,YAAY;AACd,SAAK,WAAW;AAEhB,WAAO,MAAM;AACX,WAAK,WAAW,EAAE;AAClB,SAAG,SAAS,GAAG,SAAS;AACxB,SAAG,gBAAgB,GAAG,EAAE;AAAA,IAC1B;AAAA,EACF;AAEA,SAAO,UAAU,EAAE,QAAQ,IAAI;AACjC;;;AChZA,IAAI,UAAU,wCAAwC,MAAM,GAAG;AAA/D,IACI,UAAU,sCAAsC,MAAM,GAAG;AAD7D,IAEIC,YAAW,KAAK,KAAK;AAFzB,IAGI;AAHJ,IAII;AAJJ,IAKI;AALJ,IAMI;AANJ,IAOI;AAPJ,IAQI;AARJ,IASI,WAAW,SAASC,YAAW;AACjC,SAAO,QAAQ,OAAO,WAAW,gBAAgB,OAAO,OAAO,SAAS,KAAK,kBAAkB;AACjG;AAXA,IAYI,4BAA4B,SAASC,2BAA0B,SAAS,QAAQ,UAAU,MAAM;AAElG,MAAI,IAAI,OAAO,QACX,KAAK,SAAS,IAAI,IAAI,MACtB,IAAI,GACJ;AAEJ,SAAO,IAAI,GAAG,KAAK;AACjB,YAAQ,EAAE,IAAI,IAAI,WAAW,OAAO,CAAC,EAAE,QAAQ,CAAC;AAChD,aAAS,MAAM,QAAQ,KAAK,CAAC,IAAI;AACjC,UAAM;AAAA,EACR;AAEA,SAAO;AACT;AA1BA,IA2BI,cAAc,SAASC,aAAY,QAAQ,MAAM,MAAM;AACzD,SAAO,WAAW,OAAO,MAAM,IAAI,QAAQ,MAAM,QAAQ,IAAI,CAAC,KAAK;AACrE;AA7BA,IA8BI,cAAc,SAASC,aAAY,SAAS;AAC9C,MAAI,IAAI,QAAQ,CAAC,GACb,IAAI,QAAQ,CAAC,GACb;AAEJ,OAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK,GAAG;AACtC,QAAI,QAAQ,CAAC,KAAK;AAClB,QAAI,QAAQ,IAAI,CAAC,KAAK;AAAA,EACxB;AACF;AAvCA,IAuDA,oBAAoB,SAASC,mBAAkB,QAAQ,SAAS,QAAQ,GAAG,GAAG,QAAQ,MAAM,OAAO,OAAO;AACxG,MAAI,KAAK,SAAS,SAAS;AACzB,cAAU,CAAC,OAAO;AAAA,EACpB,OAAO;AACL,SAAK,gBAAgB,SAAS,QAAQ,QAAQ,YAAY,QAAQ,GAAG,KAAK,GAAG,IAAI,YAAY,QAAQ,GAAG,KAAK,IAAI,CAAC;AAClH,SAAK,YAAY,YAAY,OAAO;AACpC,QAAI,YAAY,IAAI,kBAAkB;AACtC,cAAU,CAAC,UAAU,SAAS,KAAK,SAAS,CAAC;AAAA,EAC/C;AAEA,YAAU,OAAO,OAAO,SAAS,QAAQ,IAAI,CAAC;AAE9C,2BAAyB,QAAQ,QAAQ,GAAG,SAAS,KAAK,KAAK;AAE/D,OAAK,yBAAyB,QAAQ,QAAQ,GAAG,SAAS,KAAK,KAAK;AACpE,SAAO,yBAAyB,SAAS,KAAK,eAAe,KAAK,cAAc,IAAI,KAAK,GAAG;AAC9F;AAvEA,IAwEI,aAAa,SAASC,YAAW,GAAG;AACtC,SAAO;AACT;AA1EA,IA2EI,UAAU;AA3Ed,IA4EI,iBAAiB,SAASC,gBAAe,SAAS,QAAQ,cAAc;AAE1E,MAAI,IAAI,gBAAgB,OAAO,GAC3B,IAAI,GACJ,IAAI,GACJ;AAEJ,OAAK,QAAQ,UAAU,IAAI,YAAY,MAAM,OAAO;AAClD,UAAM,QAAQ,QAAQ;AACtB,QAAI,UAAU,MAAM;AAAA,MAClB,OAAO,CAAC,QAAQ,aAAa,OAAO;AAAA,MACpC,QAAQ,CAAC,QAAQ,aAAa,QAAQ;AAAA,IACxC;AAAA,EACF,OAAO;AACL,UAAM,UAAU,QAAQ,WAAW,QAAQ,QAAQ;AAAA,EACrD;AAEA,MAAI,UAAU,WAAW,QAAQ;AAC/B,QAAI,OAAO,OAAO,OAAO,CAAC,KAAK,MAAM,IAAI,QAAQ,QAAQ,eAAe,KAAK,OAAO;AACpF,QAAI,OAAO,OAAO,OAAO,CAAC,KAAK,MAAM,IAAI,SAAS,QAAQ,gBAAgB,KAAK,OAAO;AAAA,EACxF;AAEA,SAAO,aAAa,MAAM,KAAK,IAAI,EAAE,MAAM;AAAA,IACzC;AAAA,IACA;AAAA,EACF,CAAC,IAAI;AAAA,IACH,GAAG,EAAE;AAAA,IACL,GAAG,EAAE;AAAA,EACP,CAAC;AACH;AAzGA,IA0GI,kBAAkB,SAASC,iBAAgB,aAAa,WAAW,YAAY,UAAU;AAC3F,MAAI,eAAe,gBAAgB,YAAY,YAAY,MAAM,IAAI,GACjE,IAAI,aAAa,MAAM,EAAE,SAAS,gBAAgB,SAAS,CAAC,GAC5D,YAAY,eAAe,aAAa,YAAY,YAAY,GAChEC,mBAAkB,eAAe,WAAW,UAAU,YAAY,GAClE,IAAIA,iBAAgB,GACpB,IAAIA,iBAAgB,GACpB;AAEJ,IAAE,IAAI,EAAE,IAAI;AAEZ,MAAI,aAAa,UAAU,UAAU,kBAAkB,UAAU,QAAQ,YAAY,MAAM,QAAQ;AACjG,QAAI,UAAU,aAAa,GAAG,EAAE,MAAM,OAAO,KAAK,CAAC;AACnD,QAAI,EAAE,MAAM;AAAA,MACV,GAAG,CAAC,EAAE,CAAC;AAAA,MACP,GAAG,CAAC,EAAE,CAAC;AAAA,IACT,CAAC;AACD,SAAK,EAAE;AACP,SAAK,EAAE;AAAA,EACT;AAGA,MAAI,GAAG;AACL,QAAI,EAAE,MAAM,UAAU,QAAQ,CAAC;AAC/B,SAAK,EAAE;AACP,SAAK,EAAE;AAAA,EACT;AAEA,IAAE,IAAI,IAAI,UAAU;AACpB,IAAE,IAAI,IAAI,UAAU;AACpB,SAAO;AACT;AAzIA,IA0II,SAAS,SAASC,QAAO,SAAS,QAAQ,MAAM;AAClD,MAAI,QAAQ,KAAK,OACb,SAAS,KAAK,QACd,UAAU,KAAK,SACf,UAAU,KAAK,SACf,cAAc,KAAK;AAEvB,MAAI,IAAI,QAAQ,CAAC,EAAE,CAAC,GAChB,IAAI,QAAQ,CAAC,EAAE,CAAC,GAChB,OAAO,YAAY,QAAQ,GAAG,GAC9B,OAAO,YAAY,QAAQ,GAAG,GAC9B,aACA,GACA;AAEJ,MAAI,CAAC,WAAW,CAAC,QAAQ,QAAQ;AAC/B,WAAO,WAAW,UAAU;AAAA,EAC9B;AAEA,MAAI,OAAO;AACT,QAAI,UAAU,WAAW,cAAc,SAAS,KAAK,EAAE,CAAC,KAAK,YAAY,QAAQ;AAC/E,uBAAiB,SAAS,GAAG,GAAG,GAAG,GAAG,OAAO,GAAG,OAAO,CAAC;AAAA,IAC1D,OAAO;AACL,UAAI,eAAe,YAAY,CAAC,MAAM,OAAO;AAC3C,aAAK,IAAI,QAAQ;AAAA,UACf,iBAAiB,YAAY,CAAC,IAAI,MAAM,OAAO,YAAY,CAAC,IAAI,MAAM;AAAA,QACxE,CAAC;AAAA,MACH,OAAO;AACL,sBAAc,CAAC,YAAY,QAAQ,UAAU,IAAI,MAAM,YAAY,QAAQ,UAAU,IAAI,IAAI;AAAA,MAC/F;AAEA,UAAI,gBAAgB,QAAQ,aAAa,aAAa,MAAM;AAC5D,UAAI,EAAE,MAAM;AAAA,QACV;AAAA,QACA;AAAA,MACF,CAAC;AACD,uBAAiB,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;AAAA,IAClG;AAAA,EACF;AAEA,MAAI,QAAQ;AACV,qBAAiB,SAAS,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO,CAAC;AAAA,EACtF,WAAW,WAAW,SAAS;AAC7B,qBAAiB,SAAS,GAAG,GAAG,GAAG,GAAG,WAAW,GAAG,WAAW,CAAC;AAAA,EAClE;AAEA,SAAO;AACT;AAzLA,IA0LI,2BAA2B,SAASC,0BAAyB,QAAQ,QAAQ,UAAU,SAAS,cAAc,WAAW;AAC3H,MAAI,QAAQ,OAAO,OACf,UAAU,MAAM,SAChB,QAAQ,WAAW,QAAQ,WAAW,QAAQ,QAAQ,QAAQ,GAC9D,OAAO,SAAS,MAAM,QAAQ,GAAG,IAAI,IAAI,QAAQ,UACjD,KAAK,OAAO,MAAM,IAAI,UAAU,OAAO,KAAK,QAAQ,MAAM,GAAG,GAAG,YAAY,GAAG,MAAM,IAAI,QAAQ,MAAM,MAAM,CAAC;AAClH,KAAG,IAAI,SAAS,MAAM,IAAI,QAAQ,MAAM,SAAS,CAAC,KAAK;AACvD,KAAG,OAAO;AACV,KAAG,KAAK;AAER,SAAO,OAAO,KAAK,IAAI;AACzB;AArMA,IAsMI,iBAAiB,SAASC,gBAAe,OAAO,KAAK;AACvD,SAAO,SAAU,SAAS;AACxB,WAAO,SAAS,QAAQ,IAAI,aAAa,SAAS,OAAO,GAAG,IAAI;AAAA,EAClE;AACF;AAEO,IAAI,mBAAmB;AAAA,EAC5B,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU,SAAS,SAAS,MAAM,QAAQ,WAAW;AACnD,WAAO;AACP,eAAW,KAAK,MAAM;AACtB,eAAW,KAAK,MAAM;AACtB,qBAAiB,KAAK,KAAK;AAE3B,iBAAa,KAAK,KAAK,aAAa,WAAY;AAAA,IAAC;AAEjD,gBAAY;AAAA,EACd;AAAA,EACA,MAAM,SAAS,KAAK,QAAQ,MAAM,OAAO;AACvC,QAAI,CAAC,MAAM;AACT,cAAQ,KAAK,8CAA8C;AAC3D,aAAO;AAAA,IACT;AAEA,QAAI,EAAE,OAAO,SAAS,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK,MAAM;AAC5D,aAAO;AAAA,QACL,MAAM;AAAA,MACR;AAAA,IACF;AAEA,QAAI,WAAW,CAAC,GACZ,QAAQ,MACR,OAAO,MAAM,MACb,aAAa,MAAM,YACnB,QAAQ,MAAM,OACd,QAAQ,MAAM,OACd,IAAI,MAAM,GACV,IAAI,MAAM,GACV,WAAW,KAAK,CAAC,GACjB,SAAS,eAAe,KAAK,OAAO,SAAS,OAAO,KAAK,MAAM,CAAC,GAChE,SACA;AAEJ,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,SAAS,kBAAkB,eAAe,QAAQ,WAAW;AAElE,QAAI,KAAK,SAAS,cAAc,eAAe,GAAG;AAEhD,WAAK,UAAU,WAAW,UAAU,KAAK;AACzC,WAAK,UAAU,CAAC,CAAC,KAAK;AACtB,WAAK,QAAQ,KAAK,YAAY;AAE9B,WAAK,OAAO,OAAO,MAAM,IAAI,QAAQ,KAAK,OAAO,IAAI;AAErD,WAAK,KAAK,SAAS,OAAO,MAAM,IAAI,QAAQ,KAAK,KAAK,CAAC,KAAK;AAAA,IAC9D;AAEA,QAAI,MAAM,QAAQ,IAAI,KAAK,EAAE,YAAY,SAAS,OAAO,aAAa,UAAU;AAC9E,WAAK,KAAK,UAAU;AAClB,YAAI,CAAC,KAAK,CAAC,QAAQ,QAAQ,CAAC,GAAG;AAC7B,cAAI;AAAA,QACN,WAAW,CAAC,KAAK,CAAC,QAAQ,QAAQ,CAAC,GAAG;AACpC,cAAI;AAAA,QACN;AAAA,MACF;AAEA,UAAI,KAAK,GAAG;AAEV,iBAAS,KAAK,kBAAkB,MAAM,0BAA0B,0BAA0B,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,GAAG,QAAQ,MAAM,SAAS,SAAS,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,SAAS,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAAA,MACnN,OAAO;AACL,YAAI,IAAI;AAAA,MACV;AAEA,WAAK,KAAK,UAAU;AAClB,cAAM,KAAK,MAAM,KAAK,SAAS,KAAK,kBAAkB,MAAM,0BAA0B,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,GAAG,QAAQ,MAAM,SAAS,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAAA,MAC1J;AAAA,IACF,OAAO;AACL,gBAAU,OAAO,OAAO,WAAW,KAAK,IAAI,GAAG,QAAQ,IAAI,CAAC;AAC5D,+BAAyB,SAAS,KAAK,UAAU;AACjD,eAAS,KAAK,OAAO;AAErB,+BAAyB,MAAM,QAAQ,KAAK,KAAK,KAAK,SAAS,KAAK,KAAK,SAAS,IAAI;AAEtF,+BAAyB,MAAM,QAAQ,KAAK,KAAK,KAAK,SAAS,KAAK,KAAK,SAAS,IAAI;AAAA,IACxF;AAEA,UAAM,KAAK,mBAAmB,KAAK,OAAO,MAAM,SAAS,GAAG,IAAI;AAAA,EAClE;AAAA,EACA,QAAQ,SAAS,OAAO,OAAO,MAAM;AACnC,QAAI,WAAW,KAAK,UAChB,IAAI,SAAS,QACb,KAAK,KAAK;AAEd,QAAI,KAAK,MAAM,SAAS,CAAC,WAAW,GAAG;AACrC,UAAI,QAAQ,GAAG;AACb,gBAAQ;AAAA,MACV,WAAW,QAAQ,GAAG;AACpB,gBAAQ;AAAA,MACV;AAEA,aAAO,KAAK;AACV,0BAAkB,SAAS,CAAC,GAAG,OAAO,CAAC,KAAK,KAAK,QAAQ,SAAS,CAAC,CAAC;AAAA,MACtE;AAEA,aAAO,IAAI;AACT,WAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,EAAE,IAAI,GAAG,GAAG,GAAG,GAAG,KAAK;AACrD,aAAK,GAAG;AAAA,MACV;AAEA,WAAK,UAAU,KAAK,KAAK,KAAK,QAAQ,KAAK,OAAO,SAAS,CAAC,EAAE,SAAS,KAAK,UAAUZ,YAAW,KAAK,KAAK,UAAU,KAAK,IAAI,MAAM,KAAK;AAAA,IAC3I,OAAO;AACL,WAAK,OAAO,OAAO;AAAA,IACrB;AAAA,EACF;AAAA,EACA,WAAW,SAAS,UAAU,MAAM;AAClC,WAAO,yBAAyB,WAAW,IAAI,CAAC,EAAE;AAAA,EACpD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe,SAASa,eAAc,SAAS,MAAM;AACnD,WAAO,SAAS,OAAO,EAAE,IAAI,SAAU,QAAQ;AAC7C,aAAO,cAAe,QAAQ,SAAS,KAAK;AAAA,IAC9C,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB,SAAS,mBAAmB,aAAa,WAAW,OAAO;AAC7E,QAAI,IAAI,gBAAgB,WAAW,MAAM,IAAI,EAAE,SAAS,gBAAgB,WAAW,CAAC;AACpF,WAAO,QAAQ,EAAE,MAAM,KAAK,IAAI;AAAA,EAClC;AAAA,EACA,gBAAgB;AAAA,EAChB,qBAAqB,SAAS,oBAAoB,aAAa,WAAW,YAAY,UAAU;AAC9F,QAAI,IAAI,gBAAgB,aAAa,WAAW,YAAY,QAAQ;AAEpE,WAAO;AAAA,MACL,GAAG,EAAE;AAAA,MACL,GAAG,EAAE;AAAA,IACP;AAAA,EACF;AAAA,EACA,gBAAgB,SAAS,eAAe,OAAO,MAAM;AACnD,WAAO,QAAQ,CAAC;AAEhB,QAAI,UAAU,0BAA0B,0BAA0B,CAAC,GAAG,OAAO,KAAK,KAAK,KAAK,CAAC,GAAG,OAAO,KAAK,KAAK,KAAK,CAAC;AAEvH,SAAK,YAAY,YAAY,OAAO;AACpC,WAAO,CAAC,KAAK,SAAS,UAAU,UAAU,gBAAgB,SAAS,KAAK,SAAS,CAAC;AAAA,EACpF;AACF;AACA,SAAS,KAAK,KAAK,eAAe,gBAAgB;", "names": ["_isString", "_isNumber", "_isUndefined", "_wrapProgress", "_round", "_roundPrecise", "_splitSegment", "_getSampleIndex", "_reverseRawPath", "_copyMetaData", "_appendOrMerge", "_createPath", "_attrToObj", "line", "_setDoc", "_forceNonZeroScale", "_getDocScrollTop", "_getDocScrollLeft", "_svgOwner", "_isFixed", "_createSibling", "_consolidate", "_getCTM", "_placeSiblings", "_setMatrix", "Matrix2D", "_DEG2RAD", "_getGSAP", "_populateSegmentFromArray", "_getPropNum", "_relativize", "_segmentToRawPath", "_emptyFunc", "_originToPoint", "_getAlignMatrix", "_originToPoint2", "_align", "_addDimensionalPropTween", "_sliceModifier", "convertToPath"]}