{"version": 3, "sources": ["../../react/cjs/react-jsx-runtime.development.js", "../../react/jsx-runtime.js"], "sourcesContent": ["/**\r\n * @license React\r\n * react-jsx-runtime.development.js\r\n *\r\n * Copyright (c) Facebook, Inc. and its affiliates.\r\n *\r\n * This source code is licensed under the MIT license found in the\r\n * LICENSE file in the root directory of this source tree.\r\n */\r\n\r\n'use strict';\r\n\r\nif (process.env.NODE_ENV !== \"production\") {\r\n  (function() {\r\n'use strict';\r\n\r\nvar React = require('react');\r\n\r\n// ATTENTION\r\n// When adding new symbols to this file,\r\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\r\n// The Symbol used to tag the ReactElement-like types.\r\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\r\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\r\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\r\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\r\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\r\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\r\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\r\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\r\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\r\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\r\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\r\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\r\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\r\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\r\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\r\nfunction getIteratorFn(maybeIterable) {\r\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\r\n    return null;\r\n  }\r\n\r\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\r\n\r\n  if (typeof maybeIterator === 'function') {\r\n    return maybeIterator;\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\r\n\r\nfunction error(format) {\r\n  {\r\n    {\r\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\r\n        args[_key2 - 1] = arguments[_key2];\r\n      }\r\n\r\n      printWarning('error', format, args);\r\n    }\r\n  }\r\n}\r\n\r\nfunction printWarning(level, format, args) {\r\n  // When changing this logic, you might want to also\r\n  // update consoleWithStackDev.www.js as well.\r\n  {\r\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\r\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\r\n\r\n    if (stack !== '') {\r\n      format += '%s';\r\n      args = args.concat([stack]);\r\n    } // eslint-disable-next-line react-internal/safe-string-coercion\r\n\r\n\r\n    var argsWithFormat = args.map(function (item) {\r\n      return String(item);\r\n    }); // Careful: RN currently depends on this prefix\r\n\r\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\r\n    // breaks IE9: https://github.com/facebook/react/issues/13610\r\n    // eslint-disable-next-line react-internal/no-production-logging\r\n\r\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\r\n  }\r\n}\r\n\r\n// -----------------------------------------------------------------------------\r\n\r\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\r\nvar enableCacheElement = false;\r\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\r\n\r\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\r\n// stuff. Intended to enable React core members to more easily debug scheduling\r\n// issues in DEV builds.\r\n\r\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\r\n\r\nvar REACT_MODULE_REFERENCE;\r\n\r\n{\r\n  REACT_MODULE_REFERENCE = Symbol.for('react.module.reference');\r\n}\r\n\r\nfunction isValidElementType(type) {\r\n  if (typeof type === 'string' || typeof type === 'function') {\r\n    return true;\r\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\r\n\r\n\r\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\r\n    return true;\r\n  }\r\n\r\n  if (typeof type === 'object' && type !== null) {\r\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\r\n    // types supported by any Flight configuration anywhere since\r\n    // we don't know which Flight build this will end up being used\r\n    // with.\r\n    type.$$typeof === REACT_MODULE_REFERENCE || type.getModuleId !== undefined) {\r\n      return true;\r\n    }\r\n  }\r\n\r\n  return false;\r\n}\r\n\r\nfunction getWrappedName(outerType, innerType, wrapperName) {\r\n  var displayName = outerType.displayName;\r\n\r\n  if (displayName) {\r\n    return displayName;\r\n  }\r\n\r\n  var functionName = innerType.displayName || innerType.name || '';\r\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\r\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\r\n\r\n\r\nfunction getContextName(type) {\r\n  return type.displayName || 'Context';\r\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\r\n\r\n\r\nfunction getComponentNameFromType(type) {\r\n  if (type == null) {\r\n    // Host root, text node or just invalid type.\r\n    return null;\r\n  }\r\n\r\n  {\r\n    if (typeof type.tag === 'number') {\r\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\r\n    }\r\n  }\r\n\r\n  if (typeof type === 'function') {\r\n    return type.displayName || type.name || null;\r\n  }\r\n\r\n  if (typeof type === 'string') {\r\n    return type;\r\n  }\r\n\r\n  switch (type) {\r\n    case REACT_FRAGMENT_TYPE:\r\n      return 'Fragment';\r\n\r\n    case REACT_PORTAL_TYPE:\r\n      return 'Portal';\r\n\r\n    case REACT_PROFILER_TYPE:\r\n      return 'Profiler';\r\n\r\n    case REACT_STRICT_MODE_TYPE:\r\n      return 'StrictMode';\r\n\r\n    case REACT_SUSPENSE_TYPE:\r\n      return 'Suspense';\r\n\r\n    case REACT_SUSPENSE_LIST_TYPE:\r\n      return 'SuspenseList';\r\n\r\n  }\r\n\r\n  if (typeof type === 'object') {\r\n    switch (type.$$typeof) {\r\n      case REACT_CONTEXT_TYPE:\r\n        var context = type;\r\n        return getContextName(context) + '.Consumer';\r\n\r\n      case REACT_PROVIDER_TYPE:\r\n        var provider = type;\r\n        return getContextName(provider._context) + '.Provider';\r\n\r\n      case REACT_FORWARD_REF_TYPE:\r\n        return getWrappedName(type, type.render, 'ForwardRef');\r\n\r\n      case REACT_MEMO_TYPE:\r\n        var outerName = type.displayName || null;\r\n\r\n        if (outerName !== null) {\r\n          return outerName;\r\n        }\r\n\r\n        return getComponentNameFromType(type.type) || 'Memo';\r\n\r\n      case REACT_LAZY_TYPE:\r\n        {\r\n          var lazyComponent = type;\r\n          var payload = lazyComponent._payload;\r\n          var init = lazyComponent._init;\r\n\r\n          try {\r\n            return getComponentNameFromType(init(payload));\r\n          } catch (x) {\r\n            return null;\r\n          }\r\n        }\r\n\r\n      // eslint-disable-next-line no-fallthrough\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\nvar assign = Object.assign;\r\n\r\n// Helpers to patch console.logs to avoid logging during side-effect free\r\n// replaying on render function. This currently only patches the object\r\n// lazily which won't cover if the log function was extracted eagerly.\r\n// We could also eagerly patch the method.\r\nvar disabledDepth = 0;\r\nvar prevLog;\r\nvar prevInfo;\r\nvar prevWarn;\r\nvar prevError;\r\nvar prevGroup;\r\nvar prevGroupCollapsed;\r\nvar prevGroupEnd;\r\n\r\nfunction disabledLog() {}\r\n\r\ndisabledLog.__reactDisabledLog = true;\r\nfunction disableLogs() {\r\n  {\r\n    if (disabledDepth === 0) {\r\n      /* eslint-disable react-internal/no-production-logging */\r\n      prevLog = console.log;\r\n      prevInfo = console.info;\r\n      prevWarn = console.warn;\r\n      prevError = console.error;\r\n      prevGroup = console.group;\r\n      prevGroupCollapsed = console.groupCollapsed;\r\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\r\n\r\n      var props = {\r\n        configurable: true,\r\n        enumerable: true,\r\n        value: disabledLog,\r\n        writable: true\r\n      }; // $FlowFixMe Flow thinks console is immutable.\r\n\r\n      Object.defineProperties(console, {\r\n        info: props,\r\n        log: props,\r\n        warn: props,\r\n        error: props,\r\n        group: props,\r\n        groupCollapsed: props,\r\n        groupEnd: props\r\n      });\r\n      /* eslint-enable react-internal/no-production-logging */\r\n    }\r\n\r\n    disabledDepth++;\r\n  }\r\n}\r\nfunction reenableLogs() {\r\n  {\r\n    disabledDepth--;\r\n\r\n    if (disabledDepth === 0) {\r\n      /* eslint-disable react-internal/no-production-logging */\r\n      var props = {\r\n        configurable: true,\r\n        enumerable: true,\r\n        writable: true\r\n      }; // $FlowFixMe Flow thinks console is immutable.\r\n\r\n      Object.defineProperties(console, {\r\n        log: assign({}, props, {\r\n          value: prevLog\r\n        }),\r\n        info: assign({}, props, {\r\n          value: prevInfo\r\n        }),\r\n        warn: assign({}, props, {\r\n          value: prevWarn\r\n        }),\r\n        error: assign({}, props, {\r\n          value: prevError\r\n        }),\r\n        group: assign({}, props, {\r\n          value: prevGroup\r\n        }),\r\n        groupCollapsed: assign({}, props, {\r\n          value: prevGroupCollapsed\r\n        }),\r\n        groupEnd: assign({}, props, {\r\n          value: prevGroupEnd\r\n        })\r\n      });\r\n      /* eslint-enable react-internal/no-production-logging */\r\n    }\r\n\r\n    if (disabledDepth < 0) {\r\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\r\n    }\r\n  }\r\n}\r\n\r\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\r\nvar prefix;\r\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\r\n  {\r\n    if (prefix === undefined) {\r\n      // Extract the VM specific prefix used by each line.\r\n      try {\r\n        throw Error();\r\n      } catch (x) {\r\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\r\n        prefix = match && match[1] || '';\r\n      }\r\n    } // We use the prefix to ensure our stacks line up with native stack frames.\r\n\r\n\r\n    return '\\n' + prefix + name;\r\n  }\r\n}\r\nvar reentry = false;\r\nvar componentFrameCache;\r\n\r\n{\r\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\r\n  componentFrameCache = new PossiblyWeakMap();\r\n}\r\n\r\nfunction describeNativeComponentFrame(fn, construct) {\r\n  // If something asked for a stack inside a fake render, it should get ignored.\r\n  if ( !fn || reentry) {\r\n    return '';\r\n  }\r\n\r\n  {\r\n    var frame = componentFrameCache.get(fn);\r\n\r\n    if (frame !== undefined) {\r\n      return frame;\r\n    }\r\n  }\r\n\r\n  var control;\r\n  reentry = true;\r\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe It does accept undefined.\r\n\r\n  Error.prepareStackTrace = undefined;\r\n  var previousDispatcher;\r\n\r\n  {\r\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\r\n    // for warnings.\r\n\r\n    ReactCurrentDispatcher.current = null;\r\n    disableLogs();\r\n  }\r\n\r\n  try {\r\n    // This should throw.\r\n    if (construct) {\r\n      // Something should be setting the props in the constructor.\r\n      var Fake = function () {\r\n        throw Error();\r\n      }; // $FlowFixMe\r\n\r\n\r\n      Object.defineProperty(Fake.prototype, 'props', {\r\n        set: function () {\r\n          // We use a throwing setter instead of frozen or non-writable props\r\n          // because that won't throw in a non-strict mode function.\r\n          throw Error();\r\n        }\r\n      });\r\n\r\n      if (typeof Reflect === 'object' && Reflect.construct) {\r\n        // We construct a different control for this case to include any extra\r\n        // frames added by the construct call.\r\n        try {\r\n          Reflect.construct(Fake, []);\r\n        } catch (x) {\r\n          control = x;\r\n        }\r\n\r\n        Reflect.construct(fn, [], Fake);\r\n      } else {\r\n        try {\r\n          Fake.call();\r\n        } catch (x) {\r\n          control = x;\r\n        }\r\n\r\n        fn.call(Fake.prototype);\r\n      }\r\n    } else {\r\n      try {\r\n        throw Error();\r\n      } catch (x) {\r\n        control = x;\r\n      }\r\n\r\n      fn();\r\n    }\r\n  } catch (sample) {\r\n    // This is inlined manually because closure doesn't do it for us.\r\n    if (sample && control && typeof sample.stack === 'string') {\r\n      // This extracts the first frame from the sample that isn't also in the control.\r\n      // Skipping one frame that we assume is the frame that calls the two.\r\n      var sampleLines = sample.stack.split('\\n');\r\n      var controlLines = control.stack.split('\\n');\r\n      var s = sampleLines.length - 1;\r\n      var c = controlLines.length - 1;\r\n\r\n      while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\r\n        // We expect at least one stack frame to be shared.\r\n        // Typically this will be the root most one. However, stack frames may be\r\n        // cut off due to maximum stack limits. In this case, one maybe cut off\r\n        // earlier than the other. We assume that the sample is longer or the same\r\n        // and there for cut off earlier. So we should find the root most frame in\r\n        // the sample somewhere in the control.\r\n        c--;\r\n      }\r\n\r\n      for (; s >= 1 && c >= 0; s--, c--) {\r\n        // Next we find the first one that isn't the same which should be the\r\n        // frame that called our sample function and the control.\r\n        if (sampleLines[s] !== controlLines[c]) {\r\n          // In V8, the first line is describing the message but other VMs don't.\r\n          // If we're about to return the first line, and the control is also on the same\r\n          // line, that's a pretty good indicator that our sample threw at same line as\r\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\r\n          // This can happen if you passed a class to function component, or non-function.\r\n          if (s !== 1 || c !== 1) {\r\n            do {\r\n              s--;\r\n              c--; // We may still have similar intermediate frames from the construct call.\r\n              // The next one that isn't the same should be our match though.\r\n\r\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\r\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\r\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\r\n                // but we have a user-provided \"displayName\"\r\n                // splice it in to make the stack more readable.\r\n\r\n\r\n                if (fn.displayName && _frame.includes('<anonymous>')) {\r\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\r\n                }\r\n\r\n                {\r\n                  if (typeof fn === 'function') {\r\n                    componentFrameCache.set(fn, _frame);\r\n                  }\r\n                } // Return the line we found.\r\n\r\n\r\n                return _frame;\r\n              }\r\n            } while (s >= 1 && c >= 0);\r\n          }\r\n\r\n          break;\r\n        }\r\n      }\r\n    }\r\n  } finally {\r\n    reentry = false;\r\n\r\n    {\r\n      ReactCurrentDispatcher.current = previousDispatcher;\r\n      reenableLogs();\r\n    }\r\n\r\n    Error.prepareStackTrace = previousPrepareStackTrace;\r\n  } // Fallback to just using the name if we couldn't make it throw.\r\n\r\n\r\n  var name = fn ? fn.displayName || fn.name : '';\r\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\r\n\r\n  {\r\n    if (typeof fn === 'function') {\r\n      componentFrameCache.set(fn, syntheticFrame);\r\n    }\r\n  }\r\n\r\n  return syntheticFrame;\r\n}\r\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\r\n  {\r\n    return describeNativeComponentFrame(fn, false);\r\n  }\r\n}\r\n\r\nfunction shouldConstruct(Component) {\r\n  var prototype = Component.prototype;\r\n  return !!(prototype && prototype.isReactComponent);\r\n}\r\n\r\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\r\n\r\n  if (type == null) {\r\n    return '';\r\n  }\r\n\r\n  if (typeof type === 'function') {\r\n    {\r\n      return describeNativeComponentFrame(type, shouldConstruct(type));\r\n    }\r\n  }\r\n\r\n  if (typeof type === 'string') {\r\n    return describeBuiltInComponentFrame(type);\r\n  }\r\n\r\n  switch (type) {\r\n    case REACT_SUSPENSE_TYPE:\r\n      return describeBuiltInComponentFrame('Suspense');\r\n\r\n    case REACT_SUSPENSE_LIST_TYPE:\r\n      return describeBuiltInComponentFrame('SuspenseList');\r\n  }\r\n\r\n  if (typeof type === 'object') {\r\n    switch (type.$$typeof) {\r\n      case REACT_FORWARD_REF_TYPE:\r\n        return describeFunctionComponentFrame(type.render);\r\n\r\n      case REACT_MEMO_TYPE:\r\n        // Memo may contain any component type so we recursively resolve it.\r\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\r\n\r\n      case REACT_LAZY_TYPE:\r\n        {\r\n          var lazyComponent = type;\r\n          var payload = lazyComponent._payload;\r\n          var init = lazyComponent._init;\r\n\r\n          try {\r\n            // Lazy may contain any component type so we recursively resolve it.\r\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\r\n          } catch (x) {}\r\n        }\r\n    }\r\n  }\r\n\r\n  return '';\r\n}\r\n\r\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\r\n\r\nvar loggedTypeFailures = {};\r\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\r\n\r\nfunction setCurrentlyValidatingElement(element) {\r\n  {\r\n    if (element) {\r\n      var owner = element._owner;\r\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\r\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\r\n    } else {\r\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\r\n    }\r\n  }\r\n}\r\n\r\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\r\n  {\r\n    // $FlowFixMe This is okay but Flow doesn't know it.\r\n    var has = Function.call.bind(hasOwnProperty);\r\n\r\n    for (var typeSpecName in typeSpecs) {\r\n      if (has(typeSpecs, typeSpecName)) {\r\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\r\n        // fail the render phase where it didn't fail before. So we log it.\r\n        // After these have been cleaned up, we'll let them throw.\r\n\r\n        try {\r\n          // This is intentionally an invariant that gets caught. It's the same\r\n          // behavior as without this statement except with a better message.\r\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\r\n            // eslint-disable-next-line react-internal/prod-error-codes\r\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\r\n            err.name = 'Invariant Violation';\r\n            throw err;\r\n          }\r\n\r\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\r\n        } catch (ex) {\r\n          error$1 = ex;\r\n        }\r\n\r\n        if (error$1 && !(error$1 instanceof Error)) {\r\n          setCurrentlyValidatingElement(element);\r\n\r\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\r\n\r\n          setCurrentlyValidatingElement(null);\r\n        }\r\n\r\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\r\n          // Only monitor this failure once because there tends to be a lot of the\r\n          // same error.\r\n          loggedTypeFailures[error$1.message] = true;\r\n          setCurrentlyValidatingElement(element);\r\n\r\n          error('Failed %s type: %s', location, error$1.message);\r\n\r\n          setCurrentlyValidatingElement(null);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\r\n\r\nfunction isArray(a) {\r\n  return isArrayImpl(a);\r\n}\r\n\r\n/*\r\n * The `'' + value` pattern (used in in perf-sensitive code) throws for Symbol\r\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\r\n *\r\n * The functions in this module will throw an easier-to-understand,\r\n * easier-to-debug exception with a clear errors message message explaining the\r\n * problem. (Instead of a confusing exception thrown inside the implementation\r\n * of the `value` object).\r\n */\r\n// $FlowFixMe only called in DEV, so void return is not possible.\r\nfunction typeName(value) {\r\n  {\r\n    // toStringTag is needed for namespaced types like Temporal.Instant\r\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\r\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object';\r\n    return type;\r\n  }\r\n} // $FlowFixMe only called in DEV, so void return is not possible.\r\n\r\n\r\nfunction willCoercionThrow(value) {\r\n  {\r\n    try {\r\n      testStringCoercion(value);\r\n      return false;\r\n    } catch (e) {\r\n      return true;\r\n    }\r\n  }\r\n}\r\n\r\nfunction testStringCoercion(value) {\r\n  // If you ended up here by following an exception call stack, here's what's\r\n  // happened: you supplied an object or symbol value to React (as a prop, key,\r\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\r\n  // coerce it to a string using `'' + value`, an exception was thrown.\r\n  //\r\n  // The most common types that will cause this exception are `Symbol` instances\r\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\r\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\r\n  // exception. (Library authors do this to prevent users from using built-in\r\n  // numeric operators like `+` or comparison operators like `>=` because custom\r\n  // methods are needed to perform accurate arithmetic or comparison.)\r\n  //\r\n  // To fix the problem, coerce this object or symbol value to a string before\r\n  // passing it to React. The most reliable way is usually `String(value)`.\r\n  //\r\n  // To find which value is throwing, check the browser or debugger console.\r\n  // Before this exception was thrown, there should be `console.error` output\r\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\r\n  // problem and how that type was used: key, atrribute, input value prop, etc.\r\n  // In most cases, this console output also shows the component and its\r\n  // ancestor components where the exception happened.\r\n  //\r\n  // eslint-disable-next-line react-internal/safe-string-coercion\r\n  return '' + value;\r\n}\r\nfunction checkKeyStringCoercion(value) {\r\n  {\r\n    if (willCoercionThrow(value)) {\r\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before before using it here.', typeName(value));\r\n\r\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\r\n    }\r\n  }\r\n}\r\n\r\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\r\nvar RESERVED_PROPS = {\r\n  key: true,\r\n  ref: true,\r\n  __self: true,\r\n  __source: true\r\n};\r\nvar specialPropKeyWarningShown;\r\nvar specialPropRefWarningShown;\r\nvar didWarnAboutStringRefs;\r\n\r\n{\r\n  didWarnAboutStringRefs = {};\r\n}\r\n\r\nfunction hasValidRef(config) {\r\n  {\r\n    if (hasOwnProperty.call(config, 'ref')) {\r\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\r\n\r\n      if (getter && getter.isReactWarning) {\r\n        return false;\r\n      }\r\n    }\r\n  }\r\n\r\n  return config.ref !== undefined;\r\n}\r\n\r\nfunction hasValidKey(config) {\r\n  {\r\n    if (hasOwnProperty.call(config, 'key')) {\r\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\r\n\r\n      if (getter && getter.isReactWarning) {\r\n        return false;\r\n      }\r\n    }\r\n  }\r\n\r\n  return config.key !== undefined;\r\n}\r\n\r\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\r\n  {\r\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\r\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\r\n\r\n      if (!didWarnAboutStringRefs[componentName]) {\r\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\r\n\r\n        didWarnAboutStringRefs[componentName] = true;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nfunction defineKeyPropWarningGetter(props, displayName) {\r\n  {\r\n    var warnAboutAccessingKey = function () {\r\n      if (!specialPropKeyWarningShown) {\r\n        specialPropKeyWarningShown = true;\r\n\r\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\r\n      }\r\n    };\r\n\r\n    warnAboutAccessingKey.isReactWarning = true;\r\n    Object.defineProperty(props, 'key', {\r\n      get: warnAboutAccessingKey,\r\n      configurable: true\r\n    });\r\n  }\r\n}\r\n\r\nfunction defineRefPropWarningGetter(props, displayName) {\r\n  {\r\n    var warnAboutAccessingRef = function () {\r\n      if (!specialPropRefWarningShown) {\r\n        specialPropRefWarningShown = true;\r\n\r\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\r\n      }\r\n    };\r\n\r\n    warnAboutAccessingRef.isReactWarning = true;\r\n    Object.defineProperty(props, 'ref', {\r\n      get: warnAboutAccessingRef,\r\n      configurable: true\r\n    });\r\n  }\r\n}\r\n/**\r\n * Factory method to create a new React element. This no longer adheres to\r\n * the class pattern, so do not use new to call it. Also, instanceof check\r\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\r\n * if something is a React Element.\r\n *\r\n * @param {*} type\r\n * @param {*} props\r\n * @param {*} key\r\n * @param {string|object} ref\r\n * @param {*} owner\r\n * @param {*} self A *temporary* helper to detect places where `this` is\r\n * different from the `owner` when React.createElement is called, so that we\r\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\r\n * functions, and as long as `this` and owner are the same, there will be no\r\n * change in behavior.\r\n * @param {*} source An annotation object (added by a transpiler or otherwise)\r\n * indicating filename, line number, and/or other information.\r\n * @internal\r\n */\r\n\r\n\r\nvar ReactElement = function (type, key, ref, self, source, owner, props) {\r\n  var element = {\r\n    // This tag allows us to uniquely identify this as a React Element\r\n    $$typeof: REACT_ELEMENT_TYPE,\r\n    // Built-in properties that belong on the element\r\n    type: type,\r\n    key: key,\r\n    ref: ref,\r\n    props: props,\r\n    // Record the component responsible for creating this element.\r\n    _owner: owner\r\n  };\r\n\r\n  {\r\n    // The validation flag is currently mutative. We put it on\r\n    // an external backing store so that we can freeze the whole object.\r\n    // This can be replaced with a WeakMap once they are implemented in\r\n    // commonly used development environments.\r\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\r\n    // the validation flag non-enumerable (where possible, which should\r\n    // include every environment we run tests in), so the test framework\r\n    // ignores it.\r\n\r\n    Object.defineProperty(element._store, 'validated', {\r\n      configurable: false,\r\n      enumerable: false,\r\n      writable: true,\r\n      value: false\r\n    }); // self and source are DEV only properties.\r\n\r\n    Object.defineProperty(element, '_self', {\r\n      configurable: false,\r\n      enumerable: false,\r\n      writable: false,\r\n      value: self\r\n    }); // Two elements created in two different places should be considered\r\n    // equal for testing purposes and therefore we hide it from enumeration.\r\n\r\n    Object.defineProperty(element, '_source', {\r\n      configurable: false,\r\n      enumerable: false,\r\n      writable: false,\r\n      value: source\r\n    });\r\n\r\n    if (Object.freeze) {\r\n      Object.freeze(element.props);\r\n      Object.freeze(element);\r\n    }\r\n  }\r\n\r\n  return element;\r\n};\r\n/**\r\n * https://github.com/reactjs/rfcs/pull/107\r\n * @param {*} type\r\n * @param {object} props\r\n * @param {string} key\r\n */\r\n\r\nfunction jsxDEV(type, config, maybeKey, source, self) {\r\n  {\r\n    var propName; // Reserved names are extracted\r\n\r\n    var props = {};\r\n    var key = null;\r\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\r\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\r\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\r\n    // but as an intermediary step, we will use jsxDEV for everything except\r\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\r\n    // key is explicitly declared to be undefined or not.\r\n\r\n    if (maybeKey !== undefined) {\r\n      {\r\n        checkKeyStringCoercion(maybeKey);\r\n      }\r\n\r\n      key = '' + maybeKey;\r\n    }\r\n\r\n    if (hasValidKey(config)) {\r\n      {\r\n        checkKeyStringCoercion(config.key);\r\n      }\r\n\r\n      key = '' + config.key;\r\n    }\r\n\r\n    if (hasValidRef(config)) {\r\n      ref = config.ref;\r\n      warnIfStringRefCannotBeAutoConverted(config, self);\r\n    } // Remaining properties are added to a new props object\r\n\r\n\r\n    for (propName in config) {\r\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\r\n        props[propName] = config[propName];\r\n      }\r\n    } // Resolve default props\r\n\r\n\r\n    if (type && type.defaultProps) {\r\n      var defaultProps = type.defaultProps;\r\n\r\n      for (propName in defaultProps) {\r\n        if (props[propName] === undefined) {\r\n          props[propName] = defaultProps[propName];\r\n        }\r\n      }\r\n    }\r\n\r\n    if (key || ref) {\r\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\r\n\r\n      if (key) {\r\n        defineKeyPropWarningGetter(props, displayName);\r\n      }\r\n\r\n      if (ref) {\r\n        defineRefPropWarningGetter(props, displayName);\r\n      }\r\n    }\r\n\r\n    return ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\r\n  }\r\n}\r\n\r\nvar ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\r\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\r\n\r\nfunction setCurrentlyValidatingElement$1(element) {\r\n  {\r\n    if (element) {\r\n      var owner = element._owner;\r\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\r\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\r\n    } else {\r\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\r\n    }\r\n  }\r\n}\r\n\r\nvar propTypesMisspellWarningShown;\r\n\r\n{\r\n  propTypesMisspellWarningShown = false;\r\n}\r\n/**\r\n * Verifies the object is a ReactElement.\r\n * See https://reactjs.org/docs/react-api.html#isvalidelement\r\n * @param {?object} object\r\n * @return {boolean} True if `object` is a ReactElement.\r\n * @final\r\n */\r\n\r\n\r\nfunction isValidElement(object) {\r\n  {\r\n    return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\r\n  }\r\n}\r\n\r\nfunction getDeclarationErrorAddendum() {\r\n  {\r\n    if (ReactCurrentOwner$1.current) {\r\n      var name = getComponentNameFromType(ReactCurrentOwner$1.current.type);\r\n\r\n      if (name) {\r\n        return '\\n\\nCheck the render method of `' + name + '`.';\r\n      }\r\n    }\r\n\r\n    return '';\r\n  }\r\n}\r\n\r\nfunction getSourceInfoErrorAddendum(source) {\r\n  {\r\n    if (source !== undefined) {\r\n      var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\r\n      var lineNumber = source.lineNumber;\r\n      return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\r\n    }\r\n\r\n    return '';\r\n  }\r\n}\r\n/**\r\n * Warn if there's no key explicitly set on dynamic arrays of children or\r\n * object keys are not valid. This allows us to keep track of children between\r\n * updates.\r\n */\r\n\r\n\r\nvar ownerHasKeyUseWarning = {};\r\n\r\nfunction getCurrentComponentErrorInfo(parentType) {\r\n  {\r\n    var info = getDeclarationErrorAddendum();\r\n\r\n    if (!info) {\r\n      var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\r\n\r\n      if (parentName) {\r\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\r\n      }\r\n    }\r\n\r\n    return info;\r\n  }\r\n}\r\n/**\r\n * Warn if the element doesn't have an explicit key assigned to it.\r\n * This element is in an array. The array could grow and shrink or be\r\n * reordered. All children that haven't already been validated are required to\r\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\r\n * will only be shown once.\r\n *\r\n * @internal\r\n * @param {ReactElement} element Element that requires a key.\r\n * @param {*} parentType element's parent's type.\r\n */\r\n\r\n\r\nfunction validateExplicitKey(element, parentType) {\r\n  {\r\n    if (!element._store || element._store.validated || element.key != null) {\r\n      return;\r\n    }\r\n\r\n    element._store.validated = true;\r\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\r\n\r\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\r\n      return;\r\n    }\r\n\r\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\r\n    // property, it may be the creator of the child that's responsible for\r\n    // assigning it a key.\r\n\r\n    var childOwner = '';\r\n\r\n    if (element && element._owner && element._owner !== ReactCurrentOwner$1.current) {\r\n      // Give the component that originally created this child.\r\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\r\n    }\r\n\r\n    setCurrentlyValidatingElement$1(element);\r\n\r\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\r\n\r\n    setCurrentlyValidatingElement$1(null);\r\n  }\r\n}\r\n/**\r\n * Ensure that every element either is passed in a static location, in an\r\n * array with an explicit keys property defined, or in an object literal\r\n * with valid key property.\r\n *\r\n * @internal\r\n * @param {ReactNode} node Statically passed child of any type.\r\n * @param {*} parentType node's parent's type.\r\n */\r\n\r\n\r\nfunction validateChildKeys(node, parentType) {\r\n  {\r\n    if (typeof node !== 'object') {\r\n      return;\r\n    }\r\n\r\n    if (isArray(node)) {\r\n      for (var i = 0; i < node.length; i++) {\r\n        var child = node[i];\r\n\r\n        if (isValidElement(child)) {\r\n          validateExplicitKey(child, parentType);\r\n        }\r\n      }\r\n    } else if (isValidElement(node)) {\r\n      // This element was passed in a valid location.\r\n      if (node._store) {\r\n        node._store.validated = true;\r\n      }\r\n    } else if (node) {\r\n      var iteratorFn = getIteratorFn(node);\r\n\r\n      if (typeof iteratorFn === 'function') {\r\n        // Entry iterators used to provide implicit keys,\r\n        // but now we print a separate warning for them later.\r\n        if (iteratorFn !== node.entries) {\r\n          var iterator = iteratorFn.call(node);\r\n          var step;\r\n\r\n          while (!(step = iterator.next()).done) {\r\n            if (isValidElement(step.value)) {\r\n              validateExplicitKey(step.value, parentType);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n/**\r\n * Given an element, validate that its props follow the propTypes definition,\r\n * provided by the type.\r\n *\r\n * @param {ReactElement} element\r\n */\r\n\r\n\r\nfunction validatePropTypes(element) {\r\n  {\r\n    var type = element.type;\r\n\r\n    if (type === null || type === undefined || typeof type === 'string') {\r\n      return;\r\n    }\r\n\r\n    var propTypes;\r\n\r\n    if (typeof type === 'function') {\r\n      propTypes = type.propTypes;\r\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\r\n    // Inner props are checked in the reconciler.\r\n    type.$$typeof === REACT_MEMO_TYPE)) {\r\n      propTypes = type.propTypes;\r\n    } else {\r\n      return;\r\n    }\r\n\r\n    if (propTypes) {\r\n      // Intentionally inside to avoid triggering lazy initializers:\r\n      var name = getComponentNameFromType(type);\r\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\r\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\r\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\r\n\r\n      var _name = getComponentNameFromType(type);\r\n\r\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\r\n    }\r\n\r\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\r\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\r\n    }\r\n  }\r\n}\r\n/**\r\n * Given a fragment, validate that it can only be provided with fragment props\r\n * @param {ReactElement} fragment\r\n */\r\n\r\n\r\nfunction validateFragmentProps(fragment) {\r\n  {\r\n    var keys = Object.keys(fragment.props);\r\n\r\n    for (var i = 0; i < keys.length; i++) {\r\n      var key = keys[i];\r\n\r\n      if (key !== 'children' && key !== 'key') {\r\n        setCurrentlyValidatingElement$1(fragment);\r\n\r\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\r\n\r\n        setCurrentlyValidatingElement$1(null);\r\n        break;\r\n      }\r\n    }\r\n\r\n    if (fragment.ref !== null) {\r\n      setCurrentlyValidatingElement$1(fragment);\r\n\r\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\r\n\r\n      setCurrentlyValidatingElement$1(null);\r\n    }\r\n  }\r\n}\r\n\r\nvar didWarnAboutKeySpread = {};\r\nfunction jsxWithValidation(type, props, key, isStaticChildren, source, self) {\r\n  {\r\n    var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\r\n    // succeed and there will likely be errors in render.\r\n\r\n    if (!validType) {\r\n      var info = '';\r\n\r\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\r\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\r\n      }\r\n\r\n      var sourceInfo = getSourceInfoErrorAddendum(source);\r\n\r\n      if (sourceInfo) {\r\n        info += sourceInfo;\r\n      } else {\r\n        info += getDeclarationErrorAddendum();\r\n      }\r\n\r\n      var typeString;\r\n\r\n      if (type === null) {\r\n        typeString = 'null';\r\n      } else if (isArray(type)) {\r\n        typeString = 'array';\r\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\r\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\r\n        info = ' Did you accidentally export a JSX literal instead of a component?';\r\n      } else {\r\n        typeString = typeof type;\r\n      }\r\n\r\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\r\n    }\r\n\r\n    var element = jsxDEV(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\r\n    // TODO: Drop this when these are no longer allowed as the type argument.\r\n\r\n    if (element == null) {\r\n      return element;\r\n    } // Skip key warning if the type isn't valid since our key validation logic\r\n    // doesn't expect a non-string/function type and can throw confusing errors.\r\n    // We don't want exception behavior to differ between dev and prod.\r\n    // (Rendering will throw with a helpful message and as soon as the type is\r\n    // fixed, the key warnings will appear.)\r\n\r\n\r\n    if (validType) {\r\n      var children = props.children;\r\n\r\n      if (children !== undefined) {\r\n        if (isStaticChildren) {\r\n          if (isArray(children)) {\r\n            for (var i = 0; i < children.length; i++) {\r\n              validateChildKeys(children[i], type);\r\n            }\r\n\r\n            if (Object.freeze) {\r\n              Object.freeze(children);\r\n            }\r\n          } else {\r\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\r\n          }\r\n        } else {\r\n          validateChildKeys(children, type);\r\n        }\r\n      }\r\n    }\r\n\r\n    {\r\n      if (hasOwnProperty.call(props, 'key')) {\r\n        var componentName = getComponentNameFromType(type);\r\n        var keys = Object.keys(props).filter(function (k) {\r\n          return k !== 'key';\r\n        });\r\n        var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\r\n\r\n        if (!didWarnAboutKeySpread[componentName + beforeExample]) {\r\n          var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\r\n\r\n          error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\r\n\r\n          didWarnAboutKeySpread[componentName + beforeExample] = true;\r\n        }\r\n      }\r\n    }\r\n\r\n    if (type === REACT_FRAGMENT_TYPE) {\r\n      validateFragmentProps(element);\r\n    } else {\r\n      validatePropTypes(element);\r\n    }\r\n\r\n    return element;\r\n  }\r\n} // These two functions exist to still get child warnings in dev\r\n// even with the prod transform. This means that jsxDEV is purely\r\n// opt-in behavior for better messages but that we won't stop\r\n// giving you warnings if you use production apis.\r\n\r\nfunction jsxWithValidationStatic(type, props, key) {\r\n  {\r\n    return jsxWithValidation(type, props, key, true);\r\n  }\r\n}\r\nfunction jsxWithValidationDynamic(type, props, key) {\r\n  {\r\n    return jsxWithValidation(type, props, key, false);\r\n  }\r\n}\r\n\r\nvar jsx =  jsxWithValidationDynamic ; // we may want to special case jsxs internally to take advantage of static children.\r\n// for now we can ship identical prod functions\r\n\r\nvar jsxs =  jsxWithValidationStatic ;\r\n\r\nexports.Fragment = REACT_FRAGMENT_TYPE;\r\nexports.jsx = jsx;\r\nexports.jsxs = jsxs;\r\n  })();\r\n}\r\n", "'use strict';\r\n\r\nif (process.env.NODE_ENV === 'production') {\r\n  module.exports = require('./cjs/react-jsx-runtime.production.min.js');\r\n} else {\r\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\r\n}\r\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAYA,QAAI,MAAuC;AACzC,OAAC,WAAW;AACd;AAEA,YAAI,QAAQ;AAMZ,YAAI,qBAAqB,OAAO,IAAI,eAAe;AACnD,YAAI,oBAAoB,OAAO,IAAI,cAAc;AACjD,YAAI,sBAAsB,OAAO,IAAI,gBAAgB;AACrD,YAAI,yBAAyB,OAAO,IAAI,mBAAmB;AAC3D,YAAI,sBAAsB,OAAO,IAAI,gBAAgB;AACrD,YAAI,sBAAsB,OAAO,IAAI,gBAAgB;AACrD,YAAI,qBAAqB,OAAO,IAAI,eAAe;AACnD,YAAI,yBAAyB,OAAO,IAAI,mBAAmB;AAC3D,YAAI,sBAAsB,OAAO,IAAI,gBAAgB;AACrD,YAAI,2BAA2B,OAAO,IAAI,qBAAqB;AAC/D,YAAI,kBAAkB,OAAO,IAAI,YAAY;AAC7C,YAAI,kBAAkB,OAAO,IAAI,YAAY;AAC7C,YAAI,uBAAuB,OAAO,IAAI,iBAAiB;AACvD,YAAI,wBAAwB,OAAO;AACnC,YAAI,uBAAuB;AAC3B,iBAAS,cAAc,eAAe;AACpC,cAAI,kBAAkB,QAAQ,OAAO,kBAAkB,UAAU;AAC/D,mBAAO;AAAA,UACT;AAEA,cAAI,gBAAgB,yBAAyB,cAAc,qBAAqB,KAAK,cAAc,oBAAoB;AAEvH,cAAI,OAAO,kBAAkB,YAAY;AACvC,mBAAO;AAAA,UACT;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,uBAAuB,MAAM;AAEjC,iBAAS,MAAM,QAAQ;AACrB;AACE;AACE,uBAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACjH,qBAAK,QAAQ,CAAC,IAAI,UAAU,KAAK;AAAA,cACnC;AAEA,2BAAa,SAAS,QAAQ,IAAI;AAAA,YACpC;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,aAAa,OAAO,QAAQ,MAAM;AAGzC;AACE,gBAAIA,0BAAyB,qBAAqB;AAClD,gBAAI,QAAQA,wBAAuB,iBAAiB;AAEpD,gBAAI,UAAU,IAAI;AAChB,wBAAU;AACV,qBAAO,KAAK,OAAO,CAAC,KAAK,CAAC;AAAA,YAC5B;AAGA,gBAAI,iBAAiB,KAAK,IAAI,SAAU,MAAM;AAC5C,qBAAO,OAAO,IAAI;AAAA,YACpB,CAAC;AAED,2BAAe,QAAQ,cAAc,MAAM;AAI3C,qBAAS,UAAU,MAAM,KAAK,QAAQ,KAAK,GAAG,SAAS,cAAc;AAAA,UACvE;AAAA,QACF;AAIA,YAAI,iBAAiB;AACrB,YAAI,qBAAqB;AACzB,YAAI,0BAA0B;AAE9B,YAAI,qBAAqB;AAIzB,YAAI,qBAAqB;AAEzB,YAAI;AAEJ;AACE,mCAAyB,OAAO,IAAI,wBAAwB;AAAA,QAC9D;AAEA,iBAAS,mBAAmB,MAAM;AAChC,cAAI,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAC1D,mBAAO;AAAA,UACT;AAGA,cAAI,SAAS,uBAAuB,SAAS,uBAAuB,sBAAuB,SAAS,0BAA0B,SAAS,uBAAuB,SAAS,4BAA4B,sBAAuB,SAAS,wBAAwB,kBAAmB,sBAAuB,yBAA0B;AAC7T,mBAAO;AAAA,UACT;AAEA,cAAI,OAAO,SAAS,YAAY,SAAS,MAAM;AAC7C,gBAAI,KAAK,aAAa,mBAAmB,KAAK,aAAa,mBAAmB,KAAK,aAAa,uBAAuB,KAAK,aAAa,sBAAsB,KAAK,aAAa;AAAA;AAAA;AAAA;AAAA,YAIjL,KAAK,aAAa,0BAA0B,KAAK,gBAAgB,QAAW;AAC1E,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,iBAAS,eAAe,WAAW,WAAW,aAAa;AACzD,cAAI,cAAc,UAAU;AAE5B,cAAI,aAAa;AACf,mBAAO;AAAA,UACT;AAEA,cAAI,eAAe,UAAU,eAAe,UAAU,QAAQ;AAC9D,iBAAO,iBAAiB,KAAK,cAAc,MAAM,eAAe,MAAM;AAAA,QACxE;AAGA,iBAAS,eAAe,MAAM;AAC5B,iBAAO,KAAK,eAAe;AAAA,QAC7B;AAGA,iBAAS,yBAAyB,MAAM;AACtC,cAAI,QAAQ,MAAM;AAEhB,mBAAO;AAAA,UACT;AAEA;AACE,gBAAI,OAAO,KAAK,QAAQ,UAAU;AAChC,oBAAM,mHAAwH;AAAA,YAChI;AAAA,UACF;AAEA,cAAI,OAAO,SAAS,YAAY;AAC9B,mBAAO,KAAK,eAAe,KAAK,QAAQ;AAAA,UAC1C;AAEA,cAAI,OAAO,SAAS,UAAU;AAC5B,mBAAO;AAAA,UACT;AAEA,kBAAQ,MAAM;AAAA,YACZ,KAAK;AACH,qBAAO;AAAA,YAET,KAAK;AACH,qBAAO;AAAA,YAET,KAAK;AACH,qBAAO;AAAA,YAET,KAAK;AACH,qBAAO;AAAA,YAET,KAAK;AACH,qBAAO;AAAA,YAET,KAAK;AACH,qBAAO;AAAA,UAEX;AAEA,cAAI,OAAO,SAAS,UAAU;AAC5B,oBAAQ,KAAK,UAAU;AAAA,cACrB,KAAK;AACH,oBAAI,UAAU;AACd,uBAAO,eAAe,OAAO,IAAI;AAAA,cAEnC,KAAK;AACH,oBAAI,WAAW;AACf,uBAAO,eAAe,SAAS,QAAQ,IAAI;AAAA,cAE7C,KAAK;AACH,uBAAO,eAAe,MAAM,KAAK,QAAQ,YAAY;AAAA,cAEvD,KAAK;AACH,oBAAI,YAAY,KAAK,eAAe;AAEpC,oBAAI,cAAc,MAAM;AACtB,yBAAO;AAAA,gBACT;AAEA,uBAAO,yBAAyB,KAAK,IAAI,KAAK;AAAA,cAEhD,KAAK,iBACH;AACE,oBAAI,gBAAgB;AACpB,oBAAI,UAAU,cAAc;AAC5B,oBAAI,OAAO,cAAc;AAEzB,oBAAI;AACF,yBAAO,yBAAyB,KAAK,OAAO,CAAC;AAAA,gBAC/C,SAAS,GAAG;AACV,yBAAO;AAAA,gBACT;AAAA,cACF;AAAA,YAGJ;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,SAAS,OAAO;AAMpB,YAAI,gBAAgB;AACpB,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI;AAEJ,iBAAS,cAAc;AAAA,QAAC;AAExB,oBAAY,qBAAqB;AACjC,iBAAS,cAAc;AACrB;AACE,gBAAI,kBAAkB,GAAG;AAEvB,wBAAU,QAAQ;AAClB,yBAAW,QAAQ;AACnB,yBAAW,QAAQ;AACnB,0BAAY,QAAQ;AACpB,0BAAY,QAAQ;AACpB,mCAAqB,QAAQ;AAC7B,6BAAe,QAAQ;AAEvB,kBAAI,QAAQ;AAAA,gBACV,cAAc;AAAA,gBACd,YAAY;AAAA,gBACZ,OAAO;AAAA,gBACP,UAAU;AAAA,cACZ;AAEA,qBAAO,iBAAiB,SAAS;AAAA,gBAC/B,MAAM;AAAA,gBACN,KAAK;AAAA,gBACL,MAAM;AAAA,gBACN,OAAO;AAAA,gBACP,OAAO;AAAA,gBACP,gBAAgB;AAAA,gBAChB,UAAU;AAAA,cACZ,CAAC;AAAA,YAEH;AAEA;AAAA,UACF;AAAA,QACF;AACA,iBAAS,eAAe;AACtB;AACE;AAEA,gBAAI,kBAAkB,GAAG;AAEvB,kBAAI,QAAQ;AAAA,gBACV,cAAc;AAAA,gBACd,YAAY;AAAA,gBACZ,UAAU;AAAA,cACZ;AAEA,qBAAO,iBAAiB,SAAS;AAAA,gBAC/B,KAAK,OAAO,CAAC,GAAG,OAAO;AAAA,kBACrB,OAAO;AAAA,gBACT,CAAC;AAAA,gBACD,MAAM,OAAO,CAAC,GAAG,OAAO;AAAA,kBACtB,OAAO;AAAA,gBACT,CAAC;AAAA,gBACD,MAAM,OAAO,CAAC,GAAG,OAAO;AAAA,kBACtB,OAAO;AAAA,gBACT,CAAC;AAAA,gBACD,OAAO,OAAO,CAAC,GAAG,OAAO;AAAA,kBACvB,OAAO;AAAA,gBACT,CAAC;AAAA,gBACD,OAAO,OAAO,CAAC,GAAG,OAAO;AAAA,kBACvB,OAAO;AAAA,gBACT,CAAC;AAAA,gBACD,gBAAgB,OAAO,CAAC,GAAG,OAAO;AAAA,kBAChC,OAAO;AAAA,gBACT,CAAC;AAAA,gBACD,UAAU,OAAO,CAAC,GAAG,OAAO;AAAA,kBAC1B,OAAO;AAAA,gBACT,CAAC;AAAA,cACH,CAAC;AAAA,YAEH;AAEA,gBAAI,gBAAgB,GAAG;AACrB,oBAAM,8EAAmF;AAAA,YAC3F;AAAA,UACF;AAAA,QACF;AAEA,YAAI,yBAAyB,qBAAqB;AAClD,YAAI;AACJ,iBAAS,8BAA8B,MAAM,QAAQ,SAAS;AAC5D;AACE,gBAAI,WAAW,QAAW;AAExB,kBAAI;AACF,sBAAM,MAAM;AAAA,cACd,SAAS,GAAG;AACV,oBAAI,QAAQ,EAAE,MAAM,KAAK,EAAE,MAAM,cAAc;AAC/C,yBAAS,SAAS,MAAM,CAAC,KAAK;AAAA,cAChC;AAAA,YACF;AAGA,mBAAO,OAAO,SAAS;AAAA,UACzB;AAAA,QACF;AACA,YAAI,UAAU;AACd,YAAI;AAEJ;AACE,cAAI,kBAAkB,OAAO,YAAY,aAAa,UAAU;AAChE,gCAAsB,IAAI,gBAAgB;AAAA,QAC5C;AAEA,iBAAS,6BAA6B,IAAI,WAAW;AAEnD,cAAK,CAAC,MAAM,SAAS;AACnB,mBAAO;AAAA,UACT;AAEA;AACE,gBAAI,QAAQ,oBAAoB,IAAI,EAAE;AAEtC,gBAAI,UAAU,QAAW;AACvB,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,cAAI;AACJ,oBAAU;AACV,cAAI,4BAA4B,MAAM;AAEtC,gBAAM,oBAAoB;AAC1B,cAAI;AAEJ;AACE,iCAAqB,uBAAuB;AAG5C,mCAAuB,UAAU;AACjC,wBAAY;AAAA,UACd;AAEA,cAAI;AAEF,gBAAI,WAAW;AAEb,kBAAI,OAAO,WAAY;AACrB,sBAAM,MAAM;AAAA,cACd;AAGA,qBAAO,eAAe,KAAK,WAAW,SAAS;AAAA,gBAC7C,KAAK,WAAY;AAGf,wBAAM,MAAM;AAAA,gBACd;AAAA,cACF,CAAC;AAED,kBAAI,OAAO,YAAY,YAAY,QAAQ,WAAW;AAGpD,oBAAI;AACF,0BAAQ,UAAU,MAAM,CAAC,CAAC;AAAA,gBAC5B,SAAS,GAAG;AACV,4BAAU;AAAA,gBACZ;AAEA,wBAAQ,UAAU,IAAI,CAAC,GAAG,IAAI;AAAA,cAChC,OAAO;AACL,oBAAI;AACF,uBAAK,KAAK;AAAA,gBACZ,SAAS,GAAG;AACV,4BAAU;AAAA,gBACZ;AAEA,mBAAG,KAAK,KAAK,SAAS;AAAA,cACxB;AAAA,YACF,OAAO;AACL,kBAAI;AACF,sBAAM,MAAM;AAAA,cACd,SAAS,GAAG;AACV,0BAAU;AAAA,cACZ;AAEA,iBAAG;AAAA,YACL;AAAA,UACF,SAAS,QAAQ;AAEf,gBAAI,UAAU,WAAW,OAAO,OAAO,UAAU,UAAU;AAGzD,kBAAI,cAAc,OAAO,MAAM,MAAM,IAAI;AACzC,kBAAI,eAAe,QAAQ,MAAM,MAAM,IAAI;AAC3C,kBAAI,IAAI,YAAY,SAAS;AAC7B,kBAAI,IAAI,aAAa,SAAS;AAE9B,qBAAO,KAAK,KAAK,KAAK,KAAK,YAAY,CAAC,MAAM,aAAa,CAAC,GAAG;AAO7D;AAAA,cACF;AAEA,qBAAO,KAAK,KAAK,KAAK,GAAG,KAAK,KAAK;AAGjC,oBAAI,YAAY,CAAC,MAAM,aAAa,CAAC,GAAG;AAMtC,sBAAI,MAAM,KAAK,MAAM,GAAG;AACtB,uBAAG;AACD;AACA;AAGA,0BAAI,IAAI,KAAK,YAAY,CAAC,MAAM,aAAa,CAAC,GAAG;AAE/C,4BAAI,SAAS,OAAO,YAAY,CAAC,EAAE,QAAQ,YAAY,MAAM;AAK7D,4BAAI,GAAG,eAAe,OAAO,SAAS,aAAa,GAAG;AACpD,mCAAS,OAAO,QAAQ,eAAe,GAAG,WAAW;AAAA,wBACvD;AAEA;AACE,8BAAI,OAAO,OAAO,YAAY;AAC5B,gDAAoB,IAAI,IAAI,MAAM;AAAA,0BACpC;AAAA,wBACF;AAGA,+BAAO;AAAA,sBACT;AAAA,oBACF,SAAS,KAAK,KAAK,KAAK;AAAA,kBAC1B;AAEA;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF,UAAE;AACA,sBAAU;AAEV;AACE,qCAAuB,UAAU;AACjC,2BAAa;AAAA,YACf;AAEA,kBAAM,oBAAoB;AAAA,UAC5B;AAGA,cAAI,OAAO,KAAK,GAAG,eAAe,GAAG,OAAO;AAC5C,cAAI,iBAAiB,OAAO,8BAA8B,IAAI,IAAI;AAElE;AACE,gBAAI,OAAO,OAAO,YAAY;AAC5B,kCAAoB,IAAI,IAAI,cAAc;AAAA,YAC5C;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AACA,iBAAS,+BAA+B,IAAI,QAAQ,SAAS;AAC3D;AACE,mBAAO,6BAA6B,IAAI,KAAK;AAAA,UAC/C;AAAA,QACF;AAEA,iBAAS,gBAAgB,WAAW;AAClC,cAAI,YAAY,UAAU;AAC1B,iBAAO,CAAC,EAAE,aAAa,UAAU;AAAA,QACnC;AAEA,iBAAS,qCAAqC,MAAM,QAAQ,SAAS;AAEnE,cAAI,QAAQ,MAAM;AAChB,mBAAO;AAAA,UACT;AAEA,cAAI,OAAO,SAAS,YAAY;AAC9B;AACE,qBAAO,6BAA6B,MAAM,gBAAgB,IAAI,CAAC;AAAA,YACjE;AAAA,UACF;AAEA,cAAI,OAAO,SAAS,UAAU;AAC5B,mBAAO,8BAA8B,IAAI;AAAA,UAC3C;AAEA,kBAAQ,MAAM;AAAA,YACZ,KAAK;AACH,qBAAO,8BAA8B,UAAU;AAAA,YAEjD,KAAK;AACH,qBAAO,8BAA8B,cAAc;AAAA,UACvD;AAEA,cAAI,OAAO,SAAS,UAAU;AAC5B,oBAAQ,KAAK,UAAU;AAAA,cACrB,KAAK;AACH,uBAAO,+BAA+B,KAAK,MAAM;AAAA,cAEnD,KAAK;AAEH,uBAAO,qCAAqC,KAAK,MAAM,QAAQ,OAAO;AAAA,cAExE,KAAK,iBACH;AACE,oBAAI,gBAAgB;AACpB,oBAAI,UAAU,cAAc;AAC5B,oBAAI,OAAO,cAAc;AAEzB,oBAAI;AAEF,yBAAO,qCAAqC,KAAK,OAAO,GAAG,QAAQ,OAAO;AAAA,gBAC5E,SAAS,GAAG;AAAA,gBAAC;AAAA,cACf;AAAA,YACJ;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,iBAAiB,OAAO,UAAU;AAEtC,YAAI,qBAAqB,CAAC;AAC1B,YAAI,yBAAyB,qBAAqB;AAElD,iBAAS,8BAA8B,SAAS;AAC9C;AACE,gBAAI,SAAS;AACX,kBAAI,QAAQ,QAAQ;AACpB,kBAAI,QAAQ,qCAAqC,QAAQ,MAAM,QAAQ,SAAS,QAAQ,MAAM,OAAO,IAAI;AACzG,qCAAuB,mBAAmB,KAAK;AAAA,YACjD,OAAO;AACL,qCAAuB,mBAAmB,IAAI;AAAA,YAChD;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,eAAe,WAAW,QAAQ,UAAU,eAAe,SAAS;AAC3E;AAEE,gBAAI,MAAM,SAAS,KAAK,KAAK,cAAc;AAE3C,qBAAS,gBAAgB,WAAW;AAClC,kBAAI,IAAI,WAAW,YAAY,GAAG;AAChC,oBAAI,UAAU;AAId,oBAAI;AAGF,sBAAI,OAAO,UAAU,YAAY,MAAM,YAAY;AAEjD,wBAAI,MAAM,OAAO,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,+FAAoG,OAAO,UAAU,YAAY,IAAI,iGAAsG;AAC3U,wBAAI,OAAO;AACX,0BAAM;AAAA,kBACR;AAEA,4BAAU,UAAU,YAAY,EAAE,QAAQ,cAAc,eAAe,UAAU,MAAM,8CAA8C;AAAA,gBACvI,SAAS,IAAI;AACX,4BAAU;AAAA,gBACZ;AAEA,oBAAI,WAAW,EAAE,mBAAmB,QAAQ;AAC1C,gDAA8B,OAAO;AAErC,wBAAM,4RAAqT,iBAAiB,eAAe,UAAU,cAAc,OAAO,OAAO;AAEjY,gDAA8B,IAAI;AAAA,gBACpC;AAEA,oBAAI,mBAAmB,SAAS,EAAE,QAAQ,WAAW,qBAAqB;AAGxE,qCAAmB,QAAQ,OAAO,IAAI;AACtC,gDAA8B,OAAO;AAErC,wBAAM,sBAAsB,UAAU,QAAQ,OAAO;AAErD,gDAA8B,IAAI;AAAA,gBACpC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,YAAI,cAAc,MAAM;AAExB,iBAAS,QAAQ,GAAG;AAClB,iBAAO,YAAY,CAAC;AAAA,QACtB;AAYA,iBAAS,SAAS,OAAO;AACvB;AAEE,gBAAI,iBAAiB,OAAO,WAAW,cAAc,OAAO;AAC5D,gBAAI,OAAO,kBAAkB,MAAM,OAAO,WAAW,KAAK,MAAM,YAAY,QAAQ;AACpF,mBAAO;AAAA,UACT;AAAA,QACF;AAGA,iBAAS,kBAAkB,OAAO;AAChC;AACE,gBAAI;AACF,iCAAmB,KAAK;AACxB,qBAAO;AAAA,YACT,SAAS,GAAG;AACV,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,mBAAmB,OAAO;AAwBjC,iBAAO,KAAK;AAAA,QACd;AACA,iBAAS,uBAAuB,OAAO;AACrC;AACE,gBAAI,kBAAkB,KAAK,GAAG;AAC5B,oBAAM,mHAAwH,SAAS,KAAK,CAAC;AAE7I,qBAAO,mBAAmB,KAAK;AAAA,YACjC;AAAA,UACF;AAAA,QACF;AAEA,YAAI,oBAAoB,qBAAqB;AAC7C,YAAI,iBAAiB;AAAA,UACnB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,UAAU;AAAA,QACZ;AACA,YAAI;AACJ,YAAI;AACJ,YAAI;AAEJ;AACE,mCAAyB,CAAC;AAAA,QAC5B;AAEA,iBAAS,YAAY,QAAQ;AAC3B;AACE,gBAAI,eAAe,KAAK,QAAQ,KAAK,GAAG;AACtC,kBAAI,SAAS,OAAO,yBAAyB,QAAQ,KAAK,EAAE;AAE5D,kBAAI,UAAU,OAAO,gBAAgB;AACnC,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAEA,iBAAO,OAAO,QAAQ;AAAA,QACxB;AAEA,iBAAS,YAAY,QAAQ;AAC3B;AACE,gBAAI,eAAe,KAAK,QAAQ,KAAK,GAAG;AACtC,kBAAI,SAAS,OAAO,yBAAyB,QAAQ,KAAK,EAAE;AAE5D,kBAAI,UAAU,OAAO,gBAAgB;AACnC,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAEA,iBAAO,OAAO,QAAQ;AAAA,QACxB;AAEA,iBAAS,qCAAqC,QAAQ,MAAM;AAC1D;AACE,gBAAI,OAAO,OAAO,QAAQ,YAAY,kBAAkB,WAAW,QAAQ,kBAAkB,QAAQ,cAAc,MAAM;AACvH,kBAAI,gBAAgB,yBAAyB,kBAAkB,QAAQ,IAAI;AAE3E,kBAAI,CAAC,uBAAuB,aAAa,GAAG;AAC1C,sBAAM,6VAAsX,yBAAyB,kBAAkB,QAAQ,IAAI,GAAG,OAAO,GAAG;AAEhc,uCAAuB,aAAa,IAAI;AAAA,cAC1C;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,2BAA2B,OAAO,aAAa;AACtD;AACE,gBAAI,wBAAwB,WAAY;AACtC,kBAAI,CAAC,4BAA4B;AAC/B,6CAA6B;AAE7B,sBAAM,6OAA4P,WAAW;AAAA,cAC/Q;AAAA,YACF;AAEA,kCAAsB,iBAAiB;AACvC,mBAAO,eAAe,OAAO,OAAO;AAAA,cAClC,KAAK;AAAA,cACL,cAAc;AAAA,YAChB,CAAC;AAAA,UACH;AAAA,QACF;AAEA,iBAAS,2BAA2B,OAAO,aAAa;AACtD;AACE,gBAAI,wBAAwB,WAAY;AACtC,kBAAI,CAAC,4BAA4B;AAC/B,6CAA6B;AAE7B,sBAAM,6OAA4P,WAAW;AAAA,cAC/Q;AAAA,YACF;AAEA,kCAAsB,iBAAiB;AACvC,mBAAO,eAAe,OAAO,OAAO;AAAA,cAClC,KAAK;AAAA,cACL,cAAc;AAAA,YAChB,CAAC;AAAA,UACH;AAAA,QACF;AAuBA,YAAI,eAAe,SAAU,MAAM,KAAK,KAAK,MAAM,QAAQ,OAAO,OAAO;AACvE,cAAI,UAAU;AAAA;AAAA,YAEZ,UAAU;AAAA;AAAA,YAEV;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA;AAAA,YAEA,QAAQ;AAAA,UACV;AAEA;AAKE,oBAAQ,SAAS,CAAC;AAKlB,mBAAO,eAAe,QAAQ,QAAQ,aAAa;AAAA,cACjD,cAAc;AAAA,cACd,YAAY;AAAA,cACZ,UAAU;AAAA,cACV,OAAO;AAAA,YACT,CAAC;AAED,mBAAO,eAAe,SAAS,SAAS;AAAA,cACtC,cAAc;AAAA,cACd,YAAY;AAAA,cACZ,UAAU;AAAA,cACV,OAAO;AAAA,YACT,CAAC;AAGD,mBAAO,eAAe,SAAS,WAAW;AAAA,cACxC,cAAc;AAAA,cACd,YAAY;AAAA,cACZ,UAAU;AAAA,cACV,OAAO;AAAA,YACT,CAAC;AAED,gBAAI,OAAO,QAAQ;AACjB,qBAAO,OAAO,QAAQ,KAAK;AAC3B,qBAAO,OAAO,OAAO;AAAA,YACvB;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAQA,iBAAS,OAAO,MAAM,QAAQ,UAAU,QAAQ,MAAM;AACpD;AACE,gBAAI;AAEJ,gBAAI,QAAQ,CAAC;AACb,gBAAI,MAAM;AACV,gBAAI,MAAM;AAOV,gBAAI,aAAa,QAAW;AAC1B;AACE,uCAAuB,QAAQ;AAAA,cACjC;AAEA,oBAAM,KAAK;AAAA,YACb;AAEA,gBAAI,YAAY,MAAM,GAAG;AACvB;AACE,uCAAuB,OAAO,GAAG;AAAA,cACnC;AAEA,oBAAM,KAAK,OAAO;AAAA,YACpB;AAEA,gBAAI,YAAY,MAAM,GAAG;AACvB,oBAAM,OAAO;AACb,mDAAqC,QAAQ,IAAI;AAAA,YACnD;AAGA,iBAAK,YAAY,QAAQ;AACvB,kBAAI,eAAe,KAAK,QAAQ,QAAQ,KAAK,CAAC,eAAe,eAAe,QAAQ,GAAG;AACrF,sBAAM,QAAQ,IAAI,OAAO,QAAQ;AAAA,cACnC;AAAA,YACF;AAGA,gBAAI,QAAQ,KAAK,cAAc;AAC7B,kBAAI,eAAe,KAAK;AAExB,mBAAK,YAAY,cAAc;AAC7B,oBAAI,MAAM,QAAQ,MAAM,QAAW;AACjC,wBAAM,QAAQ,IAAI,aAAa,QAAQ;AAAA,gBACzC;AAAA,cACF;AAAA,YACF;AAEA,gBAAI,OAAO,KAAK;AACd,kBAAI,cAAc,OAAO,SAAS,aAAa,KAAK,eAAe,KAAK,QAAQ,YAAY;AAE5F,kBAAI,KAAK;AACP,2CAA2B,OAAO,WAAW;AAAA,cAC/C;AAEA,kBAAI,KAAK;AACP,2CAA2B,OAAO,WAAW;AAAA,cAC/C;AAAA,YACF;AAEA,mBAAO,aAAa,MAAM,KAAK,KAAK,MAAM,QAAQ,kBAAkB,SAAS,KAAK;AAAA,UACpF;AAAA,QACF;AAEA,YAAI,sBAAsB,qBAAqB;AAC/C,YAAI,2BAA2B,qBAAqB;AAEpD,iBAAS,gCAAgC,SAAS;AAChD;AACE,gBAAI,SAAS;AACX,kBAAI,QAAQ,QAAQ;AACpB,kBAAI,QAAQ,qCAAqC,QAAQ,MAAM,QAAQ,SAAS,QAAQ,MAAM,OAAO,IAAI;AACzG,uCAAyB,mBAAmB,KAAK;AAAA,YACnD,OAAO;AACL,uCAAyB,mBAAmB,IAAI;AAAA,YAClD;AAAA,UACF;AAAA,QACF;AAEA,YAAI;AAEJ;AACE,0CAAgC;AAAA,QAClC;AAUA,iBAAS,eAAe,QAAQ;AAC9B;AACE,mBAAO,OAAO,WAAW,YAAY,WAAW,QAAQ,OAAO,aAAa;AAAA,UAC9E;AAAA,QACF;AAEA,iBAAS,8BAA8B;AACrC;AACE,gBAAI,oBAAoB,SAAS;AAC/B,kBAAI,OAAO,yBAAyB,oBAAoB,QAAQ,IAAI;AAEpE,kBAAI,MAAM;AACR,uBAAO,qCAAqC,OAAO;AAAA,cACrD;AAAA,YACF;AAEA,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,iBAAS,2BAA2B,QAAQ;AAC1C;AACE,gBAAI,WAAW,QAAW;AACxB,kBAAI,WAAW,OAAO,SAAS,QAAQ,aAAa,EAAE;AACtD,kBAAI,aAAa,OAAO;AACxB,qBAAO,4BAA4B,WAAW,MAAM,aAAa;AAAA,YACnE;AAEA,mBAAO;AAAA,UACT;AAAA,QACF;AAQA,YAAI,wBAAwB,CAAC;AAE7B,iBAAS,6BAA6B,YAAY;AAChD;AACE,gBAAI,OAAO,4BAA4B;AAEvC,gBAAI,CAAC,MAAM;AACT,kBAAI,aAAa,OAAO,eAAe,WAAW,aAAa,WAAW,eAAe,WAAW;AAEpG,kBAAI,YAAY;AACd,uBAAO,gDAAgD,aAAa;AAAA,cACtE;AAAA,YACF;AAEA,mBAAO;AAAA,UACT;AAAA,QACF;AAcA,iBAAS,oBAAoB,SAAS,YAAY;AAChD;AACE,gBAAI,CAAC,QAAQ,UAAU,QAAQ,OAAO,aAAa,QAAQ,OAAO,MAAM;AACtE;AAAA,YACF;AAEA,oBAAQ,OAAO,YAAY;AAC3B,gBAAI,4BAA4B,6BAA6B,UAAU;AAEvE,gBAAI,sBAAsB,yBAAyB,GAAG;AACpD;AAAA,YACF;AAEA,kCAAsB,yBAAyB,IAAI;AAInD,gBAAI,aAAa;AAEjB,gBAAI,WAAW,QAAQ,UAAU,QAAQ,WAAW,oBAAoB,SAAS;AAE/E,2BAAa,iCAAiC,yBAAyB,QAAQ,OAAO,IAAI,IAAI;AAAA,YAChG;AAEA,4CAAgC,OAAO;AAEvC,kBAAM,6HAAkI,2BAA2B,UAAU;AAE7K,4CAAgC,IAAI;AAAA,UACtC;AAAA,QACF;AAYA,iBAAS,kBAAkB,MAAM,YAAY;AAC3C;AACE,gBAAI,OAAO,SAAS,UAAU;AAC5B;AAAA,YACF;AAEA,gBAAI,QAAQ,IAAI,GAAG;AACjB,uBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,oBAAI,QAAQ,KAAK,CAAC;AAElB,oBAAI,eAAe,KAAK,GAAG;AACzB,sCAAoB,OAAO,UAAU;AAAA,gBACvC;AAAA,cACF;AAAA,YACF,WAAW,eAAe,IAAI,GAAG;AAE/B,kBAAI,KAAK,QAAQ;AACf,qBAAK,OAAO,YAAY;AAAA,cAC1B;AAAA,YACF,WAAW,MAAM;AACf,kBAAI,aAAa,cAAc,IAAI;AAEnC,kBAAI,OAAO,eAAe,YAAY;AAGpC,oBAAI,eAAe,KAAK,SAAS;AAC/B,sBAAI,WAAW,WAAW,KAAK,IAAI;AACnC,sBAAI;AAEJ,yBAAO,EAAE,OAAO,SAAS,KAAK,GAAG,MAAM;AACrC,wBAAI,eAAe,KAAK,KAAK,GAAG;AAC9B,0CAAoB,KAAK,OAAO,UAAU;AAAA,oBAC5C;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AASA,iBAAS,kBAAkB,SAAS;AAClC;AACE,gBAAI,OAAO,QAAQ;AAEnB,gBAAI,SAAS,QAAQ,SAAS,UAAa,OAAO,SAAS,UAAU;AACnE;AAAA,YACF;AAEA,gBAAI;AAEJ,gBAAI,OAAO,SAAS,YAAY;AAC9B,0BAAY,KAAK;AAAA,YACnB,WAAW,OAAO,SAAS,aAAa,KAAK,aAAa;AAAA;AAAA,YAE1D,KAAK,aAAa,kBAAkB;AAClC,0BAAY,KAAK;AAAA,YACnB,OAAO;AACL;AAAA,YACF;AAEA,gBAAI,WAAW;AAEb,kBAAI,OAAO,yBAAyB,IAAI;AACxC,6BAAe,WAAW,QAAQ,OAAO,QAAQ,MAAM,OAAO;AAAA,YAChE,WAAW,KAAK,cAAc,UAAa,CAAC,+BAA+B;AACzE,8CAAgC;AAEhC,kBAAI,QAAQ,yBAAyB,IAAI;AAEzC,oBAAM,uGAAuG,SAAS,SAAS;AAAA,YACjI;AAEA,gBAAI,OAAO,KAAK,oBAAoB,cAAc,CAAC,KAAK,gBAAgB,sBAAsB;AAC5F,oBAAM,4HAAiI;AAAA,YACzI;AAAA,UACF;AAAA,QACF;AAOA,iBAAS,sBAAsB,UAAU;AACvC;AACE,gBAAI,OAAO,OAAO,KAAK,SAAS,KAAK;AAErC,qBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,kBAAI,MAAM,KAAK,CAAC;AAEhB,kBAAI,QAAQ,cAAc,QAAQ,OAAO;AACvC,gDAAgC,QAAQ;AAExC,sBAAM,4GAAiH,GAAG;AAE1H,gDAAgC,IAAI;AACpC;AAAA,cACF;AAAA,YACF;AAEA,gBAAI,SAAS,QAAQ,MAAM;AACzB,8CAAgC,QAAQ;AAExC,oBAAM,uDAAuD;AAE7D,8CAAgC,IAAI;AAAA,YACtC;AAAA,UACF;AAAA,QACF;AAEA,YAAI,wBAAwB,CAAC;AAC7B,iBAAS,kBAAkB,MAAM,OAAO,KAAK,kBAAkB,QAAQ,MAAM;AAC3E;AACE,gBAAI,YAAY,mBAAmB,IAAI;AAGvC,gBAAI,CAAC,WAAW;AACd,kBAAI,OAAO;AAEX,kBAAI,SAAS,UAAa,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,KAAK,IAAI,EAAE,WAAW,GAAG;AACrG,wBAAQ;AAAA,cACV;AAEA,kBAAI,aAAa,2BAA2B,MAAM;AAElD,kBAAI,YAAY;AACd,wBAAQ;AAAA,cACV,OAAO;AACL,wBAAQ,4BAA4B;AAAA,cACtC;AAEA,kBAAI;AAEJ,kBAAI,SAAS,MAAM;AACjB,6BAAa;AAAA,cACf,WAAW,QAAQ,IAAI,GAAG;AACxB,6BAAa;AAAA,cACf,WAAW,SAAS,UAAa,KAAK,aAAa,oBAAoB;AACrE,6BAAa,OAAO,yBAAyB,KAAK,IAAI,KAAK,aAAa;AACxE,uBAAO;AAAA,cACT,OAAO;AACL,6BAAa,OAAO;AAAA,cACtB;AAEA,oBAAM,2IAAqJ,YAAY,IAAI;AAAA,YAC7K;AAEA,gBAAI,UAAU,OAAO,MAAM,OAAO,KAAK,QAAQ,IAAI;AAGnD,gBAAI,WAAW,MAAM;AACnB,qBAAO;AAAA,YACT;AAOA,gBAAI,WAAW;AACb,kBAAI,WAAW,MAAM;AAErB,kBAAI,aAAa,QAAW;AAC1B,oBAAI,kBAAkB;AACpB,sBAAI,QAAQ,QAAQ,GAAG;AACrB,6BAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,wCAAkB,SAAS,CAAC,GAAG,IAAI;AAAA,oBACrC;AAEA,wBAAI,OAAO,QAAQ;AACjB,6BAAO,OAAO,QAAQ;AAAA,oBACxB;AAAA,kBACF,OAAO;AACL,0BAAM,sJAAgK;AAAA,kBACxK;AAAA,gBACF,OAAO;AACL,oCAAkB,UAAU,IAAI;AAAA,gBAClC;AAAA,cACF;AAAA,YACF;AAEA;AACE,kBAAI,eAAe,KAAK,OAAO,KAAK,GAAG;AACrC,oBAAI,gBAAgB,yBAAyB,IAAI;AACjD,oBAAI,OAAO,OAAO,KAAK,KAAK,EAAE,OAAO,SAAU,GAAG;AAChD,yBAAO,MAAM;AAAA,gBACf,CAAC;AACD,oBAAI,gBAAgB,KAAK,SAAS,IAAI,oBAAoB,KAAK,KAAK,SAAS,IAAI,WAAW;AAE5F,oBAAI,CAAC,sBAAsB,gBAAgB,aAAa,GAAG;AACzD,sBAAI,eAAe,KAAK,SAAS,IAAI,MAAM,KAAK,KAAK,SAAS,IAAI,WAAW;AAE7E,wBAAM,mOAA4P,eAAe,eAAe,cAAc,aAAa;AAE3T,wCAAsB,gBAAgB,aAAa,IAAI;AAAA,gBACzD;AAAA,cACF;AAAA,YACF;AAEA,gBAAI,SAAS,qBAAqB;AAChC,oCAAsB,OAAO;AAAA,YAC/B,OAAO;AACL,gCAAkB,OAAO;AAAA,YAC3B;AAEA,mBAAO;AAAA,UACT;AAAA,QACF;AAKA,iBAAS,wBAAwB,MAAM,OAAO,KAAK;AACjD;AACE,mBAAO,kBAAkB,MAAM,OAAO,KAAK,IAAI;AAAA,UACjD;AAAA,QACF;AACA,iBAAS,yBAAyB,MAAM,OAAO,KAAK;AAClD;AACE,mBAAO,kBAAkB,MAAM,OAAO,KAAK,KAAK;AAAA,UAClD;AAAA,QACF;AAEA,YAAI,MAAO;AAGX,YAAI,OAAQ;AAEZ,gBAAQ,WAAW;AACnB,gBAAQ,MAAM;AACd,gBAAQ,OAAO;AAAA,MACb,GAAG;AAAA,IACL;AAAA;AAAA;;;ACpzCA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;", "names": ["ReactDebugCurrentFrame"]}