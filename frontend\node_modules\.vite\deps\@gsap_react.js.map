{"version": 3, "sources": ["../../@gsap/react/src/index.js"], "sourcesContent": ["/*!\r\n * @gsap/react 2.1.2\r\n * https://gsap.com\r\n *\r\n * Copyright 2008-2025, GreenSock. All rights reserved.\r\n * Subject to the terms at https://gsap.com/standard-license or for\r\n * Club GSAP members, the agreement issued with that membership.\r\n * @author: <PERSON>, <EMAIL>\r\n*/\r\n/* eslint-disable */\r\nimport { useEffect, useLayoutEffect, useRef } from \"react\";\r\nimport gsap from \"gsap\";\r\n\r\nlet useIsomorphicLayoutEffect = typeof document !== \"undefined\" ? useLayoutEffect : useEffect,\r\n    isConfig = value => value && !Array.isArray(value) && typeof(value) === \"object\",\r\n    emptyArray = [],\r\n    defaultConfig = {},\r\n    _gsap = gsap; // accommodates situations where different versions of GSAP may be loaded, so a user can gsap.registerPlugin(useGSAP);\r\n\r\nexport const useGSAP = (callback, dependencies = emptyArray) => {\r\n  let config = defaultConfig;\r\n  if (isConfig(callback)) {\r\n    config = callback;\r\n    callback = null;\r\n    dependencies = \"dependencies\" in config ? config.dependencies : emptyArray;\r\n  } else if (isConfig(dependencies)) {\r\n    config = dependencies;\r\n    dependencies = \"dependencies\" in config ? config.dependencies : emptyArray;\r\n  }\r\n  (callback && typeof callback !== \"function\") && console.warn(\"First parameter must be a function or config object\");\r\n  const { scope, revertOnUpdate } = config,\r\n        mounted = useRef(false),\r\n        context = useRef(_gsap.context(() => { }, scope)),\r\n        contextSafe = useRef((func) => context.current.add(null, func)),\r\n        deferCleanup = dependencies && dependencies.length && !revertOnUpdate;\r\n  deferCleanup && useIsomorphicLayoutEffect(() => {\r\n    mounted.current = true;\r\n    return () => context.current.revert();\r\n  }, emptyArray);\r\n  useIsomorphicLayoutEffect(() => {\r\n    callback && context.current.add(callback, scope);\r\n    if (!deferCleanup || !mounted.current) { // React renders bottom-up, thus there could be hooks with dependencies that run BEFORE the component mounts, thus cleanup wouldn't occur since a hook with an empty dependency Array would only run once the component mounts.\r\n      return () => context.current.revert();\r\n    }\r\n  }, dependencies);\r\n  return { context: context.current, contextSafe: contextSafe.current };\r\n};\r\nuseGSAP.register = core => { _gsap = core; };\r\nuseGSAP.headless = true; // doesn't require the window to be registered.\r\n"], "mappings": ";;;;;;;;;;;AAUA,mBAAmD;AAGnD,IAAI,4BAA4B,OAAO,aAAa,cAAc,+BAAkB;AAApF,IACI,WAAW,WAAS,SAAS,CAAC,MAAM,QAAQ,KAAK,KAAK,OAAO,UAAW;AAD5E,IAEI,aAAa,CAAC;AAFlB,IAGI,gBAAgB,CAAC;AAHrB,IAII,QAAQ;AAEL,IAAM,UAAU,CAAC,UAAU,eAAe,eAAe;AAC9D,MAAI,SAAS;AACb,MAAI,SAAS,QAAQ,GAAG;AACtB,aAAS;AACT,eAAW;AACX,mBAAe,kBAAkB,SAAS,OAAO,eAAe;AAAA,EAClE,WAAW,SAAS,YAAY,GAAG;AACjC,aAAS;AACT,mBAAe,kBAAkB,SAAS,OAAO,eAAe;AAAA,EAClE;AACA,EAAC,YAAY,OAAO,aAAa,cAAe,QAAQ,KAAK,qDAAqD;AAClH,QAAM,EAAE,OAAO,eAAe,IAAI,QAC5B,cAAU,qBAAO,KAAK,GACtB,cAAU,qBAAO,MAAM,QAAQ,MAAM;AAAA,EAAE,GAAG,KAAK,CAAC,GAChD,kBAAc,qBAAO,CAAC,SAAS,QAAQ,QAAQ,IAAI,MAAM,IAAI,CAAC,GAC9D,eAAe,gBAAgB,aAAa,UAAU,CAAC;AAC7D,kBAAgB,0BAA0B,MAAM;AAC9C,YAAQ,UAAU;AAClB,WAAO,MAAM,QAAQ,QAAQ,OAAO;AAAA,EACtC,GAAG,UAAU;AACb,4BAA0B,MAAM;AAC9B,gBAAY,QAAQ,QAAQ,IAAI,UAAU,KAAK;AAC/C,QAAI,CAAC,gBAAgB,CAAC,QAAQ,SAAS;AACrC,aAAO,MAAM,QAAQ,QAAQ,OAAO;AAAA,IACtC;AAAA,EACF,GAAG,YAAY;AACf,SAAO,EAAE,SAAS,QAAQ,SAAS,aAAa,YAAY,QAAQ;AACtE;AACA,QAAQ,WAAW,UAAQ;AAAE,UAAQ;AAAM;AAC3C,QAAQ,WAAW;", "names": []}