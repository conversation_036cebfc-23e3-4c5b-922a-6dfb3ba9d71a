{"version": 3, "sources": ["../../react-barcode/lib/react-barcode.js"], "sourcesContent": ["\"use strict\";\r\n\r\nfunction _typeof(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\r\n\r\nvar _react = _interopRequireDefault(require(\"react\"));\r\n\r\nvar _jsbarcode = _interopRequireDefault(require(\"jsbarcode\"));\r\n\r\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\r\n\r\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } return _assertThisInitialized(self); }\r\n\r\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\r\n\r\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }\r\n\r\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\r\n\r\nvar getDOMNode; // Super naive semver detection but it's good enough. We support 0.12, 0.13\r\n// which both have getDOMNode on the ref. 0.14 and 15 make the DOM node the ref.\r\n\r\nvar version = _react[\"default\"].version.split(/[.-]/);\r\n\r\nif (version[0] === \"0\" && (version[1] === \"13\" || version[1] === \"12\")) {\r\n  getDOMNode = function getDOMNode(ref) {\r\n    return ref.getDOMNode();\r\n  };\r\n} else {\r\n  getDOMNode = function getDOMNode(ref) {\r\n    return ref;\r\n  };\r\n}\r\n\r\nvar Barcode =\r\n/*#__PURE__*/\r\nfunction (_React$Component) {\r\n  _inherits(Barcode, _React$Component);\r\n\r\n  function Barcode(props) {\r\n    var _this;\r\n\r\n    _classCallCheck(this, Barcode);\r\n\r\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(Barcode).call(this, props));\r\n    _this.renderElementRef = _react[\"default\"].createRef();\r\n    _this.update = _this.update.bind(_assertThisInitialized(_this));\r\n    return _this;\r\n  }\r\n\r\n  _createClass(Barcode, [{\r\n    key: \"shouldComponentUpdate\",\r\n    value: function shouldComponentUpdate(nextProps) {\r\n      var _this2 = this;\r\n\r\n      return Object.keys(Barcode.propTypes).some(function (k) {\r\n        return _this2.props[k] !== nextProps[k];\r\n      });\r\n    }\r\n  }, {\r\n    key: \"componentDidMount\",\r\n    value: function componentDidMount() {\r\n      this.update();\r\n    }\r\n  }, {\r\n    key: \"componentDidUpdate\",\r\n    value: function componentDidUpdate() {\r\n      this.update();\r\n    }\r\n  }, {\r\n    key: \"update\",\r\n    value: function update() {\r\n      var renderElement = getDOMNode(this.renderElementRef.current);\r\n\r\n      try {\r\n        new _jsbarcode[\"default\"](renderElement, this.props.value, Object.assign({\r\n          text: this.props.text || this.props.value\r\n        }, this.props));\r\n      } catch (e) {\r\n        // prevent stop the parent process\r\n        window.console.error(e);\r\n      }\r\n    }\r\n  }, {\r\n    key: \"render\",\r\n    value: function render() {\r\n      var _this$props = this.props,\r\n          id = _this$props.id,\r\n          className = _this$props.className;\r\n\r\n      if (this.props.renderer === \"svg\") {\r\n        return _react[\"default\"].createElement(\"svg\", {\r\n          ref: this.renderElementRef,\r\n          id: id,\r\n          className: className\r\n        });\r\n      } else if (this.props.renderer === \"canvas\") {\r\n        return _react[\"default\"].createElement(\"canvas\", {\r\n          ref: this.renderElementRef,\r\n          id: id,\r\n          className: className\r\n        });\r\n      } else if (this.props.renderer === \"img\") {\r\n        return _react[\"default\"].createElement(\"img\", {\r\n          ref: this.renderElementRef,\r\n          id: id,\r\n          className: className\r\n        });\r\n      }\r\n    }\r\n  }]);\r\n\r\n  return Barcode;\r\n}(_react[\"default\"].Component);\r\n\r\nBarcode.propTypes = {\r\n  value: _propTypes[\"default\"].string.isRequired,\r\n  text: _propTypes[\"default\"].string,\r\n  renderer: _propTypes[\"default\"].string,\r\n  format: _propTypes[\"default\"].string,\r\n  width: _propTypes[\"default\"].number,\r\n  height: _propTypes[\"default\"].number,\r\n  displayValue: _propTypes[\"default\"].bool,\r\n  fontOptions: _propTypes[\"default\"].string,\r\n  font: _propTypes[\"default\"].string,\r\n  textAlign: _propTypes[\"default\"].string,\r\n  textPosition: _propTypes[\"default\"].string,\r\n  textMargin: _propTypes[\"default\"].number,\r\n  fontSize: _propTypes[\"default\"].number,\r\n  background: _propTypes[\"default\"].string,\r\n  lineColor: _propTypes[\"default\"].string,\r\n  margin: _propTypes[\"default\"].number,\r\n  marginTop: _propTypes[\"default\"].number,\r\n  marginBottom: _propTypes[\"default\"].number,\r\n  marginLeft: _propTypes[\"default\"].number,\r\n  marginRight: _propTypes[\"default\"].number,\r\n  id: _propTypes[\"default\"].string,\r\n  className: _propTypes[\"default\"].string,\r\n  ean128: _propTypes[\"default\"].bool\r\n};\r\nBarcode.defaultProps = {\r\n  format: \"CODE128\",\r\n  renderer: \"svg\",\r\n  width: 2,\r\n  height: 100,\r\n  displayValue: true,\r\n  fontOptions: \"\",\r\n  font: \"monospace\",\r\n  textAlign: \"center\",\r\n  textPosition: \"bottom\",\r\n  textMargin: 2,\r\n  fontSize: 20,\r\n  background: \"#ffffff\",\r\n  lineColor: \"#000000\",\r\n  margin: 10,\r\n  className: \"\",\r\n  ean128: false\r\n};\r\nmodule.exports = Barcode;"], "mappings": ";;;;;;;;;;;;;;;AAAA;AAAA;AAEA,aAAS,QAAQ,KAAK;AAAE,UAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AAAE,kBAAU,SAASA,SAAQC,MAAK;AAAE,iBAAO,OAAOA;AAAA,QAAK;AAAA,MAAG,OAAO;AAAE,kBAAU,SAASD,SAAQC,MAAK;AAAE,iBAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,QAAK;AAAA,MAAG;AAAE,aAAO,QAAQ,GAAG;AAAA,IAAG;AAE9V,QAAI,SAAS,uBAAuB,eAAgB;AAEpD,QAAI,aAAa,uBAAuB,mBAAoB;AAE5D,QAAI,aAAa,uBAAuB,oBAAqB;AAE7D,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,WAAW,IAAI;AAAA,IAAG;AAEhG,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,kBAAkB,QAAQ,OAAO;AAAE,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,YAAI,aAAa,MAAM,CAAC;AAAG,mBAAW,aAAa,WAAW,cAAc;AAAO,mBAAW,eAAe;AAAM,YAAI,WAAW,WAAY,YAAW,WAAW;AAAM,eAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,MAAG;AAAA,IAAE;AAE5T,aAAS,aAAa,aAAa,YAAY,aAAa;AAAE,UAAI,WAAY,mBAAkB,YAAY,WAAW,UAAU;AAAG,UAAI,YAAa,mBAAkB,aAAa,WAAW;AAAG,aAAO;AAAA,IAAa;AAEtN,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,SAAS,QAAQ,IAAI,MAAM,YAAY,OAAO,SAAS,aAAa;AAAE,eAAO;AAAA,MAAM;AAAE,aAAO,uBAAuB,IAAI;AAAA,IAAG;AAEhL,aAAS,gBAAgB,GAAG;AAAE,wBAAkB,OAAO,iBAAiB,OAAO,iBAAiB,SAASC,iBAAgBC,IAAG;AAAE,eAAOA,GAAE,aAAa,OAAO,eAAeA,EAAC;AAAA,MAAG;AAAG,aAAO,gBAAgB,CAAC;AAAA,IAAG;AAE5M,aAAS,uBAAuB,MAAM;AAAE,UAAI,SAAS,QAAQ;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO;AAAA,IAAM;AAErK,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,oDAAoD;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,iBAAgB,UAAU,UAAU;AAAA,IAAG;AAEhY,aAAS,gBAAgB,GAAG,GAAG;AAAE,wBAAkB,OAAO,kBAAkB,SAASC,iBAAgBD,IAAGE,IAAG;AAAE,QAAAF,GAAE,YAAYE;AAAG,eAAOF;AAAA,MAAG;AAAG,aAAO,gBAAgB,GAAG,CAAC;AAAA,IAAG;AAEzK,QAAI;AAGJ,QAAI,UAAU,OAAO,SAAS,EAAE,QAAQ,MAAM,MAAM;AAEpD,QAAI,QAAQ,CAAC,MAAM,QAAQ,QAAQ,CAAC,MAAM,QAAQ,QAAQ,CAAC,MAAM,OAAO;AACtE,mBAAa,SAASG,YAAW,KAAK;AACpC,eAAO,IAAI,WAAW;AAAA,MACxB;AAAA,IACF,OAAO;AACL,mBAAa,SAASA,YAAW,KAAK;AACpC,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,UAEJ,SAAU,kBAAkB;AAC1B,gBAAUC,UAAS,gBAAgB;AAEnC,eAASA,SAAQ,OAAO;AACtB,YAAI;AAEJ,wBAAgB,MAAMA,QAAO;AAE7B,gBAAQ,2BAA2B,MAAM,gBAAgBA,QAAO,EAAE,KAAK,MAAM,KAAK,CAAC;AACnF,cAAM,mBAAmB,OAAO,SAAS,EAAE,UAAU;AACrD,cAAM,SAAS,MAAM,OAAO,KAAK,uBAAuB,KAAK,CAAC;AAC9D,eAAO;AAAA,MACT;AAEA,mBAAaA,UAAS,CAAC;AAAA,QACrB,KAAK;AAAA,QACL,OAAO,SAAS,sBAAsB,WAAW;AAC/C,cAAI,SAAS;AAEb,iBAAO,OAAO,KAAKA,SAAQ,SAAS,EAAE,KAAK,SAAU,GAAG;AACtD,mBAAO,OAAO,MAAM,CAAC,MAAM,UAAU,CAAC;AAAA,UACxC,CAAC;AAAA,QACH;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,oBAAoB;AAClC,eAAK,OAAO;AAAA,QACd;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,qBAAqB;AACnC,eAAK,OAAO;AAAA,QACd;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACvB,cAAI,gBAAgB,WAAW,KAAK,iBAAiB,OAAO;AAE5D,cAAI;AACF,gBAAI,WAAW,SAAS,EAAE,eAAe,KAAK,MAAM,OAAO,OAAO,OAAO;AAAA,cACvE,MAAM,KAAK,MAAM,QAAQ,KAAK,MAAM;AAAA,YACtC,GAAG,KAAK,KAAK,CAAC;AAAA,UAChB,SAAS,GAAG;AAEV,mBAAO,QAAQ,MAAM,CAAC;AAAA,UACxB;AAAA,QACF;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACvB,cAAI,cAAc,KAAK,OACnB,KAAK,YAAY,IACjB,YAAY,YAAY;AAE5B,cAAI,KAAK,MAAM,aAAa,OAAO;AACjC,mBAAO,OAAO,SAAS,EAAE,cAAc,OAAO;AAAA,cAC5C,KAAK,KAAK;AAAA,cACV;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH,WAAW,KAAK,MAAM,aAAa,UAAU;AAC3C,mBAAO,OAAO,SAAS,EAAE,cAAc,UAAU;AAAA,cAC/C,KAAK,KAAK;AAAA,cACV;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH,WAAW,KAAK,MAAM,aAAa,OAAO;AACxC,mBAAO,OAAO,SAAS,EAAE,cAAc,OAAO;AAAA,cAC5C,KAAK,KAAK;AAAA,cACV;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC,CAAC;AAEF,aAAOA;AAAA,IACT,EAAE,OAAO,SAAS,EAAE,SAAS;AAE7B,YAAQ,YAAY;AAAA,MAClB,OAAO,WAAW,SAAS,EAAE,OAAO;AAAA,MACpC,MAAM,WAAW,SAAS,EAAE;AAAA,MAC5B,UAAU,WAAW,SAAS,EAAE;AAAA,MAChC,QAAQ,WAAW,SAAS,EAAE;AAAA,MAC9B,OAAO,WAAW,SAAS,EAAE;AAAA,MAC7B,QAAQ,WAAW,SAAS,EAAE;AAAA,MAC9B,cAAc,WAAW,SAAS,EAAE;AAAA,MACpC,aAAa,WAAW,SAAS,EAAE;AAAA,MACnC,MAAM,WAAW,SAAS,EAAE;AAAA,MAC5B,WAAW,WAAW,SAAS,EAAE;AAAA,MACjC,cAAc,WAAW,SAAS,EAAE;AAAA,MACpC,YAAY,WAAW,SAAS,EAAE;AAAA,MAClC,UAAU,WAAW,SAAS,EAAE;AAAA,MAChC,YAAY,WAAW,SAAS,EAAE;AAAA,MAClC,WAAW,WAAW,SAAS,EAAE;AAAA,MACjC,QAAQ,WAAW,SAAS,EAAE;AAAA,MAC9B,WAAW,WAAW,SAAS,EAAE;AAAA,MACjC,cAAc,WAAW,SAAS,EAAE;AAAA,MACpC,YAAY,WAAW,SAAS,EAAE;AAAA,MAClC,aAAa,WAAW,SAAS,EAAE;AAAA,MACnC,IAAI,WAAW,SAAS,EAAE;AAAA,MAC1B,WAAW,WAAW,SAAS,EAAE;AAAA,MACjC,QAAQ,WAAW,SAAS,EAAE;AAAA,IAChC;AACA,YAAQ,eAAe;AAAA,MACrB,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,aAAa;AAAA,MACb,MAAM;AAAA,MACN,WAAW;AAAA,MACX,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,QAAQ;AAAA,IACV;AACA,WAAO,UAAU;AAAA;AAAA;", "names": ["_typeof", "obj", "_getPrototypeOf", "o", "_setPrototypeOf", "p", "getDOMNode", "Barcode"]}