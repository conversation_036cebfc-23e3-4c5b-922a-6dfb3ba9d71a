{"version": 3, "sources": ["../../jsbarcode/bin/barcodes/Barcode.js", "../../jsbarcode/bin/barcodes/CODE39/index.js", "../../jsbarcode/bin/barcodes/CODE128/constants.js", "../../jsbarcode/bin/barcodes/CODE128/CODE128.js", "../../jsbarcode/bin/barcodes/CODE128/auto.js", "../../jsbarcode/bin/barcodes/CODE128/CODE128_AUTO.js", "../../jsbarcode/bin/barcodes/CODE128/CODE128A.js", "../../jsbarcode/bin/barcodes/CODE128/CODE128B.js", "../../jsbarcode/bin/barcodes/CODE128/CODE128C.js", "../../jsbarcode/bin/barcodes/CODE128/index.js", "../../jsbarcode/bin/barcodes/EAN_UPC/constants.js", "../../jsbarcode/bin/barcodes/EAN_UPC/encoder.js", "../../jsbarcode/bin/barcodes/EAN_UPC/EAN.js", "../../jsbarcode/bin/barcodes/EAN_UPC/EAN13.js", "../../jsbarcode/bin/barcodes/EAN_UPC/EAN8.js", "../../jsbarcode/bin/barcodes/EAN_UPC/EAN5.js", "../../jsbarcode/bin/barcodes/EAN_UPC/EAN2.js", "../../jsbarcode/bin/barcodes/EAN_UPC/UPC.js", "../../jsbarcode/bin/barcodes/EAN_UPC/UPCE.js", "../../jsbarcode/bin/barcodes/EAN_UPC/index.js", "../../jsbarcode/bin/barcodes/ITF/constants.js", "../../jsbarcode/bin/barcodes/ITF/ITF.js", "../../jsbarcode/bin/barcodes/ITF/ITF14.js", "../../jsbarcode/bin/barcodes/ITF/index.js", "../../jsbarcode/bin/barcodes/MSI/MSI.js", "../../jsbarcode/bin/barcodes/MSI/checksums.js", "../../jsbarcode/bin/barcodes/MSI/MSI10.js", "../../jsbarcode/bin/barcodes/MSI/MSI11.js", "../../jsbarcode/bin/barcodes/MSI/MSI1010.js", "../../jsbarcode/bin/barcodes/MSI/MSI1110.js", "../../jsbarcode/bin/barcodes/MSI/index.js", "../../jsbarcode/bin/barcodes/pharmacode/index.js", "../../jsbarcode/bin/barcodes/codabar/index.js", "../../jsbarcode/bin/barcodes/GenericBarcode/index.js", "../../jsbarcode/bin/barcodes/index.js", "../../jsbarcode/bin/help/merge.js", "../../jsbarcode/bin/help/linearizeEncodings.js", "../../jsbarcode/bin/help/fixOptions.js", "../../jsbarcode/bin/help/optionsFromStrings.js", "../../jsbarcode/bin/options/defaults.js", "../../jsbarcode/bin/help/getOptionsFromElement.js", "../../jsbarcode/bin/renderers/shared.js", "../../jsbarcode/bin/renderers/canvas.js", "../../jsbarcode/bin/renderers/svg.js", "../../jsbarcode/bin/renderers/object.js", "../../jsbarcode/bin/renderers/index.js", "../../jsbarcode/bin/exceptions/exceptions.js", "../../jsbarcode/bin/help/getRenderProperties.js", "../../jsbarcode/bin/exceptions/ErrorHandler.js", "../../jsbarcode/bin/JsBarcode.js"], "sourcesContent": ["\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nvar Barcode = function Barcode(data, options) {\r\n\t_classCallCheck(this, Barcode);\r\n\r\n\tthis.data = data;\r\n\tthis.text = options.text || data;\r\n\tthis.options = options;\r\n};\r\n\r\nexports.default = Barcode;", "\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\nexports.CODE39 = undefined;\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nvar _Barcode2 = require(\"../Barcode.js\");\r\n\r\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation:\r\n// https://en.wikipedia.org/wiki/Code_39#Encoding\r\n\r\nvar CODE39 = function (_Barcode) {\r\n\t_inherits(CODE39, _Barcode);\r\n\r\n\tfunction CODE39(data, options) {\r\n\t\t_classCallCheck(this, CODE39);\r\n\r\n\t\tdata = data.toUpperCase();\r\n\r\n\t\t// Calculate mod43 checksum if enabled\r\n\t\tif (options.mod43) {\r\n\t\t\tdata += getCharacter(mod43checksum(data));\r\n\t\t}\r\n\r\n\t\treturn _possibleConstructorReturn(this, (CODE39.__proto__ || Object.getPrototypeOf(CODE39)).call(this, data, options));\r\n\t}\r\n\r\n\t_createClass(CODE39, [{\r\n\t\tkey: \"encode\",\r\n\t\tvalue: function encode() {\r\n\t\t\t// First character is always a *\r\n\t\t\tvar result = getEncoding(\"*\");\r\n\r\n\t\t\t// Take every character and add the binary representation to the result\r\n\t\t\tfor (var i = 0; i < this.data.length; i++) {\r\n\t\t\t\tresult += getEncoding(this.data[i]) + \"0\";\r\n\t\t\t}\r\n\r\n\t\t\t// Last character is always a *\r\n\t\t\tresult += getEncoding(\"*\");\r\n\r\n\t\t\treturn {\r\n\t\t\t\tdata: result,\r\n\t\t\t\ttext: this.text\r\n\t\t\t};\r\n\t\t}\r\n\t}, {\r\n\t\tkey: \"valid\",\r\n\t\tvalue: function valid() {\r\n\t\t\treturn this.data.search(/^[0-9A-Z\\-\\.\\ \\$\\/\\+\\%]+$/) !== -1;\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn CODE39;\r\n}(_Barcode3.default);\r\n\r\n// All characters. The position in the array is the (checksum) value\r\n\r\n\r\nvar characters = [\"0\", \"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\", \"H\", \"I\", \"J\", \"K\", \"L\", \"M\", \"N\", \"O\", \"P\", \"Q\", \"R\", \"S\", \"T\", \"U\", \"V\", \"W\", \"X\", \"Y\", \"Z\", \"-\", \".\", \" \", \"$\", \"/\", \"+\", \"%\", \"*\"];\r\n\r\n// The decimal representation of the characters, is converted to the\r\n// corresponding binary with the getEncoding function\r\nvar encodings = [20957, 29783, 23639, 30485, 20951, 29813, 23669, 20855, 29789, 23645, 29975, 23831, 30533, 22295, 30149, 24005, 21623, 29981, 23837, 22301, 30023, 23879, 30545, 22343, 30161, 24017, 21959, 30065, 23921, 22385, 29015, 18263, 29141, 17879, 29045, 18293, 17783, 29021, 18269, 17477, 17489, 17681, 20753, 35770];\r\n\r\n// Get the binary representation of a character by converting the encodings\r\n// from decimal to binary\r\nfunction getEncoding(character) {\r\n\treturn getBinary(characterValue(character));\r\n}\r\n\r\nfunction getBinary(characterValue) {\r\n\treturn encodings[characterValue].toString(2);\r\n}\r\n\r\nfunction getCharacter(characterValue) {\r\n\treturn characters[characterValue];\r\n}\r\n\r\nfunction characterValue(character) {\r\n\treturn characters.indexOf(character);\r\n}\r\n\r\nfunction mod43checksum(data) {\r\n\tvar checksum = 0;\r\n\tfor (var i = 0; i < data.length; i++) {\r\n\t\tchecksum += characterValue(data[i]);\r\n\t}\r\n\r\n\tchecksum = checksum % 43;\r\n\treturn checksum;\r\n}\r\n\r\nexports.CODE39 = CODE39;", "\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _SET_BY_CODE;\r\n\r\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\r\n\r\n// constants for internal usage\r\nvar SET_A = exports.SET_A = 0;\r\nvar SET_B = exports.SET_B = 1;\r\nvar SET_C = exports.SET_C = 2;\r\n\r\n// Special characters\r\nvar SHIFT = exports.SHIFT = 98;\r\nvar START_A = exports.START_A = 103;\r\nvar START_B = exports.START_B = 104;\r\nvar START_C = exports.START_C = 105;\r\nvar MODULO = exports.MODULO = 103;\r\nvar STOP = exports.STOP = 106;\r\nvar FNC1 = exports.FNC1 = 207;\r\n\r\n// Get set by start code\r\nvar SET_BY_CODE = exports.SET_BY_CODE = (_SET_BY_CODE = {}, _defineProperty(_SET_BY_CODE, START_A, SET_A), _defineProperty(_SET_BY_CODE, START_B, SET_B), _defineProperty(_SET_BY_CODE, START_C, SET_C), _SET_BY_CODE);\r\n\r\n// Get next set by code\r\nvar SWAP = exports.SWAP = {\r\n\t101: SET_A,\r\n\t100: SET_B,\r\n\t99: SET_C\r\n};\r\n\r\nvar A_START_CHAR = exports.A_START_CHAR = String.fromCharCode(208); // START_A + 105\r\nvar B_START_CHAR = exports.B_START_CHAR = String.fromCharCode(209); // START_B + 105\r\nvar C_START_CHAR = exports.C_START_CHAR = String.fromCharCode(210); // START_C + 105\r\n\r\n// 128A (Code Set A)\r\n// ASCII characters 00 to 95 (0–9, A–Z and control codes), special characters, and FNC 1–4\r\nvar A_CHARS = exports.A_CHARS = \"[\\x00-\\x5F\\xC8-\\xCF]\";\r\n\r\n// 128B (Code Set B)\r\n// ASCII characters 32 to 127 (0–9, A–Z, a–z), special characters, and FNC 1–4\r\nvar B_CHARS = exports.B_CHARS = \"[\\x20-\\x7F\\xC8-\\xCF]\";\r\n\r\n// 128C (Code Set C)\r\n// 00–99 (encodes two digits with a single code point) and FNC1\r\nvar C_CHARS = exports.C_CHARS = \"(\\xCF*[0-9]{2}\\xCF*)\";\r\n\r\n// CODE128 includes 107 symbols:\r\n// 103 data symbols, 3 start symbols (A, B and C), and 1 stop symbol (the last one)\r\n// Each symbol consist of three black bars (1) and three white spaces (0).\r\nvar BARS = exports.BARS = [11011001100, 11001101100, 11001100110, 10010011000, 10010001100, 10001001100, 10011001000, 10011000100, 10001100100, 11001001000, 11001000100, 11000100100, 10110011100, 10011011100, 10011001110, 10111001100, 10011101100, 10011100110, 11001110010, 11001011100, 11001001110, 11011100100, 11001110100, 11101101110, 11101001100, 11100101100, 11100100110, 11101100100, 11100110100, 11100110010, 11011011000, 11011000110, 11000110110, 10100011000, 10001011000, 10001000110, 10110001000, 10001101000, 10001100010, 11010001000, 11000101000, 11000100010, 10110111000, 10110001110, 10001101110, 10111011000, 10111000110, 10001110110, 11101110110, 11010001110, 11000101110, 11011101000, 11011100010, 11011101110, 11101011000, 11101000110, 11100010110, 11101101000, 11101100010, 11100011010, 11101111010, 11001000010, 11110001010, 10100110000, 10100001100, 10010110000, 10010000110, 10000101100, 10000100110, 10110010000, 10110000100, 10011010000, 10011000010, 10000110100, 10000110010, 11000010010, 11001010000, 11110111010, 11000010100, 10001111010, 10100111100, 10010111100, 10010011110, 10111100100, 10011110100, 10011110010, 11110100100, 11110010100, 11110010010, 11011011110, 11011110110, 11110110110, 10101111000, 10100011110, 10001011110, 10111101000, 10111100010, 11110101000, 11110100010, 10111011110, 10111101110, 11101011110, 11110101110, 11010000100, 11010010000, 11010011100, 1100011101011];", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nvar _Barcode2 = require('../Barcode.js');\r\n\r\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\r\n\r\nvar _constants = require('./constants');\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\r\n\r\n// This is the master class,\r\n// it does require the start code to be included in the string\r\nvar CODE128 = function (_Barcode) {\r\n\t_inherits(CODE128, _Barcode);\r\n\r\n\tfunction CODE128(data, options) {\r\n\t\t_classCallCheck(this, CODE128);\r\n\r\n\t\t// Get array of ascii codes from data\r\n\t\tvar _this = _possibleConstructorReturn(this, (CODE128.__proto__ || Object.getPrototypeOf(CODE128)).call(this, data.substring(1), options));\r\n\r\n\t\t_this.bytes = data.split('').map(function (char) {\r\n\t\t\treturn char.charCodeAt(0);\r\n\t\t});\r\n\t\treturn _this;\r\n\t}\r\n\r\n\t_createClass(CODE128, [{\r\n\t\tkey: 'valid',\r\n\t\tvalue: function valid() {\r\n\t\t\t// ASCII value ranges 0-127, 200-211\r\n\t\t\treturn (/^[\\x00-\\x7F\\xC8-\\xD3]+$/.test(this.data)\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\t// The public encoding function\r\n\r\n\t}, {\r\n\t\tkey: 'encode',\r\n\t\tvalue: function encode() {\r\n\t\t\tvar bytes = this.bytes;\r\n\t\t\t// Remove the start code from the bytes and set its index\r\n\t\t\tvar startIndex = bytes.shift() - 105;\r\n\t\t\t// Get start set by index\r\n\t\t\tvar startSet = _constants.SET_BY_CODE[startIndex];\r\n\r\n\t\t\tif (startSet === undefined) {\r\n\t\t\t\tthrow new RangeError('The encoding does not start with a start character.');\r\n\t\t\t}\r\n\r\n\t\t\tif (this.shouldEncodeAsEan128() === true) {\r\n\t\t\t\tbytes.unshift(_constants.FNC1);\r\n\t\t\t}\r\n\r\n\t\t\t// Start encode with the right type\r\n\t\t\tvar encodingResult = CODE128.next(bytes, 1, startSet);\r\n\r\n\t\t\treturn {\r\n\t\t\t\ttext: this.text === this.data ? this.text.replace(/[^\\x20-\\x7E]/g, '') : this.text,\r\n\t\t\t\tdata:\r\n\t\t\t\t// Add the start bits\r\n\t\t\t\tCODE128.getBar(startIndex) +\r\n\t\t\t\t// Add the encoded bits\r\n\t\t\t\tencodingResult.result +\r\n\t\t\t\t// Add the checksum\r\n\t\t\t\tCODE128.getBar((encodingResult.checksum + startIndex) % _constants.MODULO) +\r\n\t\t\t\t// Add the end bits\r\n\t\t\t\tCODE128.getBar(_constants.STOP)\r\n\t\t\t};\r\n\t\t}\r\n\r\n\t\t// GS1-128/EAN-128\r\n\r\n\t}, {\r\n\t\tkey: 'shouldEncodeAsEan128',\r\n\t\tvalue: function shouldEncodeAsEan128() {\r\n\t\t\tvar isEAN128 = this.options.ean128 || false;\r\n\t\t\tif (typeof isEAN128 === 'string') {\r\n\t\t\t\tisEAN128 = isEAN128.toLowerCase() === 'true';\r\n\t\t\t}\r\n\t\t\treturn isEAN128;\r\n\t\t}\r\n\r\n\t\t// Get a bar symbol by index\r\n\r\n\t}], [{\r\n\t\tkey: 'getBar',\r\n\t\tvalue: function getBar(index) {\r\n\t\t\treturn _constants.BARS[index] ? _constants.BARS[index].toString() : '';\r\n\t\t}\r\n\r\n\t\t// Correct an index by a set and shift it from the bytes array\r\n\r\n\t}, {\r\n\t\tkey: 'correctIndex',\r\n\t\tvalue: function correctIndex(bytes, set) {\r\n\t\t\tif (set === _constants.SET_A) {\r\n\t\t\t\tvar charCode = bytes.shift();\r\n\t\t\t\treturn charCode < 32 ? charCode + 64 : charCode - 32;\r\n\t\t\t} else if (set === _constants.SET_B) {\r\n\t\t\t\treturn bytes.shift() - 32;\r\n\t\t\t} else {\r\n\t\t\t\treturn (bytes.shift() - 48) * 10 + bytes.shift() - 48;\r\n\t\t\t}\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'next',\r\n\t\tvalue: function next(bytes, pos, set) {\r\n\t\t\tif (!bytes.length) {\r\n\t\t\t\treturn { result: '', checksum: 0 };\r\n\t\t\t}\r\n\r\n\t\t\tvar nextCode = void 0,\r\n\t\t\t    index = void 0;\r\n\r\n\t\t\t// Special characters\r\n\t\t\tif (bytes[0] >= 200) {\r\n\t\t\t\tindex = bytes.shift() - 105;\r\n\t\t\t\tvar nextSet = _constants.SWAP[index];\r\n\r\n\t\t\t\t// Swap to other set\r\n\t\t\t\tif (nextSet !== undefined) {\r\n\t\t\t\t\tnextCode = CODE128.next(bytes, pos + 1, nextSet);\r\n\t\t\t\t}\r\n\t\t\t\t// Continue on current set but encode a special character\r\n\t\t\t\telse {\r\n\t\t\t\t\t\t// Shift\r\n\t\t\t\t\t\tif ((set === _constants.SET_A || set === _constants.SET_B) && index === _constants.SHIFT) {\r\n\t\t\t\t\t\t\t// Convert the next character so that is encoded correctly\r\n\t\t\t\t\t\t\tbytes[0] = set === _constants.SET_A ? bytes[0] > 95 ? bytes[0] - 96 : bytes[0] : bytes[0] < 32 ? bytes[0] + 96 : bytes[0];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tnextCode = CODE128.next(bytes, pos + 1, set);\r\n\t\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// Continue encoding\r\n\t\t\telse {\r\n\t\t\t\t\tindex = CODE128.correctIndex(bytes, set);\r\n\t\t\t\t\tnextCode = CODE128.next(bytes, pos + 1, set);\r\n\t\t\t\t}\r\n\r\n\t\t\t// Get the correct binary encoding and calculate the weight\r\n\t\t\tvar enc = CODE128.getBar(index);\r\n\t\t\tvar weight = index * pos;\r\n\r\n\t\t\treturn {\r\n\t\t\t\tresult: enc + nextCode.result,\r\n\t\t\t\tchecksum: weight + nextCode.checksum\r\n\t\t\t};\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn CODE128;\r\n}(_Barcode3.default);\r\n\r\nexports.default = CODE128;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _constants = require('./constants');\r\n\r\n// Match Set functions\r\nvar matchSetALength = function matchSetALength(string) {\r\n\treturn string.match(new RegExp('^' + _constants.A_CHARS + '*'))[0].length;\r\n};\r\nvar matchSetBLength = function matchSetBLength(string) {\r\n\treturn string.match(new RegExp('^' + _constants.B_CHARS + '*'))[0].length;\r\n};\r\nvar matchSetC = function matchSetC(string) {\r\n\treturn string.match(new RegExp('^' + _constants.C_CHARS + '*'))[0];\r\n};\r\n\r\n// CODE128A or CODE128B\r\nfunction autoSelectFromAB(string, isA) {\r\n\tvar ranges = isA ? _constants.A_CHARS : _constants.B_CHARS;\r\n\tvar untilC = string.match(new RegExp('^(' + ranges + '+?)(([0-9]{2}){2,})([^0-9]|$)'));\r\n\r\n\tif (untilC) {\r\n\t\treturn untilC[1] + String.fromCharCode(204) + autoSelectFromC(string.substring(untilC[1].length));\r\n\t}\r\n\r\n\tvar chars = string.match(new RegExp('^' + ranges + '+'))[0];\r\n\r\n\tif (chars.length === string.length) {\r\n\t\treturn string;\r\n\t}\r\n\r\n\treturn chars + String.fromCharCode(isA ? 205 : 206) + autoSelectFromAB(string.substring(chars.length), !isA);\r\n}\r\n\r\n// CODE128C\r\nfunction autoSelectFromC(string) {\r\n\tvar cMatch = matchSetC(string);\r\n\tvar length = cMatch.length;\r\n\r\n\tif (length === string.length) {\r\n\t\treturn string;\r\n\t}\r\n\r\n\tstring = string.substring(length);\r\n\r\n\t// Select A/B depending on the longest match\r\n\tvar isA = matchSetALength(string) >= matchSetBLength(string);\r\n\treturn cMatch + String.fromCharCode(isA ? 206 : 205) + autoSelectFromAB(string, isA);\r\n}\r\n\r\n// Detect Code Set (A, B or C) and format the string\r\n\r\nexports.default = function (string) {\r\n\tvar newString = void 0;\r\n\tvar cLength = matchSetC(string).length;\r\n\r\n\t// Select 128C if the string start with enough digits\r\n\tif (cLength >= 2) {\r\n\t\tnewString = _constants.C_START_CHAR + autoSelectFromC(string);\r\n\t} else {\r\n\t\t// Select A/B depending on the longest match\r\n\t\tvar isA = matchSetALength(string) > matchSetBLength(string);\r\n\t\tnewString = (isA ? _constants.A_START_CHAR : _constants.B_START_CHAR) + autoSelectFromAB(string, isA);\r\n\t}\r\n\r\n\treturn newString.replace(/[\\xCD\\xCE]([^])[\\xCD\\xCE]/, // Any sequence between 205 and 206 characters\r\n\tfunction (match, char) {\r\n\t\treturn String.fromCharCode(203) + char;\r\n\t});\r\n};", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _CODE2 = require('./CODE128');\r\n\r\nvar _CODE3 = _interopRequireDefault(_CODE2);\r\n\r\nvar _auto = require('./auto');\r\n\r\nvar _auto2 = _interopRequireDefault(_auto);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\r\n\r\nvar CODE128AUTO = function (_CODE) {\r\n\t_inherits(CODE128AUTO, _CODE);\r\n\r\n\tfunction CODE128AUTO(data, options) {\r\n\t\t_classCallCheck(this, CODE128AUTO);\r\n\r\n\t\t// ASCII value ranges 0-127, 200-211\r\n\t\tif (/^[\\x00-\\x7F\\xC8-\\xD3]+$/.test(data)) {\r\n\t\t\tvar _this = _possibleConstructorReturn(this, (CODE128AUTO.__proto__ || Object.getPrototypeOf(CODE128AUTO)).call(this, (0, _auto2.default)(data), options));\r\n\t\t} else {\r\n\t\t\tvar _this = _possibleConstructorReturn(this, (CODE128AUTO.__proto__ || Object.getPrototypeOf(CODE128AUTO)).call(this, data, options));\r\n\t\t}\r\n\t\treturn _possibleConstructorReturn(_this);\r\n\t}\r\n\r\n\treturn CODE128AUTO;\r\n}(_CODE3.default);\r\n\r\nexports.default = CODE128AUTO;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nvar _CODE2 = require('./CODE128.js');\r\n\r\nvar _CODE3 = _interopRequireDefault(_CODE2);\r\n\r\nvar _constants = require('./constants');\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\r\n\r\nvar CODE128A = function (_CODE) {\r\n\t_inherits(CODE128A, _CODE);\r\n\r\n\tfunction CODE128A(string, options) {\r\n\t\t_classCallCheck(this, CODE128A);\r\n\r\n\t\treturn _possibleConstructorReturn(this, (CODE128A.__proto__ || Object.getPrototypeOf(CODE128A)).call(this, _constants.A_START_CHAR + string, options));\r\n\t}\r\n\r\n\t_createClass(CODE128A, [{\r\n\t\tkey: 'valid',\r\n\t\tvalue: function valid() {\r\n\t\t\treturn new RegExp('^' + _constants.A_CHARS + '+$').test(this.data);\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn CODE128A;\r\n}(_CODE3.default);\r\n\r\nexports.default = CODE128A;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nvar _CODE2 = require('./CODE128.js');\r\n\r\nvar _CODE3 = _interopRequireDefault(_CODE2);\r\n\r\nvar _constants = require('./constants');\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\r\n\r\nvar CODE128B = function (_CODE) {\r\n\t_inherits(CODE128B, _CODE);\r\n\r\n\tfunction CODE128B(string, options) {\r\n\t\t_classCallCheck(this, CODE128B);\r\n\r\n\t\treturn _possibleConstructorReturn(this, (CODE128B.__proto__ || Object.getPrototypeOf(CODE128B)).call(this, _constants.B_START_CHAR + string, options));\r\n\t}\r\n\r\n\t_createClass(CODE128B, [{\r\n\t\tkey: 'valid',\r\n\t\tvalue: function valid() {\r\n\t\t\treturn new RegExp('^' + _constants.B_CHARS + '+$').test(this.data);\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn CODE128B;\r\n}(_CODE3.default);\r\n\r\nexports.default = CODE128B;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nvar _CODE2 = require('./CODE128.js');\r\n\r\nvar _CODE3 = _interopRequireDefault(_CODE2);\r\n\r\nvar _constants = require('./constants');\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\r\n\r\nvar CODE128C = function (_CODE) {\r\n\t_inherits(CODE128C, _CODE);\r\n\r\n\tfunction CODE128C(string, options) {\r\n\t\t_classCallCheck(this, CODE128C);\r\n\r\n\t\treturn _possibleConstructorReturn(this, (CODE128C.__proto__ || Object.getPrototypeOf(CODE128C)).call(this, _constants.C_START_CHAR + string, options));\r\n\t}\r\n\r\n\t_createClass(CODE128C, [{\r\n\t\tkey: 'valid',\r\n\t\tvalue: function valid() {\r\n\t\t\treturn new RegExp('^' + _constants.C_CHARS + '+$').test(this.data);\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn CODE128C;\r\n}(_CODE3.default);\r\n\r\nexports.default = CODE128C;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n  value: true\r\n});\r\nexports.CODE128C = exports.CODE128B = exports.CODE128A = exports.CODE128 = undefined;\r\n\r\nvar _CODE128_AUTO = require('./CODE128_AUTO.js');\r\n\r\nvar _CODE128_AUTO2 = _interopRequireDefault(_CODE128_AUTO);\r\n\r\nvar _CODE128A = require('./CODE128A.js');\r\n\r\nvar _CODE128A2 = _interopRequireDefault(_CODE128A);\r\n\r\nvar _CODE128B = require('./CODE128B.js');\r\n\r\nvar _CODE128B2 = _interopRequireDefault(_CODE128B);\r\n\r\nvar _CODE128C = require('./CODE128C.js');\r\n\r\nvar _CODE128C2 = _interopRequireDefault(_CODE128C);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nexports.CODE128 = _CODE128_AUTO2.default;\r\nexports.CODE128A = _CODE128A2.default;\r\nexports.CODE128B = _CODE128B2.default;\r\nexports.CODE128C = _CODE128C2.default;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n// Standard start end and middle bits\r\nvar SIDE_BIN = exports.SIDE_BIN = '101';\r\nvar MIDDLE_BIN = exports.MIDDLE_BIN = '01010';\r\n\r\nvar BINARIES = exports.BINARIES = {\r\n\t'L': [// The L (left) type of encoding\r\n\t'0001101', '0011001', '0010011', '0111101', '0100011', '0110001', '0101111', '0111011', '0110111', '0001011'],\r\n\t'G': [// The G type of encoding\r\n\t'0100111', '0110011', '0011011', '0100001', '0011101', '0111001', '0000101', '0010001', '0001001', '0010111'],\r\n\t'R': [// The R (right) type of encoding\r\n\t'1110010', '1100110', '1101100', '1000010', '1011100', '1001110', '1010000', '1000100', '1001000', '1110100'],\r\n\t'O': [// The O (odd) encoding for UPC-E\r\n\t'0001101', '0011001', '0010011', '0111101', '0100011', '0110001', '0101111', '0111011', '0110111', '0001011'],\r\n\t'E': [// The E (even) encoding for UPC-E\r\n\t'0100111', '0110011', '0011011', '0100001', '0011101', '0111001', '0000101', '0010001', '0001001', '0010111']\r\n};\r\n\r\n// Define the EAN-2 structure\r\nvar EAN2_STRUCTURE = exports.EAN2_STRUCTURE = ['LL', 'LG', 'GL', 'GG'];\r\n\r\n// Define the EAN-5 structure\r\nvar EAN5_STRUCTURE = exports.EAN5_STRUCTURE = ['GGLLL', 'GLGLL', 'GLLGL', 'GLLLG', 'LGGLL', 'LLGGL', 'LLLGG', 'LGLGL', 'LGLLG', 'LLGLG'];\r\n\r\n// Define the EAN-13 structure\r\nvar EAN13_STRUCTURE = exports.EAN13_STRUCTURE = ['LLLLLL', 'LLGLGG', 'LLGGLG', 'LLGGGL', 'LGLLGG', 'LGGLLG', 'LGGGLL', 'LGLGLG', 'LGLGGL', 'LGGLGL'];", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _constants = require('./constants');\r\n\r\n// Encode data string\r\nvar encode = function encode(data, structure, separator) {\r\n\tvar encoded = data.split('').map(function (val, idx) {\r\n\t\treturn _constants.BINARIES[structure[idx]];\r\n\t}).map(function (val, idx) {\r\n\t\treturn val ? val[data[idx]] : '';\r\n\t});\r\n\r\n\tif (separator) {\r\n\t\tvar last = data.length - 1;\r\n\t\tencoded = encoded.map(function (val, idx) {\r\n\t\t\treturn idx < last ? val + separator : val;\r\n\t\t});\r\n\t}\r\n\r\n\treturn encoded.join('');\r\n};\r\n\r\nexports.default = encode;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nvar _constants = require('./constants');\r\n\r\nvar _encoder = require('./encoder');\r\n\r\nvar _encoder2 = _interopRequireDefault(_encoder);\r\n\r\nvar _Barcode2 = require('../Barcode');\r\n\r\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\r\n\r\n// Base class for EAN8 & EAN13\r\nvar EAN = function (_Barcode) {\r\n\t_inherits(EAN, _Barcode);\r\n\r\n\tfunction EAN(data, options) {\r\n\t\t_classCallCheck(this, EAN);\r\n\r\n\t\t// Make sure the font is not bigger than the space between the guard bars\r\n\t\tvar _this = _possibleConstructorReturn(this, (EAN.__proto__ || Object.getPrototypeOf(EAN)).call(this, data, options));\r\n\r\n\t\t_this.fontSize = !options.flat && options.fontSize > options.width * 10 ? options.width * 10 : options.fontSize;\r\n\r\n\t\t// Make the guard bars go down half the way of the text\r\n\t\t_this.guardHeight = options.height + _this.fontSize / 2 + options.textMargin;\r\n\t\treturn _this;\r\n\t}\r\n\r\n\t_createClass(EAN, [{\r\n\t\tkey: 'encode',\r\n\t\tvalue: function encode() {\r\n\t\t\treturn this.options.flat ? this.encodeFlat() : this.encodeGuarded();\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'leftText',\r\n\t\tvalue: function leftText(from, to) {\r\n\t\t\treturn this.text.substr(from, to);\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'leftEncode',\r\n\t\tvalue: function leftEncode(data, structure) {\r\n\t\t\treturn (0, _encoder2.default)(data, structure);\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'rightText',\r\n\t\tvalue: function rightText(from, to) {\r\n\t\t\treturn this.text.substr(from, to);\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'rightEncode',\r\n\t\tvalue: function rightEncode(data, structure) {\r\n\t\t\treturn (0, _encoder2.default)(data, structure);\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'encodeGuarded',\r\n\t\tvalue: function encodeGuarded() {\r\n\t\t\tvar textOptions = { fontSize: this.fontSize };\r\n\t\t\tvar guardOptions = { height: this.guardHeight };\r\n\r\n\t\t\treturn [{ data: _constants.SIDE_BIN, options: guardOptions }, { data: this.leftEncode(), text: this.leftText(), options: textOptions }, { data: _constants.MIDDLE_BIN, options: guardOptions }, { data: this.rightEncode(), text: this.rightText(), options: textOptions }, { data: _constants.SIDE_BIN, options: guardOptions }];\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'encodeFlat',\r\n\t\tvalue: function encodeFlat() {\r\n\t\t\tvar data = [_constants.SIDE_BIN, this.leftEncode(), _constants.MIDDLE_BIN, this.rightEncode(), _constants.SIDE_BIN];\r\n\r\n\t\t\treturn {\r\n\t\t\t\tdata: data.join(''),\r\n\t\t\t\ttext: this.text\r\n\t\t\t};\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn EAN;\r\n}(_Barcode3.default);\r\n\r\nexports.default = EAN;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nvar _get = function get(object, property, receiver) { if (object === null) object = Function.prototype; var desc = Object.getOwnPropertyDescriptor(object, property); if (desc === undefined) { var parent = Object.getPrototypeOf(object); if (parent === null) { return undefined; } else { return get(parent, property, receiver); } } else if (\"value\" in desc) { return desc.value; } else { var getter = desc.get; if (getter === undefined) { return undefined; } return getter.call(receiver); } };\r\n\r\nvar _constants = require('./constants');\r\n\r\nvar _EAN2 = require('./EAN');\r\n\r\nvar _EAN3 = _interopRequireDefault(_EAN2);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation:\r\n// https://en.wikipedia.org/wiki/International_Article_Number_(EAN)#Binary_encoding_of_data_digits_into_EAN-13_barcode\r\n\r\n// Calculate the checksum digit\r\n// https://en.wikipedia.org/wiki/International_Article_Number_(EAN)#Calculation_of_checksum_digit\r\nvar checksum = function checksum(number) {\r\n\tvar res = number.substr(0, 12).split('').map(function (n) {\r\n\t\treturn +n;\r\n\t}).reduce(function (sum, a, idx) {\r\n\t\treturn idx % 2 ? sum + a * 3 : sum + a;\r\n\t}, 0);\r\n\r\n\treturn (10 - res % 10) % 10;\r\n};\r\n\r\nvar EAN13 = function (_EAN) {\r\n\t_inherits(EAN13, _EAN);\r\n\r\n\tfunction EAN13(data, options) {\r\n\t\t_classCallCheck(this, EAN13);\r\n\r\n\t\t// Add checksum if it does not exist\r\n\t\tif (data.search(/^[0-9]{12}$/) !== -1) {\r\n\t\t\tdata += checksum(data);\r\n\t\t}\r\n\r\n\t\t// Adds a last character to the end of the barcode\r\n\t\tvar _this = _possibleConstructorReturn(this, (EAN13.__proto__ || Object.getPrototypeOf(EAN13)).call(this, data, options));\r\n\r\n\t\t_this.lastChar = options.lastChar;\r\n\t\treturn _this;\r\n\t}\r\n\r\n\t_createClass(EAN13, [{\r\n\t\tkey: 'valid',\r\n\t\tvalue: function valid() {\r\n\t\t\treturn this.data.search(/^[0-9]{13}$/) !== -1 && +this.data[12] === checksum(this.data);\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'leftText',\r\n\t\tvalue: function leftText() {\r\n\t\t\treturn _get(EAN13.prototype.__proto__ || Object.getPrototypeOf(EAN13.prototype), 'leftText', this).call(this, 1, 6);\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'leftEncode',\r\n\t\tvalue: function leftEncode() {\r\n\t\t\tvar data = this.data.substr(1, 6);\r\n\t\t\tvar structure = _constants.EAN13_STRUCTURE[this.data[0]];\r\n\t\t\treturn _get(EAN13.prototype.__proto__ || Object.getPrototypeOf(EAN13.prototype), 'leftEncode', this).call(this, data, structure);\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'rightText',\r\n\t\tvalue: function rightText() {\r\n\t\t\treturn _get(EAN13.prototype.__proto__ || Object.getPrototypeOf(EAN13.prototype), 'rightText', this).call(this, 7, 6);\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'rightEncode',\r\n\t\tvalue: function rightEncode() {\r\n\t\t\tvar data = this.data.substr(7, 6);\r\n\t\t\treturn _get(EAN13.prototype.__proto__ || Object.getPrototypeOf(EAN13.prototype), 'rightEncode', this).call(this, data, 'RRRRRR');\r\n\t\t}\r\n\r\n\t\t// The \"standard\" way of printing EAN13 barcodes with guard bars\r\n\r\n\t}, {\r\n\t\tkey: 'encodeGuarded',\r\n\t\tvalue: function encodeGuarded() {\r\n\t\t\tvar data = _get(EAN13.prototype.__proto__ || Object.getPrototypeOf(EAN13.prototype), 'encodeGuarded', this).call(this);\r\n\r\n\t\t\t// Extend data with left digit & last character\r\n\t\t\tif (this.options.displayValue) {\r\n\t\t\t\tdata.unshift({\r\n\t\t\t\t\tdata: '000000000000',\r\n\t\t\t\t\ttext: this.text.substr(0, 1),\r\n\t\t\t\t\toptions: { textAlign: 'left', fontSize: this.fontSize }\r\n\t\t\t\t});\r\n\r\n\t\t\t\tif (this.options.lastChar) {\r\n\t\t\t\t\tdata.push({\r\n\t\t\t\t\t\tdata: '00'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tdata.push({\r\n\t\t\t\t\t\tdata: '00000',\r\n\t\t\t\t\t\ttext: this.options.lastChar,\r\n\t\t\t\t\t\toptions: { fontSize: this.fontSize }\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn data;\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn EAN13;\r\n}(_EAN3.default);\r\n\r\nexports.default = EAN13;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nvar _get = function get(object, property, receiver) { if (object === null) object = Function.prototype; var desc = Object.getOwnPropertyDescriptor(object, property); if (desc === undefined) { var parent = Object.getPrototypeOf(object); if (parent === null) { return undefined; } else { return get(parent, property, receiver); } } else if (\"value\" in desc) { return desc.value; } else { var getter = desc.get; if (getter === undefined) { return undefined; } return getter.call(receiver); } };\r\n\r\nvar _EAN2 = require('./EAN');\r\n\r\nvar _EAN3 = _interopRequireDefault(_EAN2);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation:\r\n// http://www.barcodeisland.com/ean8.phtml\r\n\r\n// Calculate the checksum digit\r\nvar checksum = function checksum(number) {\r\n\tvar res = number.substr(0, 7).split('').map(function (n) {\r\n\t\treturn +n;\r\n\t}).reduce(function (sum, a, idx) {\r\n\t\treturn idx % 2 ? sum + a : sum + a * 3;\r\n\t}, 0);\r\n\r\n\treturn (10 - res % 10) % 10;\r\n};\r\n\r\nvar EAN8 = function (_EAN) {\r\n\t_inherits(EAN8, _EAN);\r\n\r\n\tfunction EAN8(data, options) {\r\n\t\t_classCallCheck(this, EAN8);\r\n\r\n\t\t// Add checksum if it does not exist\r\n\t\tif (data.search(/^[0-9]{7}$/) !== -1) {\r\n\t\t\tdata += checksum(data);\r\n\t\t}\r\n\r\n\t\treturn _possibleConstructorReturn(this, (EAN8.__proto__ || Object.getPrototypeOf(EAN8)).call(this, data, options));\r\n\t}\r\n\r\n\t_createClass(EAN8, [{\r\n\t\tkey: 'valid',\r\n\t\tvalue: function valid() {\r\n\t\t\treturn this.data.search(/^[0-9]{8}$/) !== -1 && +this.data[7] === checksum(this.data);\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'leftText',\r\n\t\tvalue: function leftText() {\r\n\t\t\treturn _get(EAN8.prototype.__proto__ || Object.getPrototypeOf(EAN8.prototype), 'leftText', this).call(this, 0, 4);\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'leftEncode',\r\n\t\tvalue: function leftEncode() {\r\n\t\t\tvar data = this.data.substr(0, 4);\r\n\t\t\treturn _get(EAN8.prototype.__proto__ || Object.getPrototypeOf(EAN8.prototype), 'leftEncode', this).call(this, data, 'LLLL');\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'rightText',\r\n\t\tvalue: function rightText() {\r\n\t\t\treturn _get(EAN8.prototype.__proto__ || Object.getPrototypeOf(EAN8.prototype), 'rightText', this).call(this, 4, 4);\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'rightEncode',\r\n\t\tvalue: function rightEncode() {\r\n\t\t\tvar data = this.data.substr(4, 4);\r\n\t\t\treturn _get(EAN8.prototype.__proto__ || Object.getPrototypeOf(EAN8.prototype), 'rightEncode', this).call(this, data, 'RRRR');\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn EAN8;\r\n}(_EAN3.default);\r\n\r\nexports.default = EAN8;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nvar _constants = require('./constants');\r\n\r\nvar _encoder = require('./encoder');\r\n\r\nvar _encoder2 = _interopRequireDefault(_encoder);\r\n\r\nvar _Barcode2 = require('../Barcode');\r\n\r\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation:\r\n// https://en.wikipedia.org/wiki/EAN_5#Encoding\r\n\r\nvar checksum = function checksum(data) {\r\n\tvar result = data.split('').map(function (n) {\r\n\t\treturn +n;\r\n\t}).reduce(function (sum, a, idx) {\r\n\t\treturn idx % 2 ? sum + a * 9 : sum + a * 3;\r\n\t}, 0);\r\n\treturn result % 10;\r\n};\r\n\r\nvar EAN5 = function (_Barcode) {\r\n\t_inherits(EAN5, _Barcode);\r\n\r\n\tfunction EAN5(data, options) {\r\n\t\t_classCallCheck(this, EAN5);\r\n\r\n\t\treturn _possibleConstructorReturn(this, (EAN5.__proto__ || Object.getPrototypeOf(EAN5)).call(this, data, options));\r\n\t}\r\n\r\n\t_createClass(EAN5, [{\r\n\t\tkey: 'valid',\r\n\t\tvalue: function valid() {\r\n\t\t\treturn this.data.search(/^[0-9]{5}$/) !== -1;\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'encode',\r\n\t\tvalue: function encode() {\r\n\t\t\tvar structure = _constants.EAN5_STRUCTURE[checksum(this.data)];\r\n\t\t\treturn {\r\n\t\t\t\tdata: '1011' + (0, _encoder2.default)(this.data, structure, '01'),\r\n\t\t\t\ttext: this.text\r\n\t\t\t};\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn EAN5;\r\n}(_Barcode3.default);\r\n\r\nexports.default = EAN5;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nvar _constants = require('./constants');\r\n\r\nvar _encoder = require('./encoder');\r\n\r\nvar _encoder2 = _interopRequireDefault(_encoder);\r\n\r\nvar _Barcode2 = require('../Barcode');\r\n\r\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation:\r\n// https://en.wikipedia.org/wiki/EAN_2#Encoding\r\n\r\nvar EAN2 = function (_Barcode) {\r\n\t_inherits(EAN2, _Barcode);\r\n\r\n\tfunction EAN2(data, options) {\r\n\t\t_classCallCheck(this, EAN2);\r\n\r\n\t\treturn _possibleConstructorReturn(this, (EAN2.__proto__ || Object.getPrototypeOf(EAN2)).call(this, data, options));\r\n\t}\r\n\r\n\t_createClass(EAN2, [{\r\n\t\tkey: 'valid',\r\n\t\tvalue: function valid() {\r\n\t\t\treturn this.data.search(/^[0-9]{2}$/) !== -1;\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'encode',\r\n\t\tvalue: function encode() {\r\n\t\t\t// Choose the structure based on the number mod 4\r\n\t\t\tvar structure = _constants.EAN2_STRUCTURE[parseInt(this.data) % 4];\r\n\t\t\treturn {\r\n\t\t\t\t// Start bits + Encode the two digits with 01 in between\r\n\t\t\t\tdata: '1011' + (0, _encoder2.default)(this.data, structure, '01'),\r\n\t\t\t\ttext: this.text\r\n\t\t\t};\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn EAN2;\r\n}(_Barcode3.default);\r\n\r\nexports.default = EAN2;", "\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nexports.checksum = checksum;\r\n\r\nvar _encoder = require(\"./encoder\");\r\n\r\nvar _encoder2 = _interopRequireDefault(_encoder);\r\n\r\nvar _Barcode2 = require(\"../Barcode.js\");\r\n\r\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation:\r\n// https://en.wikipedia.org/wiki/Universal_Product_Code#Encoding\r\n\r\nvar UPC = function (_Barcode) {\r\n\t_inherits(UPC, _Barcode);\r\n\r\n\tfunction UPC(data, options) {\r\n\t\t_classCallCheck(this, UPC);\r\n\r\n\t\t// Add checksum if it does not exist\r\n\t\tif (data.search(/^[0-9]{11}$/) !== -1) {\r\n\t\t\tdata += checksum(data);\r\n\t\t}\r\n\r\n\t\tvar _this = _possibleConstructorReturn(this, (UPC.__proto__ || Object.getPrototypeOf(UPC)).call(this, data, options));\r\n\r\n\t\t_this.displayValue = options.displayValue;\r\n\r\n\t\t// Make sure the font is not bigger than the space between the guard bars\r\n\t\tif (options.fontSize > options.width * 10) {\r\n\t\t\t_this.fontSize = options.width * 10;\r\n\t\t} else {\r\n\t\t\t_this.fontSize = options.fontSize;\r\n\t\t}\r\n\r\n\t\t// Make the guard bars go down half the way of the text\r\n\t\t_this.guardHeight = options.height + _this.fontSize / 2 + options.textMargin;\r\n\t\treturn _this;\r\n\t}\r\n\r\n\t_createClass(UPC, [{\r\n\t\tkey: \"valid\",\r\n\t\tvalue: function valid() {\r\n\t\t\treturn this.data.search(/^[0-9]{12}$/) !== -1 && this.data[11] == checksum(this.data);\r\n\t\t}\r\n\t}, {\r\n\t\tkey: \"encode\",\r\n\t\tvalue: function encode() {\r\n\t\t\tif (this.options.flat) {\r\n\t\t\t\treturn this.flatEncoding();\r\n\t\t\t} else {\r\n\t\t\t\treturn this.guardedEncoding();\r\n\t\t\t}\r\n\t\t}\r\n\t}, {\r\n\t\tkey: \"flatEncoding\",\r\n\t\tvalue: function flatEncoding() {\r\n\t\t\tvar result = \"\";\r\n\r\n\t\t\tresult += \"101\";\r\n\t\t\tresult += (0, _encoder2.default)(this.data.substr(0, 6), \"LLLLLL\");\r\n\t\t\tresult += \"01010\";\r\n\t\t\tresult += (0, _encoder2.default)(this.data.substr(6, 6), \"RRRRRR\");\r\n\t\t\tresult += \"101\";\r\n\r\n\t\t\treturn {\r\n\t\t\t\tdata: result,\r\n\t\t\t\ttext: this.text\r\n\t\t\t};\r\n\t\t}\r\n\t}, {\r\n\t\tkey: \"guardedEncoding\",\r\n\t\tvalue: function guardedEncoding() {\r\n\t\t\tvar result = [];\r\n\r\n\t\t\t// Add the first digit\r\n\t\t\tif (this.displayValue) {\r\n\t\t\t\tresult.push({\r\n\t\t\t\t\tdata: \"00000000\",\r\n\t\t\t\t\ttext: this.text.substr(0, 1),\r\n\t\t\t\t\toptions: { textAlign: \"left\", fontSize: this.fontSize }\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\t// Add the guard bars\r\n\t\t\tresult.push({\r\n\t\t\t\tdata: \"101\" + (0, _encoder2.default)(this.data[0], \"L\"),\r\n\t\t\t\toptions: { height: this.guardHeight }\r\n\t\t\t});\r\n\r\n\t\t\t// Add the left side\r\n\t\t\tresult.push({\r\n\t\t\t\tdata: (0, _encoder2.default)(this.data.substr(1, 5), \"LLLLL\"),\r\n\t\t\t\ttext: this.text.substr(1, 5),\r\n\t\t\t\toptions: { fontSize: this.fontSize }\r\n\t\t\t});\r\n\r\n\t\t\t// Add the middle bits\r\n\t\t\tresult.push({\r\n\t\t\t\tdata: \"01010\",\r\n\t\t\t\toptions: { height: this.guardHeight }\r\n\t\t\t});\r\n\r\n\t\t\t// Add the right side\r\n\t\t\tresult.push({\r\n\t\t\t\tdata: (0, _encoder2.default)(this.data.substr(6, 5), \"RRRRR\"),\r\n\t\t\t\ttext: this.text.substr(6, 5),\r\n\t\t\t\toptions: { fontSize: this.fontSize }\r\n\t\t\t});\r\n\r\n\t\t\t// Add the end bits\r\n\t\t\tresult.push({\r\n\t\t\t\tdata: (0, _encoder2.default)(this.data[11], \"R\") + \"101\",\r\n\t\t\t\toptions: { height: this.guardHeight }\r\n\t\t\t});\r\n\r\n\t\t\t// Add the last digit\r\n\t\t\tif (this.displayValue) {\r\n\t\t\t\tresult.push({\r\n\t\t\t\t\tdata: \"00000000\",\r\n\t\t\t\t\ttext: this.text.substr(11, 1),\r\n\t\t\t\t\toptions: { textAlign: \"right\", fontSize: this.fontSize }\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\treturn result;\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn UPC;\r\n}(_Barcode3.default);\r\n\r\n// Calulate the checksum digit\r\n// https://en.wikipedia.org/wiki/International_Article_Number_(EAN)#Calculation_of_checksum_digit\r\n\r\n\r\nfunction checksum(number) {\r\n\tvar result = 0;\r\n\r\n\tvar i;\r\n\tfor (i = 1; i < 11; i += 2) {\r\n\t\tresult += parseInt(number[i]);\r\n\t}\r\n\tfor (i = 0; i < 11; i += 2) {\r\n\t\tresult += parseInt(number[i]) * 3;\r\n\t}\r\n\r\n\treturn (10 - result % 10) % 10;\r\n}\r\n\r\nexports.default = UPC;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nvar _encoder = require('./encoder');\r\n\r\nvar _encoder2 = _interopRequireDefault(_encoder);\r\n\r\nvar _Barcode2 = require('../Barcode.js');\r\n\r\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\r\n\r\nvar _UPC = require('./UPC.js');\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation:\r\n// https://en.wikipedia.org/wiki/Universal_Product_Code#Encoding\r\n//\r\n// UPC-E documentation:\r\n// https://en.wikipedia.org/wiki/Universal_Product_Code#UPC-E\r\n\r\nvar EXPANSIONS = [\"XX00000XXX\", \"XX10000XXX\", \"XX20000XXX\", \"XXX00000XX\", \"XXXX00000X\", \"XXXXX00005\", \"XXXXX00006\", \"XXXXX00007\", \"XXXXX00008\", \"XXXXX00009\"];\r\n\r\nvar PARITIES = [[\"EEEOOO\", \"OOOEEE\"], [\"EEOEOO\", \"OOEOEE\"], [\"EEOOEO\", \"OOEEOE\"], [\"EEOOOE\", \"OOEEEO\"], [\"EOEEOO\", \"OEOOEE\"], [\"EOOEEO\", \"OEEOOE\"], [\"EOOOEE\", \"OEEEOO\"], [\"EOEOEO\", \"OEOEOE\"], [\"EOEOOE\", \"OEOEEO\"], [\"EOOEOE\", \"OEEOEO\"]];\r\n\r\nvar UPCE = function (_Barcode) {\r\n\t_inherits(UPCE, _Barcode);\r\n\r\n\tfunction UPCE(data, options) {\r\n\t\t_classCallCheck(this, UPCE);\r\n\r\n\t\tvar _this = _possibleConstructorReturn(this, (UPCE.__proto__ || Object.getPrototypeOf(UPCE)).call(this, data, options));\r\n\t\t// Code may be 6 or 8 digits;\r\n\t\t// A 7 digit code is ambiguous as to whether the extra digit\r\n\t\t// is a UPC-A check or number system digit.\r\n\r\n\r\n\t\t_this.isValid = false;\r\n\t\tif (data.search(/^[0-9]{6}$/) !== -1) {\r\n\t\t\t_this.middleDigits = data;\r\n\t\t\t_this.upcA = expandToUPCA(data, \"0\");\r\n\t\t\t_this.text = options.text || '' + _this.upcA[0] + data + _this.upcA[_this.upcA.length - 1];\r\n\t\t\t_this.isValid = true;\r\n\t\t} else if (data.search(/^[01][0-9]{7}$/) !== -1) {\r\n\t\t\t_this.middleDigits = data.substring(1, data.length - 1);\r\n\t\t\t_this.upcA = expandToUPCA(_this.middleDigits, data[0]);\r\n\r\n\t\t\tif (_this.upcA[_this.upcA.length - 1] === data[data.length - 1]) {\r\n\t\t\t\t_this.isValid = true;\r\n\t\t\t} else {\r\n\t\t\t\t// checksum mismatch\r\n\t\t\t\treturn _possibleConstructorReturn(_this);\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\treturn _possibleConstructorReturn(_this);\r\n\t\t}\r\n\r\n\t\t_this.displayValue = options.displayValue;\r\n\r\n\t\t// Make sure the font is not bigger than the space between the guard bars\r\n\t\tif (options.fontSize > options.width * 10) {\r\n\t\t\t_this.fontSize = options.width * 10;\r\n\t\t} else {\r\n\t\t\t_this.fontSize = options.fontSize;\r\n\t\t}\r\n\r\n\t\t// Make the guard bars go down half the way of the text\r\n\t\t_this.guardHeight = options.height + _this.fontSize / 2 + options.textMargin;\r\n\t\treturn _this;\r\n\t}\r\n\r\n\t_createClass(UPCE, [{\r\n\t\tkey: 'valid',\r\n\t\tvalue: function valid() {\r\n\t\t\treturn this.isValid;\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'encode',\r\n\t\tvalue: function encode() {\r\n\t\t\tif (this.options.flat) {\r\n\t\t\t\treturn this.flatEncoding();\r\n\t\t\t} else {\r\n\t\t\t\treturn this.guardedEncoding();\r\n\t\t\t}\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'flatEncoding',\r\n\t\tvalue: function flatEncoding() {\r\n\t\t\tvar result = \"\";\r\n\r\n\t\t\tresult += \"101\";\r\n\t\t\tresult += this.encodeMiddleDigits();\r\n\t\t\tresult += \"010101\";\r\n\r\n\t\t\treturn {\r\n\t\t\t\tdata: result,\r\n\t\t\t\ttext: this.text\r\n\t\t\t};\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'guardedEncoding',\r\n\t\tvalue: function guardedEncoding() {\r\n\t\t\tvar result = [];\r\n\r\n\t\t\t// Add the UPC-A number system digit beneath the quiet zone\r\n\t\t\tif (this.displayValue) {\r\n\t\t\t\tresult.push({\r\n\t\t\t\t\tdata: \"00000000\",\r\n\t\t\t\t\ttext: this.text[0],\r\n\t\t\t\t\toptions: { textAlign: \"left\", fontSize: this.fontSize }\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\t// Add the guard bars\r\n\t\t\tresult.push({\r\n\t\t\t\tdata: \"101\",\r\n\t\t\t\toptions: { height: this.guardHeight }\r\n\t\t\t});\r\n\r\n\t\t\t// Add the 6 UPC-E digits\r\n\t\t\tresult.push({\r\n\t\t\t\tdata: this.encodeMiddleDigits(),\r\n\t\t\t\ttext: this.text.substring(1, 7),\r\n\t\t\t\toptions: { fontSize: this.fontSize }\r\n\t\t\t});\r\n\r\n\t\t\t// Add the end bits\r\n\t\t\tresult.push({\r\n\t\t\t\tdata: \"010101\",\r\n\t\t\t\toptions: { height: this.guardHeight }\r\n\t\t\t});\r\n\r\n\t\t\t// Add the UPC-A check digit beneath the quiet zone\r\n\t\t\tif (this.displayValue) {\r\n\t\t\t\tresult.push({\r\n\t\t\t\t\tdata: \"00000000\",\r\n\t\t\t\t\ttext: this.text[7],\r\n\t\t\t\t\toptions: { textAlign: \"right\", fontSize: this.fontSize }\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\treturn result;\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'encodeMiddleDigits',\r\n\t\tvalue: function encodeMiddleDigits() {\r\n\t\t\tvar numberSystem = this.upcA[0];\r\n\t\t\tvar checkDigit = this.upcA[this.upcA.length - 1];\r\n\t\t\tvar parity = PARITIES[parseInt(checkDigit)][parseInt(numberSystem)];\r\n\t\t\treturn (0, _encoder2.default)(this.middleDigits, parity);\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn UPCE;\r\n}(_Barcode3.default);\r\n\r\nfunction expandToUPCA(middleDigits, numberSystem) {\r\n\tvar lastUpcE = parseInt(middleDigits[middleDigits.length - 1]);\r\n\tvar expansion = EXPANSIONS[lastUpcE];\r\n\r\n\tvar result = \"\";\r\n\tvar digitIndex = 0;\r\n\tfor (var i = 0; i < expansion.length; i++) {\r\n\t\tvar c = expansion[i];\r\n\t\tif (c === 'X') {\r\n\t\t\tresult += middleDigits[digitIndex++];\r\n\t\t} else {\r\n\t\t\tresult += c;\r\n\t\t}\r\n\t}\r\n\r\n\tresult = '' + numberSystem + result;\r\n\treturn '' + result + (0, _UPC.checksum)(result);\r\n}\r\n\r\nexports.default = UPCE;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n  value: true\r\n});\r\nexports.UPCE = exports.UPC = exports.EAN2 = exports.EAN5 = exports.EAN8 = exports.EAN13 = undefined;\r\n\r\nvar _EAN = require('./EAN13.js');\r\n\r\nvar _EAN2 = _interopRequireDefault(_EAN);\r\n\r\nvar _EAN3 = require('./EAN8.js');\r\n\r\nvar _EAN4 = _interopRequireDefault(_EAN3);\r\n\r\nvar _EAN5 = require('./EAN5.js');\r\n\r\nvar _EAN6 = _interopRequireDefault(_EAN5);\r\n\r\nvar _EAN7 = require('./EAN2.js');\r\n\r\nvar _EAN8 = _interopRequireDefault(_EAN7);\r\n\r\nvar _UPC = require('./UPC.js');\r\n\r\nvar _UPC2 = _interopRequireDefault(_UPC);\r\n\r\nvar _UPCE = require('./UPCE.js');\r\n\r\nvar _UPCE2 = _interopRequireDefault(_UPCE);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nexports.EAN13 = _EAN2.default;\r\nexports.EAN8 = _EAN4.default;\r\nexports.EAN5 = _EAN6.default;\r\nexports.EAN2 = _EAN8.default;\r\nexports.UPC = _UPC2.default;\r\nexports.UPCE = _UPCE2.default;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\nvar START_BIN = exports.START_BIN = '1010';\r\nvar END_BIN = exports.END_BIN = '11101';\r\n\r\nvar BINARIES = exports.BINARIES = ['00110', '10001', '01001', '11000', '00101', '10100', '01100', '00011', '10010', '01010'];", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nvar _constants = require('./constants');\r\n\r\nvar _Barcode2 = require('../Barcode');\r\n\r\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\r\n\r\nvar ITF = function (_Barcode) {\r\n\t_inherits(ITF, _Barcode);\r\n\r\n\tfunction ITF() {\r\n\t\t_classCallCheck(this, ITF);\r\n\r\n\t\treturn _possibleConstructorReturn(this, (ITF.__proto__ || Object.getPrototypeOf(ITF)).apply(this, arguments));\r\n\t}\r\n\r\n\t_createClass(ITF, [{\r\n\t\tkey: 'valid',\r\n\t\tvalue: function valid() {\r\n\t\t\treturn this.data.search(/^([0-9]{2})+$/) !== -1;\r\n\t\t}\r\n\t}, {\r\n\t\tkey: 'encode',\r\n\t\tvalue: function encode() {\r\n\t\t\tvar _this2 = this;\r\n\r\n\t\t\t// Calculate all the digit pairs\r\n\t\t\tvar encoded = this.data.match(/.{2}/g).map(function (pair) {\r\n\t\t\t\treturn _this2.encodePair(pair);\r\n\t\t\t}).join('');\r\n\r\n\t\t\treturn {\r\n\t\t\t\tdata: _constants.START_BIN + encoded + _constants.END_BIN,\r\n\t\t\t\ttext: this.text\r\n\t\t\t};\r\n\t\t}\r\n\r\n\t\t// Calculate the data of a number pair\r\n\r\n\t}, {\r\n\t\tkey: 'encodePair',\r\n\t\tvalue: function encodePair(pair) {\r\n\t\t\tvar second = _constants.BINARIES[pair[1]];\r\n\r\n\t\t\treturn _constants.BINARIES[pair[0]].split('').map(function (first, idx) {\r\n\t\t\t\treturn (first === '1' ? '111' : '1') + (second[idx] === '1' ? '000' : '0');\r\n\t\t\t}).join('');\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn ITF;\r\n}(_Barcode3.default);\r\n\r\nexports.default = ITF;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nvar _ITF2 = require('./ITF');\r\n\r\nvar _ITF3 = _interopRequireDefault(_ITF2);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\r\n\r\n// Calculate the checksum digit\r\nvar checksum = function checksum(data) {\r\n\tvar res = data.substr(0, 13).split('').map(function (num) {\r\n\t\treturn parseInt(num, 10);\r\n\t}).reduce(function (sum, n, idx) {\r\n\t\treturn sum + n * (3 - idx % 2 * 2);\r\n\t}, 0);\r\n\r\n\treturn Math.ceil(res / 10) * 10 - res;\r\n};\r\n\r\nvar ITF14 = function (_ITF) {\r\n\t_inherits(ITF14, _ITF);\r\n\r\n\tfunction ITF14(data, options) {\r\n\t\t_classCallCheck(this, ITF14);\r\n\r\n\t\t// Add checksum if it does not exist\r\n\t\tif (data.search(/^[0-9]{13}$/) !== -1) {\r\n\t\t\tdata += checksum(data);\r\n\t\t}\r\n\t\treturn _possibleConstructorReturn(this, (ITF14.__proto__ || Object.getPrototypeOf(ITF14)).call(this, data, options));\r\n\t}\r\n\r\n\t_createClass(ITF14, [{\r\n\t\tkey: 'valid',\r\n\t\tvalue: function valid() {\r\n\t\t\treturn this.data.search(/^[0-9]{14}$/) !== -1 && +this.data[13] === checksum(this.data);\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn ITF14;\r\n}(_ITF3.default);\r\n\r\nexports.default = ITF14;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n  value: true\r\n});\r\nexports.ITF14 = exports.ITF = undefined;\r\n\r\nvar _ITF = require('./ITF');\r\n\r\nvar _ITF2 = _interopRequireDefault(_ITF);\r\n\r\nvar _ITF3 = require('./ITF14');\r\n\r\nvar _ITF4 = _interopRequireDefault(_ITF3);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nexports.ITF = _ITF2.default;\r\nexports.ITF14 = _ITF4.default;", "\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nvar _Barcode2 = require(\"../Barcode.js\");\r\n\r\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation\r\n// https://en.wikipedia.org/wiki/MSI_Barcode#Character_set_and_binary_lookup\r\n\r\nvar MSI = function (_Barcode) {\r\n\t_inherits(MSI, _Barcode);\r\n\r\n\tfunction MSI(data, options) {\r\n\t\t_classCallCheck(this, MSI);\r\n\r\n\t\treturn _possibleConstructorReturn(this, (MSI.__proto__ || Object.getPrototypeOf(MSI)).call(this, data, options));\r\n\t}\r\n\r\n\t_createClass(MSI, [{\r\n\t\tkey: \"encode\",\r\n\t\tvalue: function encode() {\r\n\t\t\t// Start bits\r\n\t\t\tvar ret = \"110\";\r\n\r\n\t\t\tfor (var i = 0; i < this.data.length; i++) {\r\n\t\t\t\t// Convert the character to binary (always 4 binary digits)\r\n\t\t\t\tvar digit = parseInt(this.data[i]);\r\n\t\t\t\tvar bin = digit.toString(2);\r\n\t\t\t\tbin = addZeroes(bin, 4 - bin.length);\r\n\r\n\t\t\t\t// Add 100 for every zero and 110 for every 1\r\n\t\t\t\tfor (var b = 0; b < bin.length; b++) {\r\n\t\t\t\t\tret += bin[b] == \"0\" ? \"100\" : \"110\";\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// End bits\r\n\t\t\tret += \"1001\";\r\n\r\n\t\t\treturn {\r\n\t\t\t\tdata: ret,\r\n\t\t\t\ttext: this.text\r\n\t\t\t};\r\n\t\t}\r\n\t}, {\r\n\t\tkey: \"valid\",\r\n\t\tvalue: function valid() {\r\n\t\t\treturn this.data.search(/^[0-9]+$/) !== -1;\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn MSI;\r\n}(_Barcode3.default);\r\n\r\nfunction addZeroes(number, n) {\r\n\tfor (var i = 0; i < n; i++) {\r\n\t\tnumber = \"0\" + number;\r\n\t}\r\n\treturn number;\r\n}\r\n\r\nexports.default = MSI;", "\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\nexports.mod10 = mod10;\r\nexports.mod11 = mod11;\r\nfunction mod10(number) {\r\n\tvar sum = 0;\r\n\tfor (var i = 0; i < number.length; i++) {\r\n\t\tvar n = parseInt(number[i]);\r\n\t\tif ((i + number.length) % 2 === 0) {\r\n\t\t\tsum += n;\r\n\t\t} else {\r\n\t\t\tsum += n * 2 % 10 + Math.floor(n * 2 / 10);\r\n\t\t}\r\n\t}\r\n\treturn (10 - sum % 10) % 10;\r\n}\r\n\r\nfunction mod11(number) {\r\n\tvar sum = 0;\r\n\tvar weights = [2, 3, 4, 5, 6, 7];\r\n\tfor (var i = 0; i < number.length; i++) {\r\n\t\tvar n = parseInt(number[number.length - 1 - i]);\r\n\t\tsum += weights[i % weights.length] * n;\r\n\t}\r\n\treturn (11 - sum % 11) % 11;\r\n}", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _MSI2 = require('./MSI.js');\r\n\r\nvar _MSI3 = _interopRequireDefault(_MSI2);\r\n\r\nvar _checksums = require('./checksums.js');\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\r\n\r\nvar MSI10 = function (_MSI) {\r\n\t_inherits(MSI10, _MSI);\r\n\r\n\tfunction MSI10(data, options) {\r\n\t\t_classCallCheck(this, MSI10);\r\n\r\n\t\treturn _possibleConstructorReturn(this, (MSI10.__proto__ || Object.getPrototypeOf(MSI10)).call(this, data + (0, _checksums.mod10)(data), options));\r\n\t}\r\n\r\n\treturn MSI10;\r\n}(_MSI3.default);\r\n\r\nexports.default = MSI10;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _MSI2 = require('./MSI.js');\r\n\r\nvar _MSI3 = _interopRequireDefault(_MSI2);\r\n\r\nvar _checksums = require('./checksums.js');\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\r\n\r\nvar MSI11 = function (_MSI) {\r\n\t_inherits(MSI11, _MSI);\r\n\r\n\tfunction MSI11(data, options) {\r\n\t\t_classCallCheck(this, MSI11);\r\n\r\n\t\treturn _possibleConstructorReturn(this, (MSI11.__proto__ || Object.getPrototypeOf(MSI11)).call(this, data + (0, _checksums.mod11)(data), options));\r\n\t}\r\n\r\n\treturn MSI11;\r\n}(_MSI3.default);\r\n\r\nexports.default = MSI11;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _MSI2 = require('./MSI.js');\r\n\r\nvar _MSI3 = _interopRequireDefault(_MSI2);\r\n\r\nvar _checksums = require('./checksums.js');\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\r\n\r\nvar MSI1010 = function (_MSI) {\r\n\t_inherits(MSI1010, _MSI);\r\n\r\n\tfunction MSI1010(data, options) {\r\n\t\t_classCallCheck(this, MSI1010);\r\n\r\n\t\tdata += (0, _checksums.mod10)(data);\r\n\t\tdata += (0, _checksums.mod10)(data);\r\n\t\treturn _possibleConstructorReturn(this, (MSI1010.__proto__ || Object.getPrototypeOf(MSI1010)).call(this, data, options));\r\n\t}\r\n\r\n\treturn MSI1010;\r\n}(_MSI3.default);\r\n\r\nexports.default = MSI1010;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _MSI2 = require('./MSI.js');\r\n\r\nvar _MSI3 = _interopRequireDefault(_MSI2);\r\n\r\nvar _checksums = require('./checksums.js');\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\r\n\r\nvar MSI1110 = function (_MSI) {\r\n\t_inherits(MSI1110, _MSI);\r\n\r\n\tfunction MSI1110(data, options) {\r\n\t\t_classCallCheck(this, MSI1110);\r\n\r\n\t\tdata += (0, _checksums.mod11)(data);\r\n\t\tdata += (0, _checksums.mod10)(data);\r\n\t\treturn _possibleConstructorReturn(this, (MSI1110.__proto__ || Object.getPrototypeOf(MSI1110)).call(this, data, options));\r\n\t}\r\n\r\n\treturn MSI1110;\r\n}(_MSI3.default);\r\n\r\nexports.default = MSI1110;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n  value: true\r\n});\r\nexports.MSI1110 = exports.MSI1010 = exports.MSI11 = exports.MSI10 = exports.MSI = undefined;\r\n\r\nvar _MSI = require('./MSI.js');\r\n\r\nvar _MSI2 = _interopRequireDefault(_MSI);\r\n\r\nvar _MSI3 = require('./MSI10.js');\r\n\r\nvar _MSI4 = _interopRequireDefault(_MSI3);\r\n\r\nvar _MSI5 = require('./MSI11.js');\r\n\r\nvar _MSI6 = _interopRequireDefault(_MSI5);\r\n\r\nvar _MSI7 = require('./MSI1010.js');\r\n\r\nvar _MSI8 = _interopRequireDefault(_MSI7);\r\n\r\nvar _MSI9 = require('./MSI1110.js');\r\n\r\nvar _MSI10 = _interopRequireDefault(_MSI9);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nexports.MSI = _MSI2.default;\r\nexports.MSI10 = _MSI4.default;\r\nexports.MSI11 = _MSI6.default;\r\nexports.MSI1010 = _MSI8.default;\r\nexports.MSI1110 = _MSI10.default;", "\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\nexports.pharmacode = undefined;\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nvar _Barcode2 = require(\"../Barcode.js\");\r\n\r\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation\r\n// http://www.gomaro.ch/ftproot/Laetus_PHARMA-CODE.pdf\r\n\r\nvar pharmacode = function (_Barcode) {\r\n\t_inherits(pharmacode, _Barcode);\r\n\r\n\tfunction pharmacode(data, options) {\r\n\t\t_classCallCheck(this, pharmacode);\r\n\r\n\t\tvar _this = _possibleConstructorReturn(this, (pharmacode.__proto__ || Object.getPrototypeOf(pharmacode)).call(this, data, options));\r\n\r\n\t\t_this.number = parseInt(data, 10);\r\n\t\treturn _this;\r\n\t}\r\n\r\n\t_createClass(pharmacode, [{\r\n\t\tkey: \"encode\",\r\n\t\tvalue: function encode() {\r\n\t\t\tvar z = this.number;\r\n\t\t\tvar result = \"\";\r\n\r\n\t\t\t// http://i.imgur.com/RMm4UDJ.png\r\n\t\t\t// (source: http://www.gomaro.ch/ftproot/Laetus_PHARMA-CODE.pdf, page: 34)\r\n\t\t\twhile (!isNaN(z) && z != 0) {\r\n\t\t\t\tif (z % 2 === 0) {\r\n\t\t\t\t\t// Even\r\n\t\t\t\t\tresult = \"11100\" + result;\r\n\t\t\t\t\tz = (z - 2) / 2;\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// Odd\r\n\t\t\t\t\tresult = \"100\" + result;\r\n\t\t\t\t\tz = (z - 1) / 2;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// Remove the two last zeroes\r\n\t\t\tresult = result.slice(0, -2);\r\n\r\n\t\t\treturn {\r\n\t\t\t\tdata: result,\r\n\t\t\t\ttext: this.text\r\n\t\t\t};\r\n\t\t}\r\n\t}, {\r\n\t\tkey: \"valid\",\r\n\t\tvalue: function valid() {\r\n\t\t\treturn this.number >= 3 && this.number <= 131070;\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn pharmacode;\r\n}(_Barcode3.default);\r\n\r\nexports.pharmacode = pharmacode;", "\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\nexports.codabar = undefined;\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nvar _Barcode2 = require(\"../Barcode.js\");\r\n\r\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding specification:\r\n// http://www.barcodeisland.com/codabar.phtml\r\n\r\nvar codabar = function (_Barcode) {\r\n\t_inherits(codabar, _Barcode);\r\n\r\n\tfunction codabar(data, options) {\r\n\t\t_classCallCheck(this, codabar);\r\n\r\n\t\tif (data.search(/^[0-9\\-\\$\\:\\.\\+\\/]+$/) === 0) {\r\n\t\t\tdata = \"A\" + data + \"A\";\r\n\t\t}\r\n\r\n\t\tvar _this = _possibleConstructorReturn(this, (codabar.__proto__ || Object.getPrototypeOf(codabar)).call(this, data.toUpperCase(), options));\r\n\r\n\t\t_this.text = _this.options.text || _this.text.replace(/[A-D]/g, '');\r\n\t\treturn _this;\r\n\t}\r\n\r\n\t_createClass(codabar, [{\r\n\t\tkey: \"valid\",\r\n\t\tvalue: function valid() {\r\n\t\t\treturn this.data.search(/^[A-D][0-9\\-\\$\\:\\.\\+\\/]+[A-D]$/) !== -1;\r\n\t\t}\r\n\t}, {\r\n\t\tkey: \"encode\",\r\n\t\tvalue: function encode() {\r\n\t\t\tvar result = [];\r\n\t\t\tvar encodings = this.getEncodings();\r\n\t\t\tfor (var i = 0; i < this.data.length; i++) {\r\n\t\t\t\tresult.push(encodings[this.data.charAt(i)]);\r\n\t\t\t\t// for all characters except the last, append a narrow-space (\"0\")\r\n\t\t\t\tif (i !== this.data.length - 1) {\r\n\t\t\t\t\tresult.push(\"0\");\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn {\r\n\t\t\t\ttext: this.text,\r\n\t\t\t\tdata: result.join('')\r\n\t\t\t};\r\n\t\t}\r\n\t}, {\r\n\t\tkey: \"getEncodings\",\r\n\t\tvalue: function getEncodings() {\r\n\t\t\treturn {\r\n\t\t\t\t\"0\": \"101010011\",\r\n\t\t\t\t\"1\": \"101011001\",\r\n\t\t\t\t\"2\": \"101001011\",\r\n\t\t\t\t\"3\": \"110010101\",\r\n\t\t\t\t\"4\": \"101101001\",\r\n\t\t\t\t\"5\": \"110101001\",\r\n\t\t\t\t\"6\": \"100101011\",\r\n\t\t\t\t\"7\": \"100101101\",\r\n\t\t\t\t\"8\": \"100110101\",\r\n\t\t\t\t\"9\": \"110100101\",\r\n\t\t\t\t\"-\": \"101001101\",\r\n\t\t\t\t\"$\": \"101100101\",\r\n\t\t\t\t\":\": \"1101011011\",\r\n\t\t\t\t\"/\": \"1101101011\",\r\n\t\t\t\t\".\": \"1101101101\",\r\n\t\t\t\t\"+\": \"1011011011\",\r\n\t\t\t\t\"A\": \"1011001001\",\r\n\t\t\t\t\"B\": \"1001001011\",\r\n\t\t\t\t\"C\": \"1010010011\",\r\n\t\t\t\t\"D\": \"1010011001\"\r\n\t\t\t};\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn codabar;\r\n}(_Barcode3.default);\r\n\r\nexports.codabar = codabar;", "\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\nexports.GenericBarcode = undefined;\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nvar _Barcode2 = require(\"../Barcode.js\");\r\n\r\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\r\n\r\nvar GenericBarcode = function (_Barcode) {\r\n\t_inherits(GenericBarcode, _Barcode);\r\n\r\n\tfunction GenericBarcode(data, options) {\r\n\t\t_classCallCheck(this, GenericBarcode);\r\n\r\n\t\treturn _possibleConstructorReturn(this, (GenericBarcode.__proto__ || Object.getPrototypeOf(GenericBarcode)).call(this, data, options)); // Sets this.data and this.text\r\n\t}\r\n\r\n\t// Return the corresponding binary numbers for the data provided\r\n\r\n\r\n\t_createClass(GenericBarcode, [{\r\n\t\tkey: \"encode\",\r\n\t\tvalue: function encode() {\r\n\t\t\treturn {\r\n\t\t\t\tdata: \"10101010101010101010101010101010101010101\",\r\n\t\t\t\ttext: this.text\r\n\t\t\t};\r\n\t\t}\r\n\r\n\t\t// Resturn true/false if the string provided is valid for this encoder\r\n\r\n\t}, {\r\n\t\tkey: \"valid\",\r\n\t\tvalue: function valid() {\r\n\t\t\treturn true;\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn GenericBarcode;\r\n}(_Barcode3.default);\r\n\r\nexports.GenericBarcode = GenericBarcode;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _CODE = require('./CODE39/');\r\n\r\nvar _CODE2 = require('./CODE128/');\r\n\r\nvar _EAN_UPC = require('./EAN_UPC/');\r\n\r\nvar _ITF = require('./ITF/');\r\n\r\nvar _MSI = require('./MSI/');\r\n\r\nvar _pharmacode = require('./pharmacode/');\r\n\r\nvar _codabar = require('./codabar');\r\n\r\nvar _GenericBarcode = require('./GenericBarcode/');\r\n\r\nexports.default = {\r\n\tCODE39: _CODE.CODE39,\r\n\tCODE128: _CODE2.CODE128, CODE128A: _CODE2.CODE128A, CODE128B: _CODE2.CODE128B, CODE128C: _CODE2.CODE128C,\r\n\tEAN13: _EAN_UPC.EAN13, EAN8: _EAN_UPC.EAN8, EAN5: _EAN_UPC.EAN5, EAN2: _EAN_UPC.EAN2, UPC: _EAN_UPC.UPC, UPCE: _EAN_UPC.UPCE,\r\n\tITF14: _ITF.ITF14,\r\n\tITF: _ITF.ITF,\r\n\tMSI: _MSI.MSI, MSI10: _MSI.MSI10, MSI11: _MSI.MSI11, MSI1010: _MSI.MSI1010, MSI1110: _MSI.MSI1110,\r\n\tpharmacode: _pharmacode.pharmacode,\r\n\tcodabar: _codabar.codabar,\r\n\tGenericBarcode: _GenericBarcode.GenericBarcode\r\n};", "\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n  value: true\r\n});\r\n\r\nvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\r\n\r\nexports.default = function (old, replaceObj) {\r\n  return _extends({}, old, replaceObj);\r\n};", "\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\nexports.default = linearizeEncodings;\r\n\r\n// Encodings can be nestled like [[1-1, 1-2], 2, [3-1, 3-2]\r\n// Convert to [1-1, 1-2, 2, 3-1, 3-2]\r\n\r\nfunction linearizeEncodings(encodings) {\r\n\tvar linearEncodings = [];\r\n\tfunction nextLevel(encoded) {\r\n\t\tif (Array.isArray(encoded)) {\r\n\t\t\tfor (var i = 0; i < encoded.length; i++) {\r\n\t\t\t\tnextLevel(encoded[i]);\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tencoded.text = encoded.text || \"\";\r\n\t\t\tencoded.data = encoded.data || \"\";\r\n\t\t\tlinearEncodings.push(encoded);\r\n\t\t}\r\n\t}\r\n\tnextLevel(encodings);\r\n\r\n\treturn linearEncodings;\r\n}", "\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\nexports.default = fixOptions;\r\n\r\n\r\nfunction fixOptions(options) {\r\n\t// Fix the margins\r\n\toptions.marginTop = options.marginTop || options.margin;\r\n\toptions.marginBottom = options.marginBottom || options.margin;\r\n\toptions.marginRight = options.marginRight || options.margin;\r\n\toptions.marginLeft = options.marginLeft || options.margin;\r\n\r\n\treturn options;\r\n}", "\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\nexports.default = optionsFromStrings;\r\n\r\n// Convert string to integers/booleans where it should be\r\n\r\nfunction optionsFromStrings(options) {\r\n\tvar intOptions = [\"width\", \"height\", \"textMargin\", \"fontSize\", \"margin\", \"marginTop\", \"marginBottom\", \"marginLeft\", \"marginRight\"];\r\n\r\n\tfor (var intOption in intOptions) {\r\n\t\tif (intOptions.hasOwnProperty(intOption)) {\r\n\t\t\tintOption = intOptions[intOption];\r\n\t\t\tif (typeof options[intOption] === \"string\") {\r\n\t\t\t\toptions[intOption] = parseInt(options[intOption], 10);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tif (typeof options[\"displayValue\"] === \"string\") {\r\n\t\toptions[\"displayValue\"] = options[\"displayValue\"] != \"false\";\r\n\t}\r\n\r\n\treturn options;\r\n}", "\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\nvar defaults = {\r\n\twidth: 2,\r\n\theight: 100,\r\n\tformat: \"auto\",\r\n\tdisplayValue: true,\r\n\tfontOptions: \"\",\r\n\tfont: \"monospace\",\r\n\ttext: undefined,\r\n\ttextAlign: \"center\",\r\n\ttextPosition: \"bottom\",\r\n\ttextMargin: 2,\r\n\tfontSize: 20,\r\n\tbackground: \"#ffffff\",\r\n\tlineColor: \"#000000\",\r\n\tmargin: 10,\r\n\tmarginTop: undefined,\r\n\tmarginBottom: undefined,\r\n\tmarginLeft: undefined,\r\n\tmarginRight: undefined,\r\n\tvalid: function valid() {}\r\n};\r\n\r\nexports.default = defaults;", "\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _optionsFromStrings = require(\"./optionsFromStrings.js\");\r\n\r\nvar _optionsFromStrings2 = _interopRequireDefault(_optionsFromStrings);\r\n\r\nvar _defaults = require(\"../options/defaults.js\");\r\n\r\nvar _defaults2 = _interopRequireDefault(_defaults);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction getOptionsFromElement(element) {\r\n\tvar options = {};\r\n\tfor (var property in _defaults2.default) {\r\n\t\tif (_defaults2.default.hasOwnProperty(property)) {\r\n\t\t\t// jsbarcode-*\r\n\t\t\tif (element.hasAttribute(\"jsbarcode-\" + property.toLowerCase())) {\r\n\t\t\t\toptions[property] = element.getAttribute(\"jsbarcode-\" + property.toLowerCase());\r\n\t\t\t}\r\n\r\n\t\t\t// data-*\r\n\t\t\tif (element.hasAttribute(\"data-\" + property.toLowerCase())) {\r\n\t\t\t\toptions[property] = element.getAttribute(\"data-\" + property.toLowerCase());\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\toptions[\"value\"] = element.getAttribute(\"jsbarcode-value\") || element.getAttribute(\"data-value\");\r\n\r\n\t// Since all atributes are string they need to be converted to integers\r\n\toptions = (0, _optionsFromStrings2.default)(options);\r\n\r\n\treturn options;\r\n}\r\n\r\nexports.default = getOptionsFromElement;", "\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\nexports.getTotalWidthOfEncodings = exports.calculateEncodingAttributes = exports.getBarcodePadding = exports.getEncodingHeight = exports.getMaximumHeightOfEncodings = undefined;\r\n\r\nvar _merge = require(\"../help/merge.js\");\r\n\r\nvar _merge2 = _interopRequireDefault(_merge);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction getEncodingHeight(encoding, options) {\r\n\treturn options.height + (options.displayValue && encoding.text.length > 0 ? options.fontSize + options.textMargin : 0) + options.marginTop + options.marginBottom;\r\n}\r\n\r\nfunction getBarcodePadding(textWidth, barcodeWidth, options) {\r\n\tif (options.displayValue && barcodeWidth < textWidth) {\r\n\t\tif (options.textAlign == \"center\") {\r\n\t\t\treturn Math.floor((textWidth - barcodeWidth) / 2);\r\n\t\t} else if (options.textAlign == \"left\") {\r\n\t\t\treturn 0;\r\n\t\t} else if (options.textAlign == \"right\") {\r\n\t\t\treturn Math.floor(textWidth - barcodeWidth);\r\n\t\t}\r\n\t}\r\n\treturn 0;\r\n}\r\n\r\nfunction calculateEncodingAttributes(encodings, barcodeOptions, context) {\r\n\tfor (var i = 0; i < encodings.length; i++) {\r\n\t\tvar encoding = encodings[i];\r\n\t\tvar options = (0, _merge2.default)(barcodeOptions, encoding.options);\r\n\r\n\t\t// Calculate the width of the encoding\r\n\t\tvar textWidth;\r\n\t\tif (options.displayValue) {\r\n\t\t\ttextWidth = messureText(encoding.text, options, context);\r\n\t\t} else {\r\n\t\t\ttextWidth = 0;\r\n\t\t}\r\n\r\n\t\tvar barcodeWidth = encoding.data.length * options.width;\r\n\t\tencoding.width = Math.ceil(Math.max(textWidth, barcodeWidth));\r\n\r\n\t\tencoding.height = getEncodingHeight(encoding, options);\r\n\r\n\t\tencoding.barcodePadding = getBarcodePadding(textWidth, barcodeWidth, options);\r\n\t}\r\n}\r\n\r\nfunction getTotalWidthOfEncodings(encodings) {\r\n\tvar totalWidth = 0;\r\n\tfor (var i = 0; i < encodings.length; i++) {\r\n\t\ttotalWidth += encodings[i].width;\r\n\t}\r\n\treturn totalWidth;\r\n}\r\n\r\nfunction getMaximumHeightOfEncodings(encodings) {\r\n\tvar maxHeight = 0;\r\n\tfor (var i = 0; i < encodings.length; i++) {\r\n\t\tif (encodings[i].height > maxHeight) {\r\n\t\t\tmaxHeight = encodings[i].height;\r\n\t\t}\r\n\t}\r\n\treturn maxHeight;\r\n}\r\n\r\nfunction messureText(string, options, context) {\r\n\tvar ctx;\r\n\r\n\tif (context) {\r\n\t\tctx = context;\r\n\t} else if (typeof document !== \"undefined\") {\r\n\t\tctx = document.createElement(\"canvas\").getContext(\"2d\");\r\n\t} else {\r\n\t\t// If the text cannot be messured we will return 0.\r\n\t\t// This will make some barcode with big text render incorrectly\r\n\t\treturn 0;\r\n\t}\r\n\tctx.font = options.fontOptions + \" \" + options.fontSize + \"px \" + options.font;\r\n\r\n\t// Calculate the width of the encoding\r\n\tvar measureTextResult = ctx.measureText(string);\r\n\tif (!measureTextResult) {\r\n\t\t// Some implementations don't implement measureText and return undefined.\r\n\t\t// If the text cannot be measured we will return 0.\r\n\t\t// This will make some barcode with big text render incorrectly\r\n\t\treturn 0;\r\n\t}\r\n\tvar size = measureTextResult.width;\r\n\treturn size;\r\n}\r\n\r\nexports.getMaximumHeightOfEncodings = getMaximumHeightOfEncodings;\r\nexports.getEncodingHeight = getEncodingHeight;\r\nexports.getBarcodePadding = getBarcodePadding;\r\nexports.calculateEncodingAttributes = calculateEncodingAttributes;\r\nexports.getTotalWidthOfEncodings = getTotalWidthOfEncodings;", "\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nvar _merge = require(\"../help/merge.js\");\r\n\r\nvar _merge2 = _interopRequireDefault(_merge);\r\n\r\nvar _shared = require(\"./shared.js\");\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nvar CanvasRenderer = function () {\r\n\tfunction CanvasRenderer(canvas, encodings, options) {\r\n\t\t_classCallCheck(this, CanvasRenderer);\r\n\r\n\t\tthis.canvas = canvas;\r\n\t\tthis.encodings = encodings;\r\n\t\tthis.options = options;\r\n\t}\r\n\r\n\t_createClass(CanvasRenderer, [{\r\n\t\tkey: \"render\",\r\n\t\tvalue: function render() {\r\n\t\t\t// Abort if the browser does not support HTML5 canvas\r\n\t\t\tif (!this.canvas.getContext) {\r\n\t\t\t\tthrow new Error('The browser does not support canvas.');\r\n\t\t\t}\r\n\r\n\t\t\tthis.prepareCanvas();\r\n\t\t\tfor (var i = 0; i < this.encodings.length; i++) {\r\n\t\t\t\tvar encodingOptions = (0, _merge2.default)(this.options, this.encodings[i].options);\r\n\r\n\t\t\t\tthis.drawCanvasBarcode(encodingOptions, this.encodings[i]);\r\n\t\t\t\tthis.drawCanvasText(encodingOptions, this.encodings[i]);\r\n\r\n\t\t\t\tthis.moveCanvasDrawing(this.encodings[i]);\r\n\t\t\t}\r\n\r\n\t\t\tthis.restoreCanvas();\r\n\t\t}\r\n\t}, {\r\n\t\tkey: \"prepareCanvas\",\r\n\t\tvalue: function prepareCanvas() {\r\n\t\t\t// Get the canvas context\r\n\t\t\tvar ctx = this.canvas.getContext(\"2d\");\r\n\r\n\t\t\tctx.save();\r\n\r\n\t\t\t(0, _shared.calculateEncodingAttributes)(this.encodings, this.options, ctx);\r\n\t\t\tvar totalWidth = (0, _shared.getTotalWidthOfEncodings)(this.encodings);\r\n\t\t\tvar maxHeight = (0, _shared.getMaximumHeightOfEncodings)(this.encodings);\r\n\r\n\t\t\tthis.canvas.width = totalWidth + this.options.marginLeft + this.options.marginRight;\r\n\r\n\t\t\tthis.canvas.height = maxHeight;\r\n\r\n\t\t\t// Paint the canvas\r\n\t\t\tctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\r\n\t\t\tif (this.options.background) {\r\n\t\t\t\tctx.fillStyle = this.options.background;\r\n\t\t\t\tctx.fillRect(0, 0, this.canvas.width, this.canvas.height);\r\n\t\t\t}\r\n\r\n\t\t\tctx.translate(this.options.marginLeft, 0);\r\n\t\t}\r\n\t}, {\r\n\t\tkey: \"drawCanvasBarcode\",\r\n\t\tvalue: function drawCanvasBarcode(options, encoding) {\r\n\t\t\t// Get the canvas context\r\n\t\t\tvar ctx = this.canvas.getContext(\"2d\");\r\n\r\n\t\t\tvar binary = encoding.data;\r\n\r\n\t\t\t// Creates the barcode out of the encoded binary\r\n\t\t\tvar yFrom;\r\n\t\t\tif (options.textPosition == \"top\") {\r\n\t\t\t\tyFrom = options.marginTop + options.fontSize + options.textMargin;\r\n\t\t\t} else {\r\n\t\t\t\tyFrom = options.marginTop;\r\n\t\t\t}\r\n\r\n\t\t\tctx.fillStyle = options.lineColor;\r\n\r\n\t\t\tfor (var b = 0; b < binary.length; b++) {\r\n\t\t\t\tvar x = b * options.width + encoding.barcodePadding;\r\n\r\n\t\t\t\tif (binary[b] === \"1\") {\r\n\t\t\t\t\tctx.fillRect(x, yFrom, options.width, options.height);\r\n\t\t\t\t} else if (binary[b]) {\r\n\t\t\t\t\tctx.fillRect(x, yFrom, options.width, options.height * binary[b]);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}, {\r\n\t\tkey: \"drawCanvasText\",\r\n\t\tvalue: function drawCanvasText(options, encoding) {\r\n\t\t\t// Get the canvas context\r\n\t\t\tvar ctx = this.canvas.getContext(\"2d\");\r\n\r\n\t\t\tvar font = options.fontOptions + \" \" + options.fontSize + \"px \" + options.font;\r\n\r\n\t\t\t// Draw the text if displayValue is set\r\n\t\t\tif (options.displayValue) {\r\n\t\t\t\tvar x, y;\r\n\r\n\t\t\t\tif (options.textPosition == \"top\") {\r\n\t\t\t\t\ty = options.marginTop + options.fontSize - options.textMargin;\r\n\t\t\t\t} else {\r\n\t\t\t\t\ty = options.height + options.textMargin + options.marginTop + options.fontSize;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tctx.font = font;\r\n\r\n\t\t\t\t// Draw the text in the correct X depending on the textAlign option\r\n\t\t\t\tif (options.textAlign == \"left\" || encoding.barcodePadding > 0) {\r\n\t\t\t\t\tx = 0;\r\n\t\t\t\t\tctx.textAlign = 'left';\r\n\t\t\t\t} else if (options.textAlign == \"right\") {\r\n\t\t\t\t\tx = encoding.width - 1;\r\n\t\t\t\t\tctx.textAlign = 'right';\r\n\t\t\t\t}\r\n\t\t\t\t// In all other cases, center the text\r\n\t\t\t\telse {\r\n\t\t\t\t\t\tx = encoding.width / 2;\r\n\t\t\t\t\t\tctx.textAlign = 'center';\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\tctx.fillText(encoding.text, x, y);\r\n\t\t\t}\r\n\t\t}\r\n\t}, {\r\n\t\tkey: \"moveCanvasDrawing\",\r\n\t\tvalue: function moveCanvasDrawing(encoding) {\r\n\t\t\tvar ctx = this.canvas.getContext(\"2d\");\r\n\r\n\t\t\tctx.translate(encoding.width, 0);\r\n\t\t}\r\n\t}, {\r\n\t\tkey: \"restoreCanvas\",\r\n\t\tvalue: function restoreCanvas() {\r\n\t\t\t// Get the canvas context\r\n\t\t\tvar ctx = this.canvas.getContext(\"2d\");\r\n\r\n\t\t\tctx.restore();\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn CanvasRenderer;\r\n}();\r\n\r\nexports.default = CanvasRenderer;", "\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nvar _merge = require(\"../help/merge.js\");\r\n\r\nvar _merge2 = _interopRequireDefault(_merge);\r\n\r\nvar _shared = require(\"./shared.js\");\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nvar svgns = \"http://www.w3.org/2000/svg\";\r\n\r\nvar SVGRenderer = function () {\r\n\tfunction SVGRenderer(svg, encodings, options) {\r\n\t\t_classCallCheck(this, SVGRenderer);\r\n\r\n\t\tthis.svg = svg;\r\n\t\tthis.encodings = encodings;\r\n\t\tthis.options = options;\r\n\t\tthis.document = options.xmlDocument || document;\r\n\t}\r\n\r\n\t_createClass(SVGRenderer, [{\r\n\t\tkey: \"render\",\r\n\t\tvalue: function render() {\r\n\t\t\tvar currentX = this.options.marginLeft;\r\n\r\n\t\t\tthis.prepareSVG();\r\n\t\t\tfor (var i = 0; i < this.encodings.length; i++) {\r\n\t\t\t\tvar encoding = this.encodings[i];\r\n\t\t\t\tvar encodingOptions = (0, _merge2.default)(this.options, encoding.options);\r\n\r\n\t\t\t\tvar group = this.createGroup(currentX, encodingOptions.marginTop, this.svg);\r\n\r\n\t\t\t\tthis.setGroupOptions(group, encodingOptions);\r\n\r\n\t\t\t\tthis.drawSvgBarcode(group, encodingOptions, encoding);\r\n\t\t\t\tthis.drawSVGText(group, encodingOptions, encoding);\r\n\r\n\t\t\t\tcurrentX += encoding.width;\r\n\t\t\t}\r\n\t\t}\r\n\t}, {\r\n\t\tkey: \"prepareSVG\",\r\n\t\tvalue: function prepareSVG() {\r\n\t\t\t// Clear the SVG\r\n\t\t\twhile (this.svg.firstChild) {\r\n\t\t\t\tthis.svg.removeChild(this.svg.firstChild);\r\n\t\t\t}\r\n\r\n\t\t\t(0, _shared.calculateEncodingAttributes)(this.encodings, this.options);\r\n\t\t\tvar totalWidth = (0, _shared.getTotalWidthOfEncodings)(this.encodings);\r\n\t\t\tvar maxHeight = (0, _shared.getMaximumHeightOfEncodings)(this.encodings);\r\n\r\n\t\t\tvar width = totalWidth + this.options.marginLeft + this.options.marginRight;\r\n\t\t\tthis.setSvgAttributes(width, maxHeight);\r\n\r\n\t\t\tif (this.options.background) {\r\n\t\t\t\tthis.drawRect(0, 0, width, maxHeight, this.svg).setAttribute(\"style\", \"fill:\" + this.options.background + \";\");\r\n\t\t\t}\r\n\t\t}\r\n\t}, {\r\n\t\tkey: \"drawSvgBarcode\",\r\n\t\tvalue: function drawSvgBarcode(parent, options, encoding) {\r\n\t\t\tvar binary = encoding.data;\r\n\r\n\t\t\t// Creates the barcode out of the encoded binary\r\n\t\t\tvar yFrom;\r\n\t\t\tif (options.textPosition == \"top\") {\r\n\t\t\t\tyFrom = options.fontSize + options.textMargin;\r\n\t\t\t} else {\r\n\t\t\t\tyFrom = 0;\r\n\t\t\t}\r\n\r\n\t\t\tvar barWidth = 0;\r\n\t\t\tvar x = 0;\r\n\t\t\tfor (var b = 0; b < binary.length; b++) {\r\n\t\t\t\tx = b * options.width + encoding.barcodePadding;\r\n\r\n\t\t\t\tif (binary[b] === \"1\") {\r\n\t\t\t\t\tbarWidth++;\r\n\t\t\t\t} else if (barWidth > 0) {\r\n\t\t\t\t\tthis.drawRect(x - options.width * barWidth, yFrom, options.width * barWidth, options.height, parent);\r\n\t\t\t\t\tbarWidth = 0;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// Last draw is needed since the barcode ends with 1\r\n\t\t\tif (barWidth > 0) {\r\n\t\t\t\tthis.drawRect(x - options.width * (barWidth - 1), yFrom, options.width * barWidth, options.height, parent);\r\n\t\t\t}\r\n\t\t}\r\n\t}, {\r\n\t\tkey: \"drawSVGText\",\r\n\t\tvalue: function drawSVGText(parent, options, encoding) {\r\n\t\t\tvar textElem = this.document.createElementNS(svgns, 'text');\r\n\r\n\t\t\t// Draw the text if displayValue is set\r\n\t\t\tif (options.displayValue) {\r\n\t\t\t\tvar x, y;\r\n\r\n\t\t\t\ttextElem.setAttribute(\"style\", \"font:\" + options.fontOptions + \" \" + options.fontSize + \"px \" + options.font);\r\n\r\n\t\t\t\tif (options.textPosition == \"top\") {\r\n\t\t\t\t\ty = options.fontSize - options.textMargin;\r\n\t\t\t\t} else {\r\n\t\t\t\t\ty = options.height + options.textMargin + options.fontSize;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Draw the text in the correct X depending on the textAlign option\r\n\t\t\t\tif (options.textAlign == \"left\" || encoding.barcodePadding > 0) {\r\n\t\t\t\t\tx = 0;\r\n\t\t\t\t\ttextElem.setAttribute(\"text-anchor\", \"start\");\r\n\t\t\t\t} else if (options.textAlign == \"right\") {\r\n\t\t\t\t\tx = encoding.width - 1;\r\n\t\t\t\t\ttextElem.setAttribute(\"text-anchor\", \"end\");\r\n\t\t\t\t}\r\n\t\t\t\t// In all other cases, center the text\r\n\t\t\t\telse {\r\n\t\t\t\t\t\tx = encoding.width / 2;\r\n\t\t\t\t\t\ttextElem.setAttribute(\"text-anchor\", \"middle\");\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\ttextElem.setAttribute(\"x\", x);\r\n\t\t\t\ttextElem.setAttribute(\"y\", y);\r\n\r\n\t\t\t\ttextElem.appendChild(this.document.createTextNode(encoding.text));\r\n\r\n\t\t\t\tparent.appendChild(textElem);\r\n\t\t\t}\r\n\t\t}\r\n\t}, {\r\n\t\tkey: \"setSvgAttributes\",\r\n\t\tvalue: function setSvgAttributes(width, height) {\r\n\t\t\tvar svg = this.svg;\r\n\t\t\tsvg.setAttribute(\"width\", width + \"px\");\r\n\t\t\tsvg.setAttribute(\"height\", height + \"px\");\r\n\t\t\tsvg.setAttribute(\"x\", \"0px\");\r\n\t\t\tsvg.setAttribute(\"y\", \"0px\");\r\n\t\t\tsvg.setAttribute(\"viewBox\", \"0 0 \" + width + \" \" + height);\r\n\r\n\t\t\tsvg.setAttribute(\"xmlns\", svgns);\r\n\t\t\tsvg.setAttribute(\"version\", \"1.1\");\r\n\r\n\t\t\tsvg.setAttribute(\"style\", \"transform: translate(0,0)\");\r\n\t\t}\r\n\t}, {\r\n\t\tkey: \"createGroup\",\r\n\t\tvalue: function createGroup(x, y, parent) {\r\n\t\t\tvar group = this.document.createElementNS(svgns, 'g');\r\n\t\t\tgroup.setAttribute(\"transform\", \"translate(\" + x + \", \" + y + \")\");\r\n\r\n\t\t\tparent.appendChild(group);\r\n\r\n\t\t\treturn group;\r\n\t\t}\r\n\t}, {\r\n\t\tkey: \"setGroupOptions\",\r\n\t\tvalue: function setGroupOptions(group, options) {\r\n\t\t\tgroup.setAttribute(\"style\", \"fill:\" + options.lineColor + \";\");\r\n\t\t}\r\n\t}, {\r\n\t\tkey: \"drawRect\",\r\n\t\tvalue: function drawRect(x, y, width, height, parent) {\r\n\t\t\tvar rect = this.document.createElementNS(svgns, 'rect');\r\n\r\n\t\t\trect.setAttribute(\"x\", x);\r\n\t\t\trect.setAttribute(\"y\", y);\r\n\t\t\trect.setAttribute(\"width\", width);\r\n\t\t\trect.setAttribute(\"height\", height);\r\n\r\n\t\t\tparent.appendChild(rect);\r\n\r\n\t\t\treturn rect;\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn SVGRenderer;\r\n}();\r\n\r\nexports.default = SVGRenderer;", "\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nvar ObjectRenderer = function () {\r\n\tfunction ObjectRenderer(object, encodings, options) {\r\n\t\t_classCallCheck(this, ObjectRenderer);\r\n\r\n\t\tthis.object = object;\r\n\t\tthis.encodings = encodings;\r\n\t\tthis.options = options;\r\n\t}\r\n\r\n\t_createClass(ObjectRenderer, [{\r\n\t\tkey: \"render\",\r\n\t\tvalue: function render() {\r\n\t\t\tthis.object.encodings = this.encodings;\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn ObjectRenderer;\r\n}();\r\n\r\nexports.default = ObjectRenderer;", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n  value: true\r\n});\r\n\r\nvar _canvas = require('./canvas.js');\r\n\r\nvar _canvas2 = _interopRequireDefault(_canvas);\r\n\r\nvar _svg = require('./svg.js');\r\n\r\nvar _svg2 = _interopRequireDefault(_svg);\r\n\r\nvar _object = require('./object.js');\r\n\r\nvar _object2 = _interopRequireDefault(_object);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\nexports.default = { CanvasRenderer: _canvas2.default, SVGRenderer: _svg2.default, ObjectRenderer: _object2.default };", "'use strict';\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\r\n\r\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\r\n\r\nvar InvalidInputException = function (_Error) {\r\n\t_inherits(InvalidInputException, _Error);\r\n\r\n\tfunction InvalidInputException(symbology, input) {\r\n\t\t_classCallCheck(this, InvalidInputException);\r\n\r\n\t\tvar _this = _possibleConstructorReturn(this, (InvalidInputException.__proto__ || Object.getPrototypeOf(InvalidInputException)).call(this));\r\n\r\n\t\t_this.name = \"InvalidInputException\";\r\n\r\n\t\t_this.symbology = symbology;\r\n\t\t_this.input = input;\r\n\r\n\t\t_this.message = '\"' + _this.input + '\" is not a valid input for ' + _this.symbology;\r\n\t\treturn _this;\r\n\t}\r\n\r\n\treturn InvalidInputException;\r\n}(Error);\r\n\r\nvar InvalidElementException = function (_Error2) {\r\n\t_inherits(InvalidElementException, _Error2);\r\n\r\n\tfunction InvalidElementException() {\r\n\t\t_classCallCheck(this, InvalidElementException);\r\n\r\n\t\tvar _this2 = _possibleConstructorReturn(this, (InvalidElementException.__proto__ || Object.getPrototypeOf(InvalidElementException)).call(this));\r\n\r\n\t\t_this2.name = \"InvalidElementException\";\r\n\t\t_this2.message = \"Not supported type to render on\";\r\n\t\treturn _this2;\r\n\t}\r\n\r\n\treturn InvalidElementException;\r\n}(Error);\r\n\r\nvar NoElementException = function (_Error3) {\r\n\t_inherits(NoElementException, _Error3);\r\n\r\n\tfunction NoElementException() {\r\n\t\t_classCallCheck(this, NoElementException);\r\n\r\n\t\tvar _this3 = _possibleConstructorReturn(this, (NoElementException.__proto__ || Object.getPrototypeOf(NoElementException)).call(this));\r\n\r\n\t\t_this3.name = \"NoElementException\";\r\n\t\t_this3.message = \"No element to render on.\";\r\n\t\treturn _this3;\r\n\t}\r\n\r\n\treturn NoElementException;\r\n}(Error);\r\n\r\nexports.InvalidInputException = InvalidInputException;\r\nexports.InvalidElementException = InvalidElementException;\r\nexports.NoElementException = NoElementException;", "\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; /* global HTMLImageElement */\r\n/* global HTMLCanvasElement */\r\n/* global SVGElement */\r\n\r\nvar _getOptionsFromElement = require(\"./getOptionsFromElement.js\");\r\n\r\nvar _getOptionsFromElement2 = _interopRequireDefault(_getOptionsFromElement);\r\n\r\nvar _renderers = require(\"../renderers\");\r\n\r\nvar _renderers2 = _interopRequireDefault(_renderers);\r\n\r\nvar _exceptions = require(\"../exceptions/exceptions.js\");\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\n// Takes an element and returns an object with information about how\r\n// it should be rendered\r\n// This could also return an array with these objects\r\n// {\r\n//   element: The element that the renderer should draw on\r\n//   renderer: The name of the renderer\r\n//   afterRender (optional): If something has to done after the renderer\r\n//     completed, calls afterRender (function)\r\n//   options (optional): Options that can be defined in the element\r\n// }\r\n\r\nfunction getRenderProperties(element) {\r\n\t// If the element is a string, query select call again\r\n\tif (typeof element === \"string\") {\r\n\t\treturn querySelectedRenderProperties(element);\r\n\t}\r\n\t// If element is array. Recursivly call with every object in the array\r\n\telse if (Array.isArray(element)) {\r\n\t\t\tvar returnArray = [];\r\n\t\t\tfor (var i = 0; i < element.length; i++) {\r\n\t\t\t\treturnArray.push(getRenderProperties(element[i]));\r\n\t\t\t}\r\n\t\t\treturn returnArray;\r\n\t\t}\r\n\t\t// If element, render on canvas and set the uri as src\r\n\t\telse if (typeof HTMLCanvasElement !== 'undefined' && element instanceof HTMLImageElement) {\r\n\t\t\t\treturn newCanvasRenderProperties(element);\r\n\t\t\t}\r\n\t\t\t// If SVG\r\n\t\t\telse if (element && element.nodeName && element.nodeName.toLowerCase() === 'svg' || typeof SVGElement !== 'undefined' && element instanceof SVGElement) {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\telement: element,\r\n\t\t\t\t\t\toptions: (0, _getOptionsFromElement2.default)(element),\r\n\t\t\t\t\t\trenderer: _renderers2.default.SVGRenderer\r\n\t\t\t\t\t};\r\n\t\t\t\t}\r\n\t\t\t\t// If canvas (in browser)\r\n\t\t\t\telse if (typeof HTMLCanvasElement !== 'undefined' && element instanceof HTMLCanvasElement) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\telement: element,\r\n\t\t\t\t\t\t\toptions: (0, _getOptionsFromElement2.default)(element),\r\n\t\t\t\t\t\t\trenderer: _renderers2.default.CanvasRenderer\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// If canvas (in node)\r\n\t\t\t\t\telse if (element && element.getContext) {\r\n\t\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\telement: element,\r\n\t\t\t\t\t\t\t\trenderer: _renderers2.default.CanvasRenderer\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t} else if (element && (typeof element === \"undefined\" ? \"undefined\" : _typeof(element)) === 'object' && !element.nodeName) {\r\n\t\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\telement: element,\r\n\t\t\t\t\t\t\t\trenderer: _renderers2.default.ObjectRenderer\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthrow new _exceptions.InvalidElementException();\r\n\t\t\t\t\t\t}\r\n}\r\n\r\nfunction querySelectedRenderProperties(string) {\r\n\tvar selector = document.querySelectorAll(string);\r\n\tif (selector.length === 0) {\r\n\t\treturn undefined;\r\n\t} else {\r\n\t\tvar returnArray = [];\r\n\t\tfor (var i = 0; i < selector.length; i++) {\r\n\t\t\treturnArray.push(getRenderProperties(selector[i]));\r\n\t\t}\r\n\t\treturn returnArray;\r\n\t}\r\n}\r\n\r\nfunction newCanvasRenderProperties(imgElement) {\r\n\tvar canvas = document.createElement('canvas');\r\n\treturn {\r\n\t\telement: canvas,\r\n\t\toptions: (0, _getOptionsFromElement2.default)(imgElement),\r\n\t\trenderer: _renderers2.default.CanvasRenderer,\r\n\t\tafterRender: function afterRender() {\r\n\t\t\timgElement.setAttribute(\"src\", canvas.toDataURL());\r\n\t\t}\r\n\t};\r\n}\r\n\r\nexports.default = getRenderProperties;", "\"use strict\";\r\n\r\nObject.defineProperty(exports, \"__esModule\", {\r\n\tvalue: true\r\n});\r\n\r\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\r\n\r\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\r\n\r\n/*eslint no-console: 0 */\r\n\r\nvar ErrorHandler = function () {\r\n\tfunction ErrorHandler(api) {\r\n\t\t_classCallCheck(this, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>);\r\n\r\n\t\tthis.api = api;\r\n\t}\r\n\r\n\t_createClass(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [{\r\n\t\tkey: \"handleCatch\",\r\n\t\tvalue: function handleCatch(e) {\r\n\t\t\t// If babel supported extending of Error in a correct way instanceof would be used here\r\n\t\t\tif (e.name === \"InvalidInputException\") {\r\n\t\t\t\tif (this.api._options.valid !== this.api._defaults.valid) {\r\n\t\t\t\t\tthis.api._options.valid(false);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthrow e.message;\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tthrow e;\r\n\t\t\t}\r\n\r\n\t\t\tthis.api.render = function () {};\r\n\t\t}\r\n\t}, {\r\n\t\tkey: \"wrapBarcodeCall\",\r\n\t\tvalue: function wrapBarcodeCall(func) {\r\n\t\t\ttry {\r\n\t\t\t\tvar result = func.apply(undefined, arguments);\r\n\t\t\t\tthis.api._options.valid(true);\r\n\t\t\t\treturn result;\r\n\t\t\t} catch (e) {\r\n\t\t\t\tthis.handleCatch(e);\r\n\r\n\t\t\t\treturn this.api;\r\n\t\t\t}\r\n\t\t}\r\n\t}]);\r\n\r\n\treturn ErrorHandler;\r\n}();\r\n\r\nexports.default = ErrorHandler;", "'use strict';\r\n\r\nvar _barcodes = require('./barcodes/');\r\n\r\nvar _barcodes2 = _interopRequireDefault(_barcodes);\r\n\r\nvar _merge = require('./help/merge.js');\r\n\r\nvar _merge2 = _interopRequireDefault(_merge);\r\n\r\nvar _linearizeEncodings = require('./help/linearizeEncodings.js');\r\n\r\nvar _linearizeEncodings2 = _interopRequireDefault(_linearizeEncodings);\r\n\r\nvar _fixOptions = require('./help/fixOptions.js');\r\n\r\nvar _fixOptions2 = _interopRequireDefault(_fixOptions);\r\n\r\nvar _getRenderProperties = require('./help/getRenderProperties.js');\r\n\r\nvar _getRenderProperties2 = _interopRequireDefault(_getRenderProperties);\r\n\r\nvar _optionsFromStrings = require('./help/optionsFromStrings.js');\r\n\r\nvar _optionsFromStrings2 = _interopRequireDefault(_optionsFromStrings);\r\n\r\nvar _ErrorHandler = require('./exceptions/ErrorHandler.js');\r\n\r\nvar _ErrorHandler2 = _interopRequireDefault(_ErrorHandler);\r\n\r\nvar _exceptions = require('./exceptions/exceptions.js');\r\n\r\nvar _defaults = require('./options/defaults.js');\r\n\r\nvar _defaults2 = _interopRequireDefault(_defaults);\r\n\r\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\r\n\r\n// The protype of the object returned from the JsBarcode() call\r\n\r\n\r\n// Help functions\r\nvar API = function API() {};\r\n\r\n// The first call of the library API\r\n// Will return an object with all barcodes calls and the data that is used\r\n// by the renderers\r\n\r\n\r\n// Default values\r\n\r\n\r\n// Exceptions\r\n// Import all the barcodes\r\nvar JsBarcode = function JsBarcode(element, text, options) {\r\n\tvar api = new API();\r\n\r\n\tif (typeof element === \"undefined\") {\r\n\t\tthrow Error(\"No element to render on was provided.\");\r\n\t}\r\n\r\n\t// Variables that will be pased through the API calls\r\n\tapi._renderProperties = (0, _getRenderProperties2.default)(element);\r\n\tapi._encodings = [];\r\n\tapi._options = _defaults2.default;\r\n\tapi._errorHandler = new _ErrorHandler2.default(api);\r\n\r\n\t// If text is set, use the simple syntax (render the barcode directly)\r\n\tif (typeof text !== \"undefined\") {\r\n\t\toptions = options || {};\r\n\r\n\t\tif (!options.format) {\r\n\t\t\toptions.format = autoSelectBarcode();\r\n\t\t}\r\n\r\n\t\tapi.options(options)[options.format](text, options).render();\r\n\t}\r\n\r\n\treturn api;\r\n};\r\n\r\n// To make tests work TODO: remove\r\nJsBarcode.getModule = function (name) {\r\n\treturn _barcodes2.default[name];\r\n};\r\n\r\n// Register all barcodes\r\nfor (var name in _barcodes2.default) {\r\n\tif (_barcodes2.default.hasOwnProperty(name)) {\r\n\t\t// Security check if the propery is a prototype property\r\n\t\tregisterBarcode(_barcodes2.default, name);\r\n\t}\r\n}\r\nfunction registerBarcode(barcodes, name) {\r\n\tAPI.prototype[name] = API.prototype[name.toUpperCase()] = API.prototype[name.toLowerCase()] = function (text, options) {\r\n\t\tvar api = this;\r\n\t\treturn api._errorHandler.wrapBarcodeCall(function () {\r\n\t\t\t// Ensure text is options.text\r\n\t\t\toptions.text = typeof options.text === 'undefined' ? undefined : '' + options.text;\r\n\r\n\t\t\tvar newOptions = (0, _merge2.default)(api._options, options);\r\n\t\t\tnewOptions = (0, _optionsFromStrings2.default)(newOptions);\r\n\t\t\tvar Encoder = barcodes[name];\r\n\t\t\tvar encoded = encode(text, Encoder, newOptions);\r\n\t\t\tapi._encodings.push(encoded);\r\n\r\n\t\t\treturn api;\r\n\t\t});\r\n\t};\r\n}\r\n\r\n// encode() handles the Encoder call and builds the binary string to be rendered\r\nfunction encode(text, Encoder, options) {\r\n\t// Ensure that text is a string\r\n\ttext = \"\" + text;\r\n\r\n\tvar encoder = new Encoder(text, options);\r\n\r\n\t// If the input is not valid for the encoder, throw error.\r\n\t// If the valid callback option is set, call it instead of throwing error\r\n\tif (!encoder.valid()) {\r\n\t\tthrow new _exceptions.InvalidInputException(encoder.constructor.name, text);\r\n\t}\r\n\r\n\t// Make a request for the binary data (and other infromation) that should be rendered\r\n\tvar encoded = encoder.encode();\r\n\r\n\t// Encodings can be nestled like [[1-1, 1-2], 2, [3-1, 3-2]\r\n\t// Convert to [1-1, 1-2, 2, 3-1, 3-2]\r\n\tencoded = (0, _linearizeEncodings2.default)(encoded);\r\n\r\n\t// Merge\r\n\tfor (var i = 0; i < encoded.length; i++) {\r\n\t\tencoded[i].options = (0, _merge2.default)(options, encoded[i].options);\r\n\t}\r\n\r\n\treturn encoded;\r\n}\r\n\r\nfunction autoSelectBarcode() {\r\n\t// If CODE128 exists. Use it\r\n\tif (_barcodes2.default[\"CODE128\"]) {\r\n\t\treturn \"CODE128\";\r\n\t}\r\n\r\n\t// Else, take the first (probably only) barcode\r\n\treturn Object.keys(_barcodes2.default)[0];\r\n}\r\n\r\n// Sets global encoder options\r\n// Added to the api by the JsBarcode function\r\nAPI.prototype.options = function (options) {\r\n\tthis._options = (0, _merge2.default)(this._options, options);\r\n\treturn this;\r\n};\r\n\r\n// Will create a blank space (usually in between barcodes)\r\nAPI.prototype.blank = function (size) {\r\n\tvar zeroes = new Array(size + 1).join(\"0\");\r\n\tthis._encodings.push({ data: zeroes });\r\n\treturn this;\r\n};\r\n\r\n// Initialize JsBarcode on all HTML elements defined.\r\nAPI.prototype.init = function () {\r\n\t// Should do nothing if no elements where found\r\n\tif (!this._renderProperties) {\r\n\t\treturn;\r\n\t}\r\n\r\n\t// Make sure renderProperies is an array\r\n\tif (!Array.isArray(this._renderProperties)) {\r\n\t\tthis._renderProperties = [this._renderProperties];\r\n\t}\r\n\r\n\tvar renderProperty;\r\n\tfor (var i in this._renderProperties) {\r\n\t\trenderProperty = this._renderProperties[i];\r\n\t\tvar options = (0, _merge2.default)(this._options, renderProperty.options);\r\n\r\n\t\tif (options.format == \"auto\") {\r\n\t\t\toptions.format = autoSelectBarcode();\r\n\t\t}\r\n\r\n\t\tthis._errorHandler.wrapBarcodeCall(function () {\r\n\t\t\tvar text = options.value;\r\n\t\t\tvar Encoder = _barcodes2.default[options.format.toUpperCase()];\r\n\t\t\tvar encoded = encode(text, Encoder, options);\r\n\r\n\t\t\trender(renderProperty, encoded, options);\r\n\t\t});\r\n\t}\r\n};\r\n\r\n// The render API call. Calls the real render function.\r\nAPI.prototype.render = function () {\r\n\tif (!this._renderProperties) {\r\n\t\tthrow new _exceptions.NoElementException();\r\n\t}\r\n\r\n\tif (Array.isArray(this._renderProperties)) {\r\n\t\tfor (var i = 0; i < this._renderProperties.length; i++) {\r\n\t\t\trender(this._renderProperties[i], this._encodings, this._options);\r\n\t\t}\r\n\t} else {\r\n\t\trender(this._renderProperties, this._encodings, this._options);\r\n\t}\r\n\r\n\treturn this;\r\n};\r\n\r\nAPI.prototype._defaults = _defaults2.default;\r\n\r\n// Prepares the encodings and calls the renderer\r\nfunction render(renderProperties, encodings, options) {\r\n\tencodings = (0, _linearizeEncodings2.default)(encodings);\r\n\r\n\tfor (var i = 0; i < encodings.length; i++) {\r\n\t\tencodings[i].options = (0, _merge2.default)(options, encodings[i].options);\r\n\t\t(0, _fixOptions2.default)(encodings[i].options);\r\n\t}\r\n\r\n\t(0, _fixOptions2.default)(options);\r\n\r\n\tvar Renderer = renderProperties.renderer;\r\n\tvar renderer = new Renderer(renderProperties.element, encodings, options);\r\n\trenderer.render();\r\n\r\n\tif (renderProperties.afterRender) {\r\n\t\trenderProperties.afterRender();\r\n\t}\r\n}\r\n\r\n// Export to browser\r\nif (typeof window !== \"undefined\") {\r\n\twindow.JsBarcode = JsBarcode;\r\n}\r\n\r\n// Export to jQuery\r\n/*global jQuery */\r\nif (typeof jQuery !== 'undefined') {\r\n\tjQuery.fn.JsBarcode = function (content, options) {\r\n\t\tvar elementArray = [];\r\n\t\tjQuery(this).each(function () {\r\n\t\t\telementArray.push(this);\r\n\t\t});\r\n\t\treturn JsBarcode(elementArray, content, options);\r\n\t};\r\n}\r\n\r\n// Export to commonJS\r\nmodule.exports = JsBarcode;"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,QAAI,UAAU,SAASA,SAAQ,MAAM,SAAS;AAC7C,sBAAgB,MAAMA,QAAO;AAE7B,WAAK,OAAO;AACZ,WAAK,OAAO,QAAQ,QAAQ;AAC5B,WAAK,UAAU;AAAA,IAChB;AAEA,YAAQ,UAAU;AAAA;AAAA;;;AChBlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,YAAQ,SAAS;AAEjB,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAG7e,QAAI,SAAS,SAAU,UAAU;AAChC,gBAAUC,SAAQ,QAAQ;AAE1B,eAASA,QAAO,MAAM,SAAS;AAC9B,wBAAgB,MAAMA,OAAM;AAE5B,eAAO,KAAK,YAAY;AAGxB,YAAI,QAAQ,OAAO;AAClB,kBAAQ,aAAa,cAAc,IAAI,CAAC;AAAA,QACzC;AAEA,eAAO,2BAA2B,OAAOA,QAAO,aAAa,OAAO,eAAeA,OAAM,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,MACtH;AAEA,mBAAaA,SAAQ,CAAC;AAAA,QACrB,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AAExB,cAAI,SAAS,YAAY,GAAG;AAG5B,mBAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AAC1C,sBAAU,YAAY,KAAK,KAAK,CAAC,CAAC,IAAI;AAAA,UACvC;AAGA,oBAAU,YAAY,GAAG;AAEzB,iBAAO;AAAA,YACN,MAAM;AAAA,YACN,MAAM,KAAK;AAAA,UACZ;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,KAAK,OAAO,2BAA2B,MAAM;AAAA,QAC1D;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,UAAU,OAAO;AAKnB,QAAI,aAAa,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAI5O,QAAI,YAAY,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAInU,aAAS,YAAY,WAAW;AAC/B,aAAO,UAAU,eAAe,SAAS,CAAC;AAAA,IAC3C;AAEA,aAAS,UAAUC,iBAAgB;AAClC,aAAO,UAAUA,eAAc,EAAE,SAAS,CAAC;AAAA,IAC5C;AAEA,aAAS,aAAaA,iBAAgB;AACrC,aAAO,WAAWA,eAAc;AAAA,IACjC;AAEA,aAAS,eAAe,WAAW;AAClC,aAAO,WAAW,QAAQ,SAAS;AAAA,IACpC;AAEA,aAAS,cAAc,MAAM;AAC5B,UAAI,WAAW;AACf,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACrC,oBAAY,eAAe,KAAK,CAAC,CAAC;AAAA,MACnC;AAEA,iBAAW,WAAW;AACtB,aAAO;AAAA,IACR;AAEA,YAAQ,SAAS;AAAA;AAAA;;;ACxGjB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI;AAEJ,aAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,UAAI,OAAO,KAAK;AAAE,eAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,MAAG,OAAO;AAAE,YAAI,GAAG,IAAI;AAAA,MAAO;AAAE,aAAO;AAAA,IAAK;AAGhN,QAAI,QAAQ,QAAQ,QAAQ;AAC5B,QAAI,QAAQ,QAAQ,QAAQ;AAC5B,QAAI,QAAQ,QAAQ,QAAQ;AAG5B,QAAI,QAAQ,QAAQ,QAAQ;AAC5B,QAAI,UAAU,QAAQ,UAAU;AAChC,QAAI,UAAU,QAAQ,UAAU;AAChC,QAAI,UAAU,QAAQ,UAAU;AAChC,QAAI,SAAS,QAAQ,SAAS;AAC9B,QAAI,OAAO,QAAQ,OAAO;AAC1B,QAAI,OAAO,QAAQ,OAAO;AAG1B,QAAI,cAAc,QAAQ,eAAe,eAAe,CAAC,GAAG,gBAAgB,cAAc,SAAS,KAAK,GAAG,gBAAgB,cAAc,SAAS,KAAK,GAAG,gBAAgB,cAAc,SAAS,KAAK,GAAG;AAGzM,QAAI,OAAO,QAAQ,OAAO;AAAA,MACzB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,IAAI;AAAA,IACL;AAEA,QAAI,eAAe,QAAQ,eAAe,OAAO,aAAa,GAAG;AACjE,QAAI,eAAe,QAAQ,eAAe,OAAO,aAAa,GAAG;AACjE,QAAI,eAAe,QAAQ,eAAe,OAAO,aAAa,GAAG;AAIjE,QAAI,UAAU,QAAQ,UAAU;AAIhC,QAAI,UAAU,QAAQ,UAAU;AAIhC,QAAI,UAAU,QAAQ,UAAU;AAKhC,QAAI,OAAO,QAAQ,OAAO,CAAC,aAAa,aAAa,aAAa,YAAa,aAAa,aAAa,YAAa,aAAa,aAAa,YAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,YAAa,aAAa,aAAa,YAAa,YAAa,aAAa,YAAa,YAAa,aAAa,YAAa,YAAa,aAAa,YAAa,aAAa,aAAa,YAAa,aAAa,aAAa,aAAa,aAAa,aAAa,YAAa,aAAa,aAAa,YAAa,aAAa,aAAa,YAAa,aAAa,aAAa,aAAa,aAAa,aAAa,WAAa,aAAa,WAAa,aAAa,aAAa,aAAa,WAAa,aAAa,WAAa,aAAa,aAAa,aAAa,aAAa,WAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,YAAa,aAAa,aAAa,YAAa,aAAa,YAAa,aAAa,aAAa,aAAa,aAAa,aAAa,aAAa,WAAa,aAAa,aAAa;AAAA;AAAA;;;ACrD14C;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,QAAI,aAAa;AAEjB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAI7e,QAAI,UAAU,SAAU,UAAU;AACjC,gBAAUC,UAAS,QAAQ;AAE3B,eAASA,SAAQ,MAAM,SAAS;AAC/B,wBAAgB,MAAMA,QAAO;AAG7B,YAAI,QAAQ,2BAA2B,OAAOA,SAAQ,aAAa,OAAO,eAAeA,QAAO,GAAG,KAAK,MAAM,KAAK,UAAU,CAAC,GAAG,OAAO,CAAC;AAEzI,cAAM,QAAQ,KAAK,MAAM,EAAE,EAAE,IAAI,SAAU,MAAM;AAChD,iBAAO,KAAK,WAAW,CAAC;AAAA,QACzB,CAAC;AACD,eAAO;AAAA,MACR;AAEA,mBAAaA,UAAS,CAAC;AAAA,QACtB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AAEvB,iBAAQ,0BAA0B,KAAK,KAAK,IAAI;AAAA,QAEjD;AAAA;AAAA,MAID,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,cAAI,QAAQ,KAAK;AAEjB,cAAI,aAAa,MAAM,MAAM,IAAI;AAEjC,cAAI,WAAW,WAAW,YAAY,UAAU;AAEhD,cAAI,aAAa,QAAW;AAC3B,kBAAM,IAAI,WAAW,qDAAqD;AAAA,UAC3E;AAEA,cAAI,KAAK,qBAAqB,MAAM,MAAM;AACzC,kBAAM,QAAQ,WAAW,IAAI;AAAA,UAC9B;AAGA,cAAI,iBAAiBA,SAAQ,KAAK,OAAO,GAAG,QAAQ;AAEpD,iBAAO;AAAA,YACN,MAAM,KAAK,SAAS,KAAK,OAAO,KAAK,KAAK,QAAQ,iBAAiB,EAAE,IAAI,KAAK;AAAA,YAC9E;AAAA;AAAA,cAEAA,SAAQ,OAAO,UAAU;AAAA,cAEzB,eAAe;AAAA,cAEfA,SAAQ,QAAQ,eAAe,WAAW,cAAc,WAAW,MAAM;AAAA,cAEzEA,SAAQ,OAAO,WAAW,IAAI;AAAA;AAAA,UAC/B;AAAA,QACD;AAAA;AAAA,MAID,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,uBAAuB;AACtC,cAAI,WAAW,KAAK,QAAQ,UAAU;AACtC,cAAI,OAAO,aAAa,UAAU;AACjC,uBAAW,SAAS,YAAY,MAAM;AAAA,UACvC;AACA,iBAAO;AAAA,QACR;AAAA;AAAA,MAID,CAAC,GAAG,CAAC;AAAA,QACJ,KAAK;AAAA,QACL,OAAO,SAAS,OAAO,OAAO;AAC7B,iBAAO,WAAW,KAAK,KAAK,IAAI,WAAW,KAAK,KAAK,EAAE,SAAS,IAAI;AAAA,QACrE;AAAA;AAAA,MAID,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,aAAa,OAAO,KAAK;AACxC,cAAI,QAAQ,WAAW,OAAO;AAC7B,gBAAI,WAAW,MAAM,MAAM;AAC3B,mBAAO,WAAW,KAAK,WAAW,KAAK,WAAW;AAAA,UACnD,WAAW,QAAQ,WAAW,OAAO;AACpC,mBAAO,MAAM,MAAM,IAAI;AAAA,UACxB,OAAO;AACN,oBAAQ,MAAM,MAAM,IAAI,MAAM,KAAK,MAAM,MAAM,IAAI;AAAA,UACpD;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,KAAK,OAAO,KAAK,KAAK;AACrC,cAAI,CAAC,MAAM,QAAQ;AAClB,mBAAO,EAAE,QAAQ,IAAI,UAAU,EAAE;AAAA,UAClC;AAEA,cAAI,WAAW,QACX,QAAQ;AAGZ,cAAI,MAAM,CAAC,KAAK,KAAK;AACpB,oBAAQ,MAAM,MAAM,IAAI;AACxB,gBAAI,UAAU,WAAW,KAAK,KAAK;AAGnC,gBAAI,YAAY,QAAW;AAC1B,yBAAWA,SAAQ,KAAK,OAAO,MAAM,GAAG,OAAO;AAAA,YAChD,OAEK;AAEH,mBAAK,QAAQ,WAAW,SAAS,QAAQ,WAAW,UAAU,UAAU,WAAW,OAAO;AAEzF,sBAAM,CAAC,IAAI,QAAQ,WAAW,QAAQ,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC;AAAA,cACzH;AACA,yBAAWA,SAAQ,KAAK,OAAO,MAAM,GAAG,GAAG;AAAA,YAC5C;AAAA,UACF,OAEK;AACH,oBAAQA,SAAQ,aAAa,OAAO,GAAG;AACvC,uBAAWA,SAAQ,KAAK,OAAO,MAAM,GAAG,GAAG;AAAA,UAC5C;AAGD,cAAI,MAAMA,SAAQ,OAAO,KAAK;AAC9B,cAAI,SAAS,QAAQ;AAErB,iBAAO;AAAA,YACN,QAAQ,MAAM,SAAS;AAAA,YACvB,UAAU,SAAS,SAAS;AAAA,UAC7B;AAAA,QACD;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,UAAU,OAAO;AAEnB,YAAQ,UAAU;AAAA;AAAA;;;ACtKlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,aAAa;AAGjB,QAAI,kBAAkB,SAASC,iBAAgB,QAAQ;AACtD,aAAO,OAAO,MAAM,IAAI,OAAO,MAAM,WAAW,UAAU,GAAG,CAAC,EAAE,CAAC,EAAE;AAAA,IACpE;AACA,QAAI,kBAAkB,SAASC,iBAAgB,QAAQ;AACtD,aAAO,OAAO,MAAM,IAAI,OAAO,MAAM,WAAW,UAAU,GAAG,CAAC,EAAE,CAAC,EAAE;AAAA,IACpE;AACA,QAAI,YAAY,SAASC,WAAU,QAAQ;AAC1C,aAAO,OAAO,MAAM,IAAI,OAAO,MAAM,WAAW,UAAU,GAAG,CAAC,EAAE,CAAC;AAAA,IAClE;AAGA,aAAS,iBAAiB,QAAQ,KAAK;AACtC,UAAI,SAAS,MAAM,WAAW,UAAU,WAAW;AACnD,UAAI,SAAS,OAAO,MAAM,IAAI,OAAO,OAAO,SAAS,+BAA+B,CAAC;AAErF,UAAI,QAAQ;AACX,eAAO,OAAO,CAAC,IAAI,OAAO,aAAa,GAAG,IAAI,gBAAgB,OAAO,UAAU,OAAO,CAAC,EAAE,MAAM,CAAC;AAAA,MACjG;AAEA,UAAI,QAAQ,OAAO,MAAM,IAAI,OAAO,MAAM,SAAS,GAAG,CAAC,EAAE,CAAC;AAE1D,UAAI,MAAM,WAAW,OAAO,QAAQ;AACnC,eAAO;AAAA,MACR;AAEA,aAAO,QAAQ,OAAO,aAAa,MAAM,MAAM,GAAG,IAAI,iBAAiB,OAAO,UAAU,MAAM,MAAM,GAAG,CAAC,GAAG;AAAA,IAC5G;AAGA,aAAS,gBAAgB,QAAQ;AAChC,UAAI,SAAS,UAAU,MAAM;AAC7B,UAAI,SAAS,OAAO;AAEpB,UAAI,WAAW,OAAO,QAAQ;AAC7B,eAAO;AAAA,MACR;AAEA,eAAS,OAAO,UAAU,MAAM;AAGhC,UAAI,MAAM,gBAAgB,MAAM,KAAK,gBAAgB,MAAM;AAC3D,aAAO,SAAS,OAAO,aAAa,MAAM,MAAM,GAAG,IAAI,iBAAiB,QAAQ,GAAG;AAAA,IACpF;AAIA,YAAQ,UAAU,SAAU,QAAQ;AACnC,UAAI,YAAY;AAChB,UAAI,UAAU,UAAU,MAAM,EAAE;AAGhC,UAAI,WAAW,GAAG;AACjB,oBAAY,WAAW,eAAe,gBAAgB,MAAM;AAAA,MAC7D,OAAO;AAEN,YAAI,MAAM,gBAAgB,MAAM,IAAI,gBAAgB,MAAM;AAC1D,qBAAa,MAAM,WAAW,eAAe,WAAW,gBAAgB,iBAAiB,QAAQ,GAAG;AAAA,MACrG;AAEA,aAAO,UAAU;AAAA,QAAQ;AAAA;AAAA,QACzB,SAAU,OAAO,MAAM;AACtB,iBAAO,OAAO,aAAa,GAAG,IAAI;AAAA,QACnC;AAAA,MAAC;AAAA,IACF;AAAA;AAAA;;;ACxEA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,SAAS;AAEb,QAAI,SAAS,uBAAuB,MAAM;AAE1C,QAAI,QAAQ;AAEZ,QAAI,SAAS,uBAAuB,KAAK;AAEzC,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,cAAc,SAAU,OAAO;AAClC,gBAAUC,cAAa,KAAK;AAE5B,eAASA,aAAY,MAAM,SAAS;AACnC,wBAAgB,MAAMA,YAAW;AAGjC,YAAI,0BAA0B,KAAK,IAAI,GAAG;AACzC,cAAI,QAAQ,2BAA2B,OAAOA,aAAY,aAAa,OAAO,eAAeA,YAAW,GAAG,KAAK,OAAO,GAAG,OAAO,SAAS,IAAI,GAAG,OAAO,CAAC;AAAA,QAC1J,OAAO;AACN,cAAI,QAAQ,2BAA2B,OAAOA,aAAY,aAAa,OAAO,eAAeA,YAAW,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,QACrI;AACA,eAAO,2BAA2B,KAAK;AAAA,MACxC;AAEA,aAAOA;AAAA,IACR,EAAE,OAAO,OAAO;AAEhB,YAAQ,UAAU;AAAA;AAAA;;;ACxClB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,SAAS;AAEb,QAAI,SAAS,uBAAuB,MAAM;AAE1C,QAAI,aAAa;AAEjB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,WAAW,SAAU,OAAO;AAC/B,gBAAUC,WAAU,KAAK;AAEzB,eAASA,UAAS,QAAQ,SAAS;AAClC,wBAAgB,MAAMA,SAAQ;AAE9B,eAAO,2BAA2B,OAAOA,UAAS,aAAa,OAAO,eAAeA,SAAQ,GAAG,KAAK,MAAM,WAAW,eAAe,QAAQ,OAAO,CAAC;AAAA,MACtJ;AAEA,mBAAaA,WAAU,CAAC;AAAA,QACvB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,IAAI,OAAO,MAAM,WAAW,UAAU,IAAI,EAAE,KAAK,KAAK,IAAI;AAAA,QAClE;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,OAAO,OAAO;AAEhB,YAAQ,UAAU;AAAA;AAAA;;;ACzClB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,SAAS;AAEb,QAAI,SAAS,uBAAuB,MAAM;AAE1C,QAAI,aAAa;AAEjB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,WAAW,SAAU,OAAO;AAC/B,gBAAUC,WAAU,KAAK;AAEzB,eAASA,UAAS,QAAQ,SAAS;AAClC,wBAAgB,MAAMA,SAAQ;AAE9B,eAAO,2BAA2B,OAAOA,UAAS,aAAa,OAAO,eAAeA,SAAQ,GAAG,KAAK,MAAM,WAAW,eAAe,QAAQ,OAAO,CAAC;AAAA,MACtJ;AAEA,mBAAaA,WAAU,CAAC;AAAA,QACvB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,IAAI,OAAO,MAAM,WAAW,UAAU,IAAI,EAAE,KAAK,KAAK,IAAI;AAAA,QAClE;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,OAAO,OAAO;AAEhB,YAAQ,UAAU;AAAA;AAAA;;;ACzClB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,SAAS;AAEb,QAAI,SAAS,uBAAuB,MAAM;AAE1C,QAAI,aAAa;AAEjB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,WAAW,SAAU,OAAO;AAC/B,gBAAUC,WAAU,KAAK;AAEzB,eAASA,UAAS,QAAQ,SAAS;AAClC,wBAAgB,MAAMA,SAAQ;AAE9B,eAAO,2BAA2B,OAAOA,UAAS,aAAa,OAAO,eAAeA,SAAQ,GAAG,KAAK,MAAM,WAAW,eAAe,QAAQ,OAAO,CAAC;AAAA,MACtJ;AAEA,mBAAaA,WAAU,CAAC;AAAA,QACvB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,IAAI,OAAO,MAAM,WAAW,UAAU,IAAI,EAAE,KAAK,KAAK,IAAI;AAAA,QAClE;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,OAAO,OAAO;AAEhB,YAAQ,UAAU;AAAA;AAAA;;;ACzClB,IAAAC,mBAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,WAAW,QAAQ,WAAW,QAAQ,WAAW,QAAQ,UAAU;AAE3E,QAAI,gBAAgB;AAEpB,QAAI,iBAAiB,uBAAuB,aAAa;AAEzD,QAAI,YAAY;AAEhB,QAAI,aAAa,uBAAuB,SAAS;AAEjD,QAAI,YAAY;AAEhB,QAAI,aAAa,uBAAuB,SAAS;AAEjD,QAAI,YAAY;AAEhB,QAAI,aAAa,uBAAuB,SAAS;AAEjD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,YAAQ,UAAU,eAAe;AACjC,YAAQ,WAAW,WAAW;AAC9B,YAAQ,WAAW,WAAW;AAC9B,YAAQ,WAAW,WAAW;AAAA;AAAA;;;AC5B9B,IAAAC,qBAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,WAAW,QAAQ,WAAW;AAClC,QAAI,aAAa,QAAQ,aAAa;AAEtC,QAAI,WAAW,QAAQ,WAAW;AAAA,MACjC,KAAK;AAAA;AAAA,QACL;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,MAAS;AAAA,MAC5G,KAAK;AAAA;AAAA,QACL;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,MAAS;AAAA,MAC5G,KAAK;AAAA;AAAA,QACL;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,MAAS;AAAA,MAC5G,KAAK;AAAA;AAAA,QACL;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,MAAS;AAAA,MAC5G,KAAK;AAAA;AAAA,QACL;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,QAAW;AAAA,MAAS;AAAA,IAC7G;AAGA,QAAI,iBAAiB,QAAQ,iBAAiB,CAAC,MAAM,MAAM,MAAM,IAAI;AAGrE,QAAI,iBAAiB,QAAQ,iBAAiB,CAAC,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,OAAO;AAGvI,QAAI,kBAAkB,QAAQ,kBAAkB,CAAC,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU,QAAQ;AAAA;AAAA;;;AC7BnJ;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,aAAa;AAGjB,QAAI,SAAS,SAASC,QAAO,MAAM,WAAW,WAAW;AACxD,UAAI,UAAU,KAAK,MAAM,EAAE,EAAE,IAAI,SAAU,KAAK,KAAK;AACpD,eAAO,WAAW,SAAS,UAAU,GAAG,CAAC;AAAA,MAC1C,CAAC,EAAE,IAAI,SAAU,KAAK,KAAK;AAC1B,eAAO,MAAM,IAAI,KAAK,GAAG,CAAC,IAAI;AAAA,MAC/B,CAAC;AAED,UAAI,WAAW;AACd,YAAI,OAAO,KAAK,SAAS;AACzB,kBAAU,QAAQ,IAAI,SAAU,KAAK,KAAK;AACzC,iBAAO,MAAM,OAAO,MAAM,YAAY;AAAA,QACvC,CAAC;AAAA,MACF;AAEA,aAAO,QAAQ,KAAK,EAAE;AAAA,IACvB;AAEA,YAAQ,UAAU;AAAA;AAAA;;;AC1BlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,aAAa;AAEjB,QAAI,WAAW;AAEf,QAAI,YAAY,uBAAuB,QAAQ;AAE/C,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAG7e,QAAI,MAAM,SAAU,UAAU;AAC7B,gBAAUC,MAAK,QAAQ;AAEvB,eAASA,KAAI,MAAM,SAAS;AAC3B,wBAAgB,MAAMA,IAAG;AAGzB,YAAI,QAAQ,2BAA2B,OAAOA,KAAI,aAAa,OAAO,eAAeA,IAAG,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAEpH,cAAM,WAAW,CAAC,QAAQ,QAAQ,QAAQ,WAAW,QAAQ,QAAQ,KAAK,QAAQ,QAAQ,KAAK,QAAQ;AAGvG,cAAM,cAAc,QAAQ,SAAS,MAAM,WAAW,IAAI,QAAQ;AAClE,eAAO;AAAA,MACR;AAEA,mBAAaA,MAAK,CAAC;AAAA,QAClB,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,iBAAO,KAAK,QAAQ,OAAO,KAAK,WAAW,IAAI,KAAK,cAAc;AAAA,QACnE;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,SAAS,MAAM,IAAI;AAClC,iBAAO,KAAK,KAAK,OAAO,MAAM,EAAE;AAAA,QACjC;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,WAAW,MAAM,WAAW;AAC3C,kBAAQ,GAAG,UAAU,SAAS,MAAM,SAAS;AAAA,QAC9C;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,UAAU,MAAM,IAAI;AACnC,iBAAO,KAAK,KAAK,OAAO,MAAM,EAAE;AAAA,QACjC;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,YAAY,MAAM,WAAW;AAC5C,kBAAQ,GAAG,UAAU,SAAS,MAAM,SAAS;AAAA,QAC9C;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,gBAAgB;AAC/B,cAAI,cAAc,EAAE,UAAU,KAAK,SAAS;AAC5C,cAAI,eAAe,EAAE,QAAQ,KAAK,YAAY;AAE9C,iBAAO,CAAC,EAAE,MAAM,WAAW,UAAU,SAAS,aAAa,GAAG,EAAE,MAAM,KAAK,WAAW,GAAG,MAAM,KAAK,SAAS,GAAG,SAAS,YAAY,GAAG,EAAE,MAAM,WAAW,YAAY,SAAS,aAAa,GAAG,EAAE,MAAM,KAAK,YAAY,GAAG,MAAM,KAAK,UAAU,GAAG,SAAS,YAAY,GAAG,EAAE,MAAM,WAAW,UAAU,SAAS,aAAa,CAAC;AAAA,QACjU;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,aAAa;AAC5B,cAAI,OAAO,CAAC,WAAW,UAAU,KAAK,WAAW,GAAG,WAAW,YAAY,KAAK,YAAY,GAAG,WAAW,QAAQ;AAElH,iBAAO;AAAA,YACN,MAAM,KAAK,KAAK,EAAE;AAAA,YAClB,MAAM,KAAK;AAAA,UACZ;AAAA,QACD;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,UAAU,OAAO;AAEnB,YAAQ,UAAU;AAAA;AAAA;;;AC3FlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,OAAO,SAAS,IAAI,QAAQ,UAAU,UAAU;AAAE,UAAI,WAAW,KAAM,UAAS,SAAS;AAAW,UAAI,OAAO,OAAO,yBAAyB,QAAQ,QAAQ;AAAG,UAAI,SAAS,QAAW;AAAE,YAAI,SAAS,OAAO,eAAe,MAAM;AAAG,YAAI,WAAW,MAAM;AAAE,iBAAO;AAAA,QAAW,OAAO;AAAE,iBAAO,IAAI,QAAQ,UAAU,QAAQ;AAAA,QAAG;AAAA,MAAE,WAAW,WAAW,MAAM;AAAE,eAAO,KAAK;AAAA,MAAO,OAAO;AAAE,YAAI,SAAS,KAAK;AAAK,YAAI,WAAW,QAAW;AAAE,iBAAO;AAAA,QAAW;AAAE,eAAO,OAAO,KAAK,QAAQ;AAAA,MAAG;AAAA,IAAE;AAEze,QAAI,aAAa;AAEjB,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAK7e,QAAI,WAAW,SAASC,UAAS,QAAQ;AACxC,UAAI,MAAM,OAAO,OAAO,GAAG,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,SAAU,GAAG;AACzD,eAAO,CAAC;AAAA,MACT,CAAC,EAAE,OAAO,SAAU,KAAK,GAAG,KAAK;AAChC,eAAO,MAAM,IAAI,MAAM,IAAI,IAAI,MAAM;AAAA,MACtC,GAAG,CAAC;AAEJ,cAAQ,KAAK,MAAM,MAAM;AAAA,IAC1B;AAEA,QAAI,QAAQ,SAAU,MAAM;AAC3B,gBAAUC,QAAO,IAAI;AAErB,eAASA,OAAM,MAAM,SAAS;AAC7B,wBAAgB,MAAMA,MAAK;AAG3B,YAAI,KAAK,OAAO,aAAa,MAAM,IAAI;AACtC,kBAAQ,SAAS,IAAI;AAAA,QACtB;AAGA,YAAI,QAAQ,2BAA2B,OAAOA,OAAM,aAAa,OAAO,eAAeA,MAAK,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAExH,cAAM,WAAW,QAAQ;AACzB,eAAO;AAAA,MACR;AAEA,mBAAaA,QAAO,CAAC;AAAA,QACpB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,KAAK,OAAO,aAAa,MAAM,MAAM,CAAC,KAAK,KAAK,EAAE,MAAM,SAAS,KAAK,IAAI;AAAA,QACvF;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,WAAW;AAC1B,iBAAO,KAAKA,OAAM,UAAU,aAAa,OAAO,eAAeA,OAAM,SAAS,GAAG,YAAY,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,QACnH;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,aAAa;AAC5B,cAAI,OAAO,KAAK,KAAK,OAAO,GAAG,CAAC;AAChC,cAAI,YAAY,WAAW,gBAAgB,KAAK,KAAK,CAAC,CAAC;AACvD,iBAAO,KAAKA,OAAM,UAAU,aAAa,OAAO,eAAeA,OAAM,SAAS,GAAG,cAAc,IAAI,EAAE,KAAK,MAAM,MAAM,SAAS;AAAA,QAChI;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,YAAY;AAC3B,iBAAO,KAAKA,OAAM,UAAU,aAAa,OAAO,eAAeA,OAAM,SAAS,GAAG,aAAa,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,QACpH;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,cAAc;AAC7B,cAAI,OAAO,KAAK,KAAK,OAAO,GAAG,CAAC;AAChC,iBAAO,KAAKA,OAAM,UAAU,aAAa,OAAO,eAAeA,OAAM,SAAS,GAAG,eAAe,IAAI,EAAE,KAAK,MAAM,MAAM,QAAQ;AAAA,QAChI;AAAA;AAAA,MAID,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,gBAAgB;AAC/B,cAAI,OAAO,KAAKA,OAAM,UAAU,aAAa,OAAO,eAAeA,OAAM,SAAS,GAAG,iBAAiB,IAAI,EAAE,KAAK,IAAI;AAGrH,cAAI,KAAK,QAAQ,cAAc;AAC9B,iBAAK,QAAQ;AAAA,cACZ,MAAM;AAAA,cACN,MAAM,KAAK,KAAK,OAAO,GAAG,CAAC;AAAA,cAC3B,SAAS,EAAE,WAAW,QAAQ,UAAU,KAAK,SAAS;AAAA,YACvD,CAAC;AAED,gBAAI,KAAK,QAAQ,UAAU;AAC1B,mBAAK,KAAK;AAAA,gBACT,MAAM;AAAA,cACP,CAAC;AACD,mBAAK,KAAK;AAAA,gBACT,MAAM;AAAA,gBACN,MAAM,KAAK,QAAQ;AAAA,gBACnB,SAAS,EAAE,UAAU,KAAK,SAAS;AAAA,cACpC,CAAC;AAAA,YACF;AAAA,UACD;AAEA,iBAAO;AAAA,QACR;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,MAAM,OAAO;AAEf,YAAQ,UAAU;AAAA;AAAA;;;ACtHlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,OAAO,SAAS,IAAI,QAAQ,UAAU,UAAU;AAAE,UAAI,WAAW,KAAM,UAAS,SAAS;AAAW,UAAI,OAAO,OAAO,yBAAyB,QAAQ,QAAQ;AAAG,UAAI,SAAS,QAAW;AAAE,YAAI,SAAS,OAAO,eAAe,MAAM;AAAG,YAAI,WAAW,MAAM;AAAE,iBAAO;AAAA,QAAW,OAAO;AAAE,iBAAO,IAAI,QAAQ,UAAU,QAAQ;AAAA,QAAG;AAAA,MAAE,WAAW,WAAW,MAAM;AAAE,eAAO,KAAK;AAAA,MAAO,OAAO;AAAE,YAAI,SAAS,KAAK;AAAK,YAAI,WAAW,QAAW;AAAE,iBAAO;AAAA,QAAW;AAAE,eAAO,OAAO,KAAK,QAAQ;AAAA,MAAG;AAAA,IAAE;AAEze,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAI7e,QAAI,WAAW,SAASC,UAAS,QAAQ;AACxC,UAAI,MAAM,OAAO,OAAO,GAAG,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,SAAU,GAAG;AACxD,eAAO,CAAC;AAAA,MACT,CAAC,EAAE,OAAO,SAAU,KAAK,GAAG,KAAK;AAChC,eAAO,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI;AAAA,MACtC,GAAG,CAAC;AAEJ,cAAQ,KAAK,MAAM,MAAM;AAAA,IAC1B;AAEA,QAAI,OAAO,SAAU,MAAM;AAC1B,gBAAUC,OAAM,IAAI;AAEpB,eAASA,MAAK,MAAM,SAAS;AAC5B,wBAAgB,MAAMA,KAAI;AAG1B,YAAI,KAAK,OAAO,YAAY,MAAM,IAAI;AACrC,kBAAQ,SAAS,IAAI;AAAA,QACtB;AAEA,eAAO,2BAA2B,OAAOA,MAAK,aAAa,OAAO,eAAeA,KAAI,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,MAClH;AAEA,mBAAaA,OAAM,CAAC;AAAA,QACnB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,KAAK,OAAO,YAAY,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,MAAM,SAAS,KAAK,IAAI;AAAA,QACrF;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,WAAW;AAC1B,iBAAO,KAAKA,MAAK,UAAU,aAAa,OAAO,eAAeA,MAAK,SAAS,GAAG,YAAY,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,QACjH;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,aAAa;AAC5B,cAAI,OAAO,KAAK,KAAK,OAAO,GAAG,CAAC;AAChC,iBAAO,KAAKA,MAAK,UAAU,aAAa,OAAO,eAAeA,MAAK,SAAS,GAAG,cAAc,IAAI,EAAE,KAAK,MAAM,MAAM,MAAM;AAAA,QAC3H;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,YAAY;AAC3B,iBAAO,KAAKA,MAAK,UAAU,aAAa,OAAO,eAAeA,MAAK,SAAS,GAAG,aAAa,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,QAClH;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,cAAc;AAC7B,cAAI,OAAO,KAAK,KAAK,OAAO,GAAG,CAAC;AAChC,iBAAO,KAAKA,MAAK,UAAU,aAAa,OAAO,eAAeA,MAAK,SAAS,GAAG,eAAe,IAAI,EAAE,KAAK,MAAM,MAAM,MAAM;AAAA,QAC5H;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,MAAM,OAAO;AAEf,YAAQ,UAAU;AAAA;AAAA;;;AChFlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,aAAa;AAEjB,QAAI,WAAW;AAEf,QAAI,YAAY,uBAAuB,QAAQ;AAE/C,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAG7e,QAAI,WAAW,SAASC,UAAS,MAAM;AACtC,UAAI,SAAS,KAAK,MAAM,EAAE,EAAE,IAAI,SAAU,GAAG;AAC5C,eAAO,CAAC;AAAA,MACT,CAAC,EAAE,OAAO,SAAU,KAAK,GAAG,KAAK;AAChC,eAAO,MAAM,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI;AAAA,MAC1C,GAAG,CAAC;AACJ,aAAO,SAAS;AAAA,IACjB;AAEA,QAAI,OAAO,SAAU,UAAU;AAC9B,gBAAUC,OAAM,QAAQ;AAExB,eAASA,MAAK,MAAM,SAAS;AAC5B,wBAAgB,MAAMA,KAAI;AAE1B,eAAO,2BAA2B,OAAOA,MAAK,aAAa,OAAO,eAAeA,KAAI,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,MAClH;AAEA,mBAAaA,OAAM,CAAC;AAAA,QACnB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,KAAK,OAAO,YAAY,MAAM;AAAA,QAC3C;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,cAAI,YAAY,WAAW,eAAe,SAAS,KAAK,IAAI,CAAC;AAC7D,iBAAO;AAAA,YACN,MAAM,UAAU,GAAG,UAAU,SAAS,KAAK,MAAM,WAAW,IAAI;AAAA,YAChE,MAAM,KAAK;AAAA,UACZ;AAAA,QACD;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,UAAU,OAAO;AAEnB,YAAQ,UAAU;AAAA;AAAA;;;AChElB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,aAAa;AAEjB,QAAI,WAAW;AAEf,QAAI,YAAY,uBAAuB,QAAQ;AAE/C,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAG7e,QAAI,OAAO,SAAU,UAAU;AAC9B,gBAAUC,OAAM,QAAQ;AAExB,eAASA,MAAK,MAAM,SAAS;AAC5B,wBAAgB,MAAMA,KAAI;AAE1B,eAAO,2BAA2B,OAAOA,MAAK,aAAa,OAAO,eAAeA,KAAI,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,MAClH;AAEA,mBAAaA,OAAM,CAAC;AAAA,QACnB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,KAAK,OAAO,YAAY,MAAM;AAAA,QAC3C;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AAExB,cAAI,YAAY,WAAW,eAAe,SAAS,KAAK,IAAI,IAAI,CAAC;AACjE,iBAAO;AAAA;AAAA,YAEN,MAAM,UAAU,GAAG,UAAU,SAAS,KAAK,MAAM,WAAW,IAAI;AAAA,YAChE,MAAM,KAAK;AAAA,UACZ;AAAA,QACD;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,UAAU,OAAO;AAEnB,YAAQ,UAAU;AAAA;AAAA;;;ACzDlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,YAAQ,WAAW;AAEnB,QAAI,WAAW;AAEf,QAAI,YAAY,uBAAuB,QAAQ;AAE/C,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAG7e,QAAI,MAAM,SAAU,UAAU;AAC7B,gBAAUC,MAAK,QAAQ;AAEvB,eAASA,KAAI,MAAM,SAAS;AAC3B,wBAAgB,MAAMA,IAAG;AAGzB,YAAI,KAAK,OAAO,aAAa,MAAM,IAAI;AACtC,kBAAQ,SAAS,IAAI;AAAA,QACtB;AAEA,YAAI,QAAQ,2BAA2B,OAAOA,KAAI,aAAa,OAAO,eAAeA,IAAG,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAEpH,cAAM,eAAe,QAAQ;AAG7B,YAAI,QAAQ,WAAW,QAAQ,QAAQ,IAAI;AAC1C,gBAAM,WAAW,QAAQ,QAAQ;AAAA,QAClC,OAAO;AACN,gBAAM,WAAW,QAAQ;AAAA,QAC1B;AAGA,cAAM,cAAc,QAAQ,SAAS,MAAM,WAAW,IAAI,QAAQ;AAClE,eAAO;AAAA,MACR;AAEA,mBAAaA,MAAK,CAAC;AAAA,QAClB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,KAAK,OAAO,aAAa,MAAM,MAAM,KAAK,KAAK,EAAE,KAAK,SAAS,KAAK,IAAI;AAAA,QACrF;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,cAAI,KAAK,QAAQ,MAAM;AACtB,mBAAO,KAAK,aAAa;AAAA,UAC1B,OAAO;AACN,mBAAO,KAAK,gBAAgB;AAAA,UAC7B;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,eAAe;AAC9B,cAAI,SAAS;AAEb,oBAAU;AACV,qBAAW,GAAG,UAAU,SAAS,KAAK,KAAK,OAAO,GAAG,CAAC,GAAG,QAAQ;AACjE,oBAAU;AACV,qBAAW,GAAG,UAAU,SAAS,KAAK,KAAK,OAAO,GAAG,CAAC,GAAG,QAAQ;AACjE,oBAAU;AAEV,iBAAO;AAAA,YACN,MAAM;AAAA,YACN,MAAM,KAAK;AAAA,UACZ;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,kBAAkB;AACjC,cAAI,SAAS,CAAC;AAGd,cAAI,KAAK,cAAc;AACtB,mBAAO,KAAK;AAAA,cACX,MAAM;AAAA,cACN,MAAM,KAAK,KAAK,OAAO,GAAG,CAAC;AAAA,cAC3B,SAAS,EAAE,WAAW,QAAQ,UAAU,KAAK,SAAS;AAAA,YACvD,CAAC;AAAA,UACF;AAGA,iBAAO,KAAK;AAAA,YACX,MAAM,SAAS,GAAG,UAAU,SAAS,KAAK,KAAK,CAAC,GAAG,GAAG;AAAA,YACtD,SAAS,EAAE,QAAQ,KAAK,YAAY;AAAA,UACrC,CAAC;AAGD,iBAAO,KAAK;AAAA,YACX,OAAO,GAAG,UAAU,SAAS,KAAK,KAAK,OAAO,GAAG,CAAC,GAAG,OAAO;AAAA,YAC5D,MAAM,KAAK,KAAK,OAAO,GAAG,CAAC;AAAA,YAC3B,SAAS,EAAE,UAAU,KAAK,SAAS;AAAA,UACpC,CAAC;AAGD,iBAAO,KAAK;AAAA,YACX,MAAM;AAAA,YACN,SAAS,EAAE,QAAQ,KAAK,YAAY;AAAA,UACrC,CAAC;AAGD,iBAAO,KAAK;AAAA,YACX,OAAO,GAAG,UAAU,SAAS,KAAK,KAAK,OAAO,GAAG,CAAC,GAAG,OAAO;AAAA,YAC5D,MAAM,KAAK,KAAK,OAAO,GAAG,CAAC;AAAA,YAC3B,SAAS,EAAE,UAAU,KAAK,SAAS;AAAA,UACpC,CAAC;AAGD,iBAAO,KAAK;AAAA,YACX,OAAO,GAAG,UAAU,SAAS,KAAK,KAAK,EAAE,GAAG,GAAG,IAAI;AAAA,YACnD,SAAS,EAAE,QAAQ,KAAK,YAAY;AAAA,UACrC,CAAC;AAGD,cAAI,KAAK,cAAc;AACtB,mBAAO,KAAK;AAAA,cACX,MAAM;AAAA,cACN,MAAM,KAAK,KAAK,OAAO,IAAI,CAAC;AAAA,cAC5B,SAAS,EAAE,WAAW,SAAS,UAAU,KAAK,SAAS;AAAA,YACxD,CAAC;AAAA,UACF;AAEA,iBAAO;AAAA,QACR;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,UAAU,OAAO;AAMnB,aAAS,SAAS,QAAQ;AACzB,UAAI,SAAS;AAEb,UAAI;AACJ,WAAK,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAC3B,kBAAU,SAAS,OAAO,CAAC,CAAC;AAAA,MAC7B;AACA,WAAK,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAC3B,kBAAU,SAAS,OAAO,CAAC,CAAC,IAAI;AAAA,MACjC;AAEA,cAAQ,KAAK,SAAS,MAAM;AAAA,IAC7B;AAEA,YAAQ,UAAU;AAAA;AAAA;;;ACpKlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,WAAW;AAEf,QAAI,YAAY,uBAAuB,QAAQ;AAE/C,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,QAAI,OAAO;AAEX,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAM7e,QAAI,aAAa,CAAC,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,YAAY;AAE5J,QAAI,WAAW,CAAC,CAAC,UAAU,QAAQ,GAAG,CAAC,UAAU,QAAQ,GAAG,CAAC,UAAU,QAAQ,GAAG,CAAC,UAAU,QAAQ,GAAG,CAAC,UAAU,QAAQ,GAAG,CAAC,UAAU,QAAQ,GAAG,CAAC,UAAU,QAAQ,GAAG,CAAC,UAAU,QAAQ,GAAG,CAAC,UAAU,QAAQ,GAAG,CAAC,UAAU,QAAQ,CAAC;AAE1O,QAAI,OAAO,SAAU,UAAU;AAC9B,gBAAUC,OAAM,QAAQ;AAExB,eAASA,MAAK,MAAM,SAAS;AAC5B,wBAAgB,MAAMA,KAAI;AAE1B,YAAI,QAAQ,2BAA2B,OAAOA,MAAK,aAAa,OAAO,eAAeA,KAAI,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAMtH,cAAM,UAAU;AAChB,YAAI,KAAK,OAAO,YAAY,MAAM,IAAI;AACrC,gBAAM,eAAe;AACrB,gBAAM,OAAO,aAAa,MAAM,GAAG;AACnC,gBAAM,OAAO,QAAQ,QAAQ,KAAK,MAAM,KAAK,CAAC,IAAI,OAAO,MAAM,KAAK,MAAM,KAAK,SAAS,CAAC;AACzF,gBAAM,UAAU;AAAA,QACjB,WAAW,KAAK,OAAO,gBAAgB,MAAM,IAAI;AAChD,gBAAM,eAAe,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC;AACtD,gBAAM,OAAO,aAAa,MAAM,cAAc,KAAK,CAAC,CAAC;AAErD,cAAI,MAAM,KAAK,MAAM,KAAK,SAAS,CAAC,MAAM,KAAK,KAAK,SAAS,CAAC,GAAG;AAChE,kBAAM,UAAU;AAAA,UACjB,OAAO;AAEN,mBAAO,2BAA2B,KAAK;AAAA,UACxC;AAAA,QACD,OAAO;AACN,iBAAO,2BAA2B,KAAK;AAAA,QACxC;AAEA,cAAM,eAAe,QAAQ;AAG7B,YAAI,QAAQ,WAAW,QAAQ,QAAQ,IAAI;AAC1C,gBAAM,WAAW,QAAQ,QAAQ;AAAA,QAClC,OAAO;AACN,gBAAM,WAAW,QAAQ;AAAA,QAC1B;AAGA,cAAM,cAAc,QAAQ,SAAS,MAAM,WAAW,IAAI,QAAQ;AAClE,eAAO;AAAA,MACR;AAEA,mBAAaA,OAAM,CAAC;AAAA,QACnB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK;AAAA,QACb;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,cAAI,KAAK,QAAQ,MAAM;AACtB,mBAAO,KAAK,aAAa;AAAA,UAC1B,OAAO;AACN,mBAAO,KAAK,gBAAgB;AAAA,UAC7B;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,eAAe;AAC9B,cAAI,SAAS;AAEb,oBAAU;AACV,oBAAU,KAAK,mBAAmB;AAClC,oBAAU;AAEV,iBAAO;AAAA,YACN,MAAM;AAAA,YACN,MAAM,KAAK;AAAA,UACZ;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,kBAAkB;AACjC,cAAI,SAAS,CAAC;AAGd,cAAI,KAAK,cAAc;AACtB,mBAAO,KAAK;AAAA,cACX,MAAM;AAAA,cACN,MAAM,KAAK,KAAK,CAAC;AAAA,cACjB,SAAS,EAAE,WAAW,QAAQ,UAAU,KAAK,SAAS;AAAA,YACvD,CAAC;AAAA,UACF;AAGA,iBAAO,KAAK;AAAA,YACX,MAAM;AAAA,YACN,SAAS,EAAE,QAAQ,KAAK,YAAY;AAAA,UACrC,CAAC;AAGD,iBAAO,KAAK;AAAA,YACX,MAAM,KAAK,mBAAmB;AAAA,YAC9B,MAAM,KAAK,KAAK,UAAU,GAAG,CAAC;AAAA,YAC9B,SAAS,EAAE,UAAU,KAAK,SAAS;AAAA,UACpC,CAAC;AAGD,iBAAO,KAAK;AAAA,YACX,MAAM;AAAA,YACN,SAAS,EAAE,QAAQ,KAAK,YAAY;AAAA,UACrC,CAAC;AAGD,cAAI,KAAK,cAAc;AACtB,mBAAO,KAAK;AAAA,cACX,MAAM;AAAA,cACN,MAAM,KAAK,KAAK,CAAC;AAAA,cACjB,SAAS,EAAE,WAAW,SAAS,UAAU,KAAK,SAAS;AAAA,YACxD,CAAC;AAAA,UACF;AAEA,iBAAO;AAAA,QACR;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,qBAAqB;AACpC,cAAI,eAAe,KAAK,KAAK,CAAC;AAC9B,cAAI,aAAa,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC;AAC/C,cAAI,SAAS,SAAS,SAAS,UAAU,CAAC,EAAE,SAAS,YAAY,CAAC;AAClE,kBAAQ,GAAG,UAAU,SAAS,KAAK,cAAc,MAAM;AAAA,QACxD;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,UAAU,OAAO;AAEnB,aAAS,aAAa,cAAc,cAAc;AACjD,UAAI,WAAW,SAAS,aAAa,aAAa,SAAS,CAAC,CAAC;AAC7D,UAAI,YAAY,WAAW,QAAQ;AAEnC,UAAI,SAAS;AACb,UAAI,aAAa;AACjB,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,YAAI,IAAI,UAAU,CAAC;AACnB,YAAI,MAAM,KAAK;AACd,oBAAU,aAAa,YAAY;AAAA,QACpC,OAAO;AACN,oBAAU;AAAA,QACX;AAAA,MACD;AAEA,eAAS,KAAK,eAAe;AAC7B,aAAO,KAAK,UAAU,GAAG,KAAK,UAAU,MAAM;AAAA,IAC/C;AAEA,YAAQ,UAAU;AAAA;AAAA;;;ACxLlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,OAAO,QAAQ,MAAM,QAAQ,OAAO,QAAQ,OAAO,QAAQ,OAAO,QAAQ,QAAQ;AAE1F,QAAI,OAAO;AAEX,QAAI,QAAQ,uBAAuB,IAAI;AAEvC,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,QAAI,OAAO;AAEX,QAAI,QAAQ,uBAAuB,IAAI;AAEvC,QAAI,QAAQ;AAEZ,QAAI,SAAS,uBAAuB,KAAK;AAEzC,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,YAAQ,QAAQ,MAAM;AACtB,YAAQ,OAAO,MAAM;AACrB,YAAQ,OAAO,MAAM;AACrB,YAAQ,OAAO,MAAM;AACrB,YAAQ,MAAM,MAAM;AACpB,YAAQ,OAAO,OAAO;AAAA;AAAA;;;ACtCtB,IAAAC,qBAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,QAAI,YAAY,QAAQ,YAAY;AACpC,QAAI,UAAU,QAAQ,UAAU;AAEhC,QAAI,WAAW,QAAQ,WAAW,CAAC,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,OAAO;AAAA;AAAA;;;ACR3H;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,aAAa;AAEjB,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,MAAM,SAAU,UAAU;AAC7B,gBAAUC,MAAK,QAAQ;AAEvB,eAASA,OAAM;AACd,wBAAgB,MAAMA,IAAG;AAEzB,eAAO,2BAA2B,OAAOA,KAAI,aAAa,OAAO,eAAeA,IAAG,GAAG,MAAM,MAAM,SAAS,CAAC;AAAA,MAC7G;AAEA,mBAAaA,MAAK,CAAC;AAAA,QAClB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,KAAK,OAAO,eAAe,MAAM;AAAA,QAC9C;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,cAAI,SAAS;AAGb,cAAI,UAAU,KAAK,KAAK,MAAM,OAAO,EAAE,IAAI,SAAU,MAAM;AAC1D,mBAAO,OAAO,WAAW,IAAI;AAAA,UAC9B,CAAC,EAAE,KAAK,EAAE;AAEV,iBAAO;AAAA,YACN,MAAM,WAAW,YAAY,UAAU,WAAW;AAAA,YAClD,MAAM,KAAK;AAAA,UACZ;AAAA,QACD;AAAA;AAAA,MAID,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,WAAW,MAAM;AAChC,cAAI,SAAS,WAAW,SAAS,KAAK,CAAC,CAAC;AAExC,iBAAO,WAAW,SAAS,KAAK,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,SAAU,OAAO,KAAK;AACvE,oBAAQ,UAAU,MAAM,QAAQ,QAAQ,OAAO,GAAG,MAAM,MAAM,QAAQ;AAAA,UACvE,CAAC,EAAE,KAAK,EAAE;AAAA,QACX;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,UAAU,OAAO;AAEnB,YAAQ,UAAU;AAAA;AAAA;;;ACpElB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAG7e,QAAI,WAAW,SAASC,UAAS,MAAM;AACtC,UAAI,MAAM,KAAK,OAAO,GAAG,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,SAAU,KAAK;AACzD,eAAO,SAAS,KAAK,EAAE;AAAA,MACxB,CAAC,EAAE,OAAO,SAAU,KAAK,GAAG,KAAK;AAChC,eAAO,MAAM,KAAK,IAAI,MAAM,IAAI;AAAA,MACjC,GAAG,CAAC;AAEJ,aAAO,KAAK,KAAK,MAAM,EAAE,IAAI,KAAK;AAAA,IACnC;AAEA,QAAI,QAAQ,SAAU,MAAM;AAC3B,gBAAUC,QAAO,IAAI;AAErB,eAASA,OAAM,MAAM,SAAS;AAC7B,wBAAgB,MAAMA,MAAK;AAG3B,YAAI,KAAK,OAAO,aAAa,MAAM,IAAI;AACtC,kBAAQ,SAAS,IAAI;AAAA,QACtB;AACA,eAAO,2BAA2B,OAAOA,OAAM,aAAa,OAAO,eAAeA,MAAK,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,MACpH;AAEA,mBAAaA,QAAO,CAAC;AAAA,QACpB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,KAAK,OAAO,aAAa,MAAM,MAAM,CAAC,KAAK,KAAK,EAAE,MAAM,SAAS,KAAK,IAAI;AAAA,QACvF;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,MAAM,OAAO;AAEf,YAAQ,UAAU;AAAA;AAAA;;;ACtDlB,IAAAC,eAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,QAAQ,QAAQ,MAAM;AAE9B,QAAI,OAAO;AAEX,QAAI,QAAQ,uBAAuB,IAAI;AAEvC,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,YAAQ,MAAM,MAAM;AACpB,YAAQ,QAAQ,MAAM;AAAA;AAAA;;;AClBtB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAG7e,QAAI,MAAM,SAAU,UAAU;AAC7B,gBAAUC,MAAK,QAAQ;AAEvB,eAASA,KAAI,MAAM,SAAS;AAC3B,wBAAgB,MAAMA,IAAG;AAEzB,eAAO,2BAA2B,OAAOA,KAAI,aAAa,OAAO,eAAeA,IAAG,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,MAChH;AAEA,mBAAaA,MAAK,CAAC;AAAA,QAClB,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AAExB,cAAI,MAAM;AAEV,mBAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AAE1C,gBAAI,QAAQ,SAAS,KAAK,KAAK,CAAC,CAAC;AACjC,gBAAI,MAAM,MAAM,SAAS,CAAC;AAC1B,kBAAM,UAAU,KAAK,IAAI,IAAI,MAAM;AAGnC,qBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACpC,qBAAO,IAAI,CAAC,KAAK,MAAM,QAAQ;AAAA,YAChC;AAAA,UACD;AAGA,iBAAO;AAEP,iBAAO;AAAA,YACN,MAAM;AAAA,YACN,MAAM,KAAK;AAAA,UACZ;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,KAAK,OAAO,UAAU,MAAM;AAAA,QACzC;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,UAAU,OAAO;AAEnB,aAAS,UAAU,QAAQ,GAAG;AAC7B,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC3B,iBAAS,MAAM;AAAA,MAChB;AACA,aAAO;AAAA,IACR;AAEA,YAAQ,UAAU;AAAA;AAAA;;;ACzElB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,YAAQ,QAAQ;AAChB,YAAQ,QAAQ;AAChB,aAAS,MAAM,QAAQ;AACtB,UAAI,MAAM;AACV,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACvC,YAAI,IAAI,SAAS,OAAO,CAAC,CAAC;AAC1B,aAAK,IAAI,OAAO,UAAU,MAAM,GAAG;AAClC,iBAAO;AAAA,QACR,OAAO;AACN,iBAAO,IAAI,IAAI,KAAK,KAAK,MAAM,IAAI,IAAI,EAAE;AAAA,QAC1C;AAAA,MACD;AACA,cAAQ,KAAK,MAAM,MAAM;AAAA,IAC1B;AAEA,aAAS,MAAM,QAAQ;AACtB,UAAI,MAAM;AACV,UAAI,UAAU,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC/B,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACvC,YAAI,IAAI,SAAS,OAAO,OAAO,SAAS,IAAI,CAAC,CAAC;AAC9C,eAAO,QAAQ,IAAI,QAAQ,MAAM,IAAI;AAAA,MACtC;AACA,cAAQ,KAAK,MAAM,MAAM;AAAA,IAC1B;AAAA;AAAA;;;AC5BA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,QAAI,aAAa;AAEjB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,QAAQ,SAAU,MAAM;AAC3B,gBAAUC,QAAO,IAAI;AAErB,eAASA,OAAM,MAAM,SAAS;AAC7B,wBAAgB,MAAMA,MAAK;AAE3B,eAAO,2BAA2B,OAAOA,OAAM,aAAa,OAAO,eAAeA,MAAK,GAAG,KAAK,MAAM,QAAQ,GAAG,WAAW,OAAO,IAAI,GAAG,OAAO,CAAC;AAAA,MAClJ;AAEA,aAAOA;AAAA,IACR,EAAE,MAAM,OAAO;AAEf,YAAQ,UAAU;AAAA;AAAA;;;AChClB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,QAAI,aAAa;AAEjB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,QAAQ,SAAU,MAAM;AAC3B,gBAAUC,QAAO,IAAI;AAErB,eAASA,OAAM,MAAM,SAAS;AAC7B,wBAAgB,MAAMA,MAAK;AAE3B,eAAO,2BAA2B,OAAOA,OAAM,aAAa,OAAO,eAAeA,MAAK,GAAG,KAAK,MAAM,QAAQ,GAAG,WAAW,OAAO,IAAI,GAAG,OAAO,CAAC;AAAA,MAClJ;AAEA,aAAOA;AAAA,IACR,EAAE,MAAM,OAAO;AAEf,YAAQ,UAAU;AAAA;AAAA;;;AChClB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,QAAI,aAAa;AAEjB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,UAAU,SAAU,MAAM;AAC7B,gBAAUC,UAAS,IAAI;AAEvB,eAASA,SAAQ,MAAM,SAAS;AAC/B,wBAAgB,MAAMA,QAAO;AAE7B,iBAAS,GAAG,WAAW,OAAO,IAAI;AAClC,iBAAS,GAAG,WAAW,OAAO,IAAI;AAClC,eAAO,2BAA2B,OAAOA,SAAQ,aAAa,OAAO,eAAeA,QAAO,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,MACxH;AAEA,aAAOA;AAAA,IACR,EAAE,MAAM,OAAO;AAEf,YAAQ,UAAU;AAAA;AAAA;;;AClClB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,QAAI,aAAa;AAEjB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,UAAU,SAAU,MAAM;AAC7B,gBAAUC,UAAS,IAAI;AAEvB,eAASA,SAAQ,MAAM,SAAS;AAC/B,wBAAgB,MAAMA,QAAO;AAE7B,iBAAS,GAAG,WAAW,OAAO,IAAI;AAClC,iBAAS,GAAG,WAAW,OAAO,IAAI;AAClC,eAAO,2BAA2B,OAAOA,SAAQ,aAAa,OAAO,eAAeA,QAAO,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,MACxH;AAEA,aAAOA;AAAA,IACR,EAAE,MAAM,OAAO;AAEf,YAAQ,UAAU;AAAA;AAAA;;;AClClB,IAAAC,eAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU,QAAQ,UAAU,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAElF,QAAI,OAAO;AAEX,QAAI,QAAQ,uBAAuB,IAAI;AAEvC,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,QAAI,QAAQ;AAEZ,QAAI,QAAQ,uBAAuB,KAAK;AAExC,QAAI,QAAQ;AAEZ,QAAI,SAAS,uBAAuB,KAAK;AAEzC,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,YAAQ,MAAM,MAAM;AACpB,YAAQ,QAAQ,MAAM;AACtB,YAAQ,QAAQ,MAAM;AACtB,YAAQ,UAAU,MAAM;AACxB,YAAQ,UAAU,OAAO;AAAA;AAAA;;;ACjCzB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,YAAQ,aAAa;AAErB,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAG7e,QAAI,aAAa,SAAU,UAAU;AACpC,gBAAUC,aAAY,QAAQ;AAE9B,eAASA,YAAW,MAAM,SAAS;AAClC,wBAAgB,MAAMA,WAAU;AAEhC,YAAI,QAAQ,2BAA2B,OAAOA,YAAW,aAAa,OAAO,eAAeA,WAAU,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAElI,cAAM,SAAS,SAAS,MAAM,EAAE;AAChC,eAAO;AAAA,MACR;AAEA,mBAAaA,aAAY,CAAC;AAAA,QACzB,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,cAAI,IAAI,KAAK;AACb,cAAI,SAAS;AAIb,iBAAO,CAAC,MAAM,CAAC,KAAK,KAAK,GAAG;AAC3B,gBAAI,IAAI,MAAM,GAAG;AAEhB,uBAAS,UAAU;AACnB,mBAAK,IAAI,KAAK;AAAA,YACf,OAAO;AAEN,uBAAS,QAAQ;AACjB,mBAAK,IAAI,KAAK;AAAA,YACf;AAAA,UACD;AAGA,mBAAS,OAAO,MAAM,GAAG,EAAE;AAE3B,iBAAO;AAAA,YACN,MAAM;AAAA,YACN,MAAM,KAAK;AAAA,UACZ;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,UAAU,KAAK,KAAK,UAAU;AAAA,QAC3C;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,UAAU,OAAO;AAEnB,YAAQ,aAAa;AAAA;AAAA;;;ACxErB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,YAAQ,UAAU;AAElB,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAG7e,QAAI,UAAU,SAAU,UAAU;AACjC,gBAAUC,UAAS,QAAQ;AAE3B,eAASA,SAAQ,MAAM,SAAS;AAC/B,wBAAgB,MAAMA,QAAO;AAE7B,YAAI,KAAK,OAAO,sBAAsB,MAAM,GAAG;AAC9C,iBAAO,MAAM,OAAO;AAAA,QACrB;AAEA,YAAI,QAAQ,2BAA2B,OAAOA,SAAQ,aAAa,OAAO,eAAeA,QAAO,GAAG,KAAK,MAAM,KAAK,YAAY,GAAG,OAAO,CAAC;AAE1I,cAAM,OAAO,MAAM,QAAQ,QAAQ,MAAM,KAAK,QAAQ,UAAU,EAAE;AAClE,eAAO;AAAA,MACR;AAEA,mBAAaA,UAAS,CAAC;AAAA,QACtB,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO,KAAK,KAAK,OAAO,gCAAgC,MAAM;AAAA,QAC/D;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,cAAI,SAAS,CAAC;AACd,cAAI,YAAY,KAAK,aAAa;AAClC,mBAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AAC1C,mBAAO,KAAK,UAAU,KAAK,KAAK,OAAO,CAAC,CAAC,CAAC;AAE1C,gBAAI,MAAM,KAAK,KAAK,SAAS,GAAG;AAC/B,qBAAO,KAAK,GAAG;AAAA,YAChB;AAAA,UACD;AACA,iBAAO;AAAA,YACN,MAAM,KAAK;AAAA,YACX,MAAM,OAAO,KAAK,EAAE;AAAA,UACrB;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,eAAe;AAC9B,iBAAO;AAAA,YACN,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,UACN;AAAA,QACD;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,UAAU,OAAO;AAEnB,YAAQ,UAAU;AAAA;AAAA;;;AC3FlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,YAAQ,iBAAiB;AAEzB,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,YAAY;AAEhB,QAAI,YAAY,uBAAuB,SAAS;AAEhD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,iBAAiB,SAAU,UAAU;AACxC,gBAAUC,iBAAgB,QAAQ;AAElC,eAASA,gBAAe,MAAM,SAAS;AACtC,wBAAgB,MAAMA,eAAc;AAEpC,eAAO,2BAA2B,OAAOA,gBAAe,aAAa,OAAO,eAAeA,eAAc,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,MACtI;AAKA,mBAAaA,iBAAgB,CAAC;AAAA,QAC7B,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,iBAAO;AAAA,YACN,MAAM;AAAA,YACN,MAAM,KAAK;AAAA,UACZ;AAAA,QACD;AAAA;AAAA,MAID,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,QAAQ;AACvB,iBAAO;AAAA,QACR;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE,UAAU,OAAO;AAEnB,YAAQ,iBAAiB;AAAA;AAAA;;;ACtDzB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,QAAQ;AAEZ,QAAI,SAAS;AAEb,QAAI,WAAW;AAEf,QAAI,OAAO;AAEX,QAAI,OAAO;AAEX,QAAI,cAAc;AAElB,QAAI,WAAW;AAEf,QAAI,kBAAkB;AAEtB,YAAQ,UAAU;AAAA,MACjB,QAAQ,MAAM;AAAA,MACd,SAAS,OAAO;AAAA,MAAS,UAAU,OAAO;AAAA,MAAU,UAAU,OAAO;AAAA,MAAU,UAAU,OAAO;AAAA,MAChG,OAAO,SAAS;AAAA,MAAO,MAAM,SAAS;AAAA,MAAM,MAAM,SAAS;AAAA,MAAM,MAAM,SAAS;AAAA,MAAM,KAAK,SAAS;AAAA,MAAK,MAAM,SAAS;AAAA,MACxH,OAAO,KAAK;AAAA,MACZ,KAAK,KAAK;AAAA,MACV,KAAK,KAAK;AAAA,MAAK,OAAO,KAAK;AAAA,MAAO,OAAO,KAAK;AAAA,MAAO,SAAS,KAAK;AAAA,MAAS,SAAS,KAAK;AAAA,MAC1F,YAAY,YAAY;AAAA,MACxB,SAAS,SAAS;AAAA,MAClB,gBAAgB,gBAAgB;AAAA,IACjC;AAAA;AAAA;;;AChCA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAED,QAAI,WAAW,OAAO,UAAU,SAAU,QAAQ;AAAE,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,YAAI,SAAS,UAAU,CAAC;AAAG,iBAAS,OAAO,QAAQ;AAAE,cAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,mBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,UAAG;AAAA,QAAE;AAAA,MAAE;AAAE,aAAO;AAAA,IAAQ;AAE/P,YAAQ,UAAU,SAAU,KAAK,YAAY;AAC3C,aAAO,SAAS,CAAC,GAAG,KAAK,UAAU;AAAA,IACrC;AAAA;AAAA;;;ACVA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,YAAQ,UAAU;AAKlB,aAAS,mBAAmB,WAAW;AACtC,UAAI,kBAAkB,CAAC;AACvB,eAAS,UAAU,SAAS;AAC3B,YAAI,MAAM,QAAQ,OAAO,GAAG;AAC3B,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACxC,sBAAU,QAAQ,CAAC,CAAC;AAAA,UACrB;AAAA,QACD,OAAO;AACN,kBAAQ,OAAO,QAAQ,QAAQ;AAC/B,kBAAQ,OAAO,QAAQ,QAAQ;AAC/B,0BAAgB,KAAK,OAAO;AAAA,QAC7B;AAAA,MACD;AACA,gBAAU,SAAS;AAEnB,aAAO;AAAA,IACR;AAAA;AAAA;;;AC1BA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,YAAQ,UAAU;AAGlB,aAAS,WAAW,SAAS;AAE5B,cAAQ,YAAY,QAAQ,aAAa,QAAQ;AACjD,cAAQ,eAAe,QAAQ,gBAAgB,QAAQ;AACvD,cAAQ,cAAc,QAAQ,eAAe,QAAQ;AACrD,cAAQ,aAAa,QAAQ,cAAc,QAAQ;AAEnD,aAAO;AAAA,IACR;AAAA;AAAA;;;AChBA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,YAAQ,UAAU;AAIlB,aAAS,mBAAmB,SAAS;AACpC,UAAI,aAAa,CAAC,SAAS,UAAU,cAAc,YAAY,UAAU,aAAa,gBAAgB,cAAc,aAAa;AAEjI,eAAS,aAAa,YAAY;AACjC,YAAI,WAAW,eAAe,SAAS,GAAG;AACzC,sBAAY,WAAW,SAAS;AAChC,cAAI,OAAO,QAAQ,SAAS,MAAM,UAAU;AAC3C,oBAAQ,SAAS,IAAI,SAAS,QAAQ,SAAS,GAAG,EAAE;AAAA,UACrD;AAAA,QACD;AAAA,MACD;AAEA,UAAI,OAAO,QAAQ,cAAc,MAAM,UAAU;AAChD,gBAAQ,cAAc,IAAI,QAAQ,cAAc,KAAK;AAAA,MACtD;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;;;AC1BA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,QAAI,WAAW;AAAA,MACd,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,aAAa;AAAA,MACb,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,MACX,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,OAAO,SAAS,QAAQ;AAAA,MAAC;AAAA,IAC1B;AAEA,YAAQ,UAAU;AAAA;AAAA;;;AC3BlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,sBAAsB;AAE1B,QAAI,uBAAuB,uBAAuB,mBAAmB;AAErE,QAAI,YAAY;AAEhB,QAAI,aAAa,uBAAuB,SAAS;AAEjD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,sBAAsB,SAAS;AACvC,UAAI,UAAU,CAAC;AACf,eAAS,YAAY,WAAW,SAAS;AACxC,YAAI,WAAW,QAAQ,eAAe,QAAQ,GAAG;AAEhD,cAAI,QAAQ,aAAa,eAAe,SAAS,YAAY,CAAC,GAAG;AAChE,oBAAQ,QAAQ,IAAI,QAAQ,aAAa,eAAe,SAAS,YAAY,CAAC;AAAA,UAC/E;AAGA,cAAI,QAAQ,aAAa,UAAU,SAAS,YAAY,CAAC,GAAG;AAC3D,oBAAQ,QAAQ,IAAI,QAAQ,aAAa,UAAU,SAAS,YAAY,CAAC;AAAA,UAC1E;AAAA,QACD;AAAA,MACD;AAEA,cAAQ,OAAO,IAAI,QAAQ,aAAa,iBAAiB,KAAK,QAAQ,aAAa,YAAY;AAG/F,iBAAW,GAAG,qBAAqB,SAAS,OAAO;AAEnD,aAAO;AAAA,IACR;AAEA,YAAQ,UAAU;AAAA;AAAA;;;ACxClB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AACD,YAAQ,2BAA2B,QAAQ,8BAA8B,QAAQ,oBAAoB,QAAQ,oBAAoB,QAAQ,8BAA8B;AAEvK,QAAI,SAAS;AAEb,QAAI,UAAU,uBAAuB,MAAM;AAE3C,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,kBAAkB,UAAU,SAAS;AAC7C,aAAO,QAAQ,UAAU,QAAQ,gBAAgB,SAAS,KAAK,SAAS,IAAI,QAAQ,WAAW,QAAQ,aAAa,KAAK,QAAQ,YAAY,QAAQ;AAAA,IACtJ;AAEA,aAAS,kBAAkB,WAAW,cAAc,SAAS;AAC5D,UAAI,QAAQ,gBAAgB,eAAe,WAAW;AACrD,YAAI,QAAQ,aAAa,UAAU;AAClC,iBAAO,KAAK,OAAO,YAAY,gBAAgB,CAAC;AAAA,QACjD,WAAW,QAAQ,aAAa,QAAQ;AACvC,iBAAO;AAAA,QACR,WAAW,QAAQ,aAAa,SAAS;AACxC,iBAAO,KAAK,MAAM,YAAY,YAAY;AAAA,QAC3C;AAAA,MACD;AACA,aAAO;AAAA,IACR;AAEA,aAAS,4BAA4B,WAAW,gBAAgB,SAAS;AACxE,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,YAAI,WAAW,UAAU,CAAC;AAC1B,YAAI,WAAW,GAAG,QAAQ,SAAS,gBAAgB,SAAS,OAAO;AAGnE,YAAI;AACJ,YAAI,QAAQ,cAAc;AACzB,sBAAY,YAAY,SAAS,MAAM,SAAS,OAAO;AAAA,QACxD,OAAO;AACN,sBAAY;AAAA,QACb;AAEA,YAAI,eAAe,SAAS,KAAK,SAAS,QAAQ;AAClD,iBAAS,QAAQ,KAAK,KAAK,KAAK,IAAI,WAAW,YAAY,CAAC;AAE5D,iBAAS,SAAS,kBAAkB,UAAU,OAAO;AAErD,iBAAS,iBAAiB,kBAAkB,WAAW,cAAc,OAAO;AAAA,MAC7E;AAAA,IACD;AAEA,aAAS,yBAAyB,WAAW;AAC5C,UAAI,aAAa;AACjB,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,sBAAc,UAAU,CAAC,EAAE;AAAA,MAC5B;AACA,aAAO;AAAA,IACR;AAEA,aAAS,4BAA4B,WAAW;AAC/C,UAAI,YAAY;AAChB,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,YAAI,UAAU,CAAC,EAAE,SAAS,WAAW;AACpC,sBAAY,UAAU,CAAC,EAAE;AAAA,QAC1B;AAAA,MACD;AACA,aAAO;AAAA,IACR;AAEA,aAAS,YAAY,QAAQ,SAAS,SAAS;AAC9C,UAAI;AAEJ,UAAI,SAAS;AACZ,cAAM;AAAA,MACP,WAAW,OAAO,aAAa,aAAa;AAC3C,cAAM,SAAS,cAAc,QAAQ,EAAE,WAAW,IAAI;AAAA,MACvD,OAAO;AAGN,eAAO;AAAA,MACR;AACA,UAAI,OAAO,QAAQ,cAAc,MAAM,QAAQ,WAAW,QAAQ,QAAQ;AAG1E,UAAI,oBAAoB,IAAI,YAAY,MAAM;AAC9C,UAAI,CAAC,mBAAmB;AAIvB,eAAO;AAAA,MACR;AACA,UAAI,OAAO,kBAAkB;AAC7B,aAAO;AAAA,IACR;AAEA,YAAQ,8BAA8B;AACtC,YAAQ,oBAAoB;AAC5B,YAAQ,oBAAoB;AAC5B,YAAQ,8BAA8B;AACtC,YAAQ,2BAA2B;AAAA;AAAA;;;ACpGnC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,SAAS;AAEb,QAAI,UAAU,uBAAuB,MAAM;AAE3C,QAAI,UAAU;AAEd,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,QAAI,iBAAiB,WAAY;AAChC,eAASC,gBAAe,QAAQ,WAAW,SAAS;AACnD,wBAAgB,MAAMA,eAAc;AAEpC,aAAK,SAAS;AACd,aAAK,YAAY;AACjB,aAAK,UAAU;AAAA,MAChB;AAEA,mBAAaA,iBAAgB,CAAC;AAAA,QAC7B,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AAExB,cAAI,CAAC,KAAK,OAAO,YAAY;AAC5B,kBAAM,IAAI,MAAM,sCAAsC;AAAA,UACvD;AAEA,eAAK,cAAc;AACnB,mBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC/C,gBAAI,mBAAmB,GAAG,QAAQ,SAAS,KAAK,SAAS,KAAK,UAAU,CAAC,EAAE,OAAO;AAElF,iBAAK,kBAAkB,iBAAiB,KAAK,UAAU,CAAC,CAAC;AACzD,iBAAK,eAAe,iBAAiB,KAAK,UAAU,CAAC,CAAC;AAEtD,iBAAK,kBAAkB,KAAK,UAAU,CAAC,CAAC;AAAA,UACzC;AAEA,eAAK,cAAc;AAAA,QACpB;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,gBAAgB;AAE/B,cAAI,MAAM,KAAK,OAAO,WAAW,IAAI;AAErC,cAAI,KAAK;AAET,WAAC,GAAG,QAAQ,6BAA6B,KAAK,WAAW,KAAK,SAAS,GAAG;AAC1E,cAAI,cAAc,GAAG,QAAQ,0BAA0B,KAAK,SAAS;AACrE,cAAI,aAAa,GAAG,QAAQ,6BAA6B,KAAK,SAAS;AAEvE,eAAK,OAAO,QAAQ,aAAa,KAAK,QAAQ,aAAa,KAAK,QAAQ;AAExE,eAAK,OAAO,SAAS;AAGrB,cAAI,UAAU,GAAG,GAAG,KAAK,OAAO,OAAO,KAAK,OAAO,MAAM;AACzD,cAAI,KAAK,QAAQ,YAAY;AAC5B,gBAAI,YAAY,KAAK,QAAQ;AAC7B,gBAAI,SAAS,GAAG,GAAG,KAAK,OAAO,OAAO,KAAK,OAAO,MAAM;AAAA,UACzD;AAEA,cAAI,UAAU,KAAK,QAAQ,YAAY,CAAC;AAAA,QACzC;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,kBAAkB,SAAS,UAAU;AAEpD,cAAI,MAAM,KAAK,OAAO,WAAW,IAAI;AAErC,cAAI,SAAS,SAAS;AAGtB,cAAI;AACJ,cAAI,QAAQ,gBAAgB,OAAO;AAClC,oBAAQ,QAAQ,YAAY,QAAQ,WAAW,QAAQ;AAAA,UACxD,OAAO;AACN,oBAAQ,QAAQ;AAAA,UACjB;AAEA,cAAI,YAAY,QAAQ;AAExB,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACvC,gBAAI,IAAI,IAAI,QAAQ,QAAQ,SAAS;AAErC,gBAAI,OAAO,CAAC,MAAM,KAAK;AACtB,kBAAI,SAAS,GAAG,OAAO,QAAQ,OAAO,QAAQ,MAAM;AAAA,YACrD,WAAW,OAAO,CAAC,GAAG;AACrB,kBAAI,SAAS,GAAG,OAAO,QAAQ,OAAO,QAAQ,SAAS,OAAO,CAAC,CAAC;AAAA,YACjE;AAAA,UACD;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,eAAe,SAAS,UAAU;AAEjD,cAAI,MAAM,KAAK,OAAO,WAAW,IAAI;AAErC,cAAI,OAAO,QAAQ,cAAc,MAAM,QAAQ,WAAW,QAAQ,QAAQ;AAG1E,cAAI,QAAQ,cAAc;AACzB,gBAAI,GAAG;AAEP,gBAAI,QAAQ,gBAAgB,OAAO;AAClC,kBAAI,QAAQ,YAAY,QAAQ,WAAW,QAAQ;AAAA,YACpD,OAAO;AACN,kBAAI,QAAQ,SAAS,QAAQ,aAAa,QAAQ,YAAY,QAAQ;AAAA,YACvE;AAEA,gBAAI,OAAO;AAGX,gBAAI,QAAQ,aAAa,UAAU,SAAS,iBAAiB,GAAG;AAC/D,kBAAI;AACJ,kBAAI,YAAY;AAAA,YACjB,WAAW,QAAQ,aAAa,SAAS;AACxC,kBAAI,SAAS,QAAQ;AACrB,kBAAI,YAAY;AAAA,YACjB,OAEK;AACH,kBAAI,SAAS,QAAQ;AACrB,kBAAI,YAAY;AAAA,YACjB;AAED,gBAAI,SAAS,SAAS,MAAM,GAAG,CAAC;AAAA,UACjC;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,kBAAkB,UAAU;AAC3C,cAAI,MAAM,KAAK,OAAO,WAAW,IAAI;AAErC,cAAI,UAAU,SAAS,OAAO,CAAC;AAAA,QAChC;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,gBAAgB;AAE/B,cAAI,MAAM,KAAK,OAAO,WAAW,IAAI;AAErC,cAAI,QAAQ;AAAA,QACb;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE;AAEF,YAAQ,UAAU;AAAA;AAAA;;;AC7JlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,QAAI,SAAS;AAEb,QAAI,UAAU,uBAAuB,MAAM;AAE3C,QAAI,UAAU;AAEd,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,QAAI,QAAQ;AAEZ,QAAI,cAAc,WAAY;AAC7B,eAASC,aAAY,KAAK,WAAW,SAAS;AAC7C,wBAAgB,MAAMA,YAAW;AAEjC,aAAK,MAAM;AACX,aAAK,YAAY;AACjB,aAAK,UAAU;AACf,aAAK,WAAW,QAAQ,eAAe;AAAA,MACxC;AAEA,mBAAaA,cAAa,CAAC;AAAA,QAC1B,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,cAAI,WAAW,KAAK,QAAQ;AAE5B,eAAK,WAAW;AAChB,mBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC/C,gBAAI,WAAW,KAAK,UAAU,CAAC;AAC/B,gBAAI,mBAAmB,GAAG,QAAQ,SAAS,KAAK,SAAS,SAAS,OAAO;AAEzE,gBAAI,QAAQ,KAAK,YAAY,UAAU,gBAAgB,WAAW,KAAK,GAAG;AAE1E,iBAAK,gBAAgB,OAAO,eAAe;AAE3C,iBAAK,eAAe,OAAO,iBAAiB,QAAQ;AACpD,iBAAK,YAAY,OAAO,iBAAiB,QAAQ;AAEjD,wBAAY,SAAS;AAAA,UACtB;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,aAAa;AAE5B,iBAAO,KAAK,IAAI,YAAY;AAC3B,iBAAK,IAAI,YAAY,KAAK,IAAI,UAAU;AAAA,UACzC;AAEA,WAAC,GAAG,QAAQ,6BAA6B,KAAK,WAAW,KAAK,OAAO;AACrE,cAAI,cAAc,GAAG,QAAQ,0BAA0B,KAAK,SAAS;AACrE,cAAI,aAAa,GAAG,QAAQ,6BAA6B,KAAK,SAAS;AAEvE,cAAI,QAAQ,aAAa,KAAK,QAAQ,aAAa,KAAK,QAAQ;AAChE,eAAK,iBAAiB,OAAO,SAAS;AAEtC,cAAI,KAAK,QAAQ,YAAY;AAC5B,iBAAK,SAAS,GAAG,GAAG,OAAO,WAAW,KAAK,GAAG,EAAE,aAAa,SAAS,UAAU,KAAK,QAAQ,aAAa,GAAG;AAAA,UAC9G;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,eAAe,QAAQ,SAAS,UAAU;AACzD,cAAI,SAAS,SAAS;AAGtB,cAAI;AACJ,cAAI,QAAQ,gBAAgB,OAAO;AAClC,oBAAQ,QAAQ,WAAW,QAAQ;AAAA,UACpC,OAAO;AACN,oBAAQ;AAAA,UACT;AAEA,cAAI,WAAW;AACf,cAAI,IAAI;AACR,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACvC,gBAAI,IAAI,QAAQ,QAAQ,SAAS;AAEjC,gBAAI,OAAO,CAAC,MAAM,KAAK;AACtB;AAAA,YACD,WAAW,WAAW,GAAG;AACxB,mBAAK,SAAS,IAAI,QAAQ,QAAQ,UAAU,OAAO,QAAQ,QAAQ,UAAU,QAAQ,QAAQ,MAAM;AACnG,yBAAW;AAAA,YACZ;AAAA,UACD;AAGA,cAAI,WAAW,GAAG;AACjB,iBAAK,SAAS,IAAI,QAAQ,SAAS,WAAW,IAAI,OAAO,QAAQ,QAAQ,UAAU,QAAQ,QAAQ,MAAM;AAAA,UAC1G;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,YAAY,QAAQ,SAAS,UAAU;AACtD,cAAI,WAAW,KAAK,SAAS,gBAAgB,OAAO,MAAM;AAG1D,cAAI,QAAQ,cAAc;AACzB,gBAAI,GAAG;AAEP,qBAAS,aAAa,SAAS,UAAU,QAAQ,cAAc,MAAM,QAAQ,WAAW,QAAQ,QAAQ,IAAI;AAE5G,gBAAI,QAAQ,gBAAgB,OAAO;AAClC,kBAAI,QAAQ,WAAW,QAAQ;AAAA,YAChC,OAAO;AACN,kBAAI,QAAQ,SAAS,QAAQ,aAAa,QAAQ;AAAA,YACnD;AAGA,gBAAI,QAAQ,aAAa,UAAU,SAAS,iBAAiB,GAAG;AAC/D,kBAAI;AACJ,uBAAS,aAAa,eAAe,OAAO;AAAA,YAC7C,WAAW,QAAQ,aAAa,SAAS;AACxC,kBAAI,SAAS,QAAQ;AACrB,uBAAS,aAAa,eAAe,KAAK;AAAA,YAC3C,OAEK;AACH,kBAAI,SAAS,QAAQ;AACrB,uBAAS,aAAa,eAAe,QAAQ;AAAA,YAC9C;AAED,qBAAS,aAAa,KAAK,CAAC;AAC5B,qBAAS,aAAa,KAAK,CAAC;AAE5B,qBAAS,YAAY,KAAK,SAAS,eAAe,SAAS,IAAI,CAAC;AAEhE,mBAAO,YAAY,QAAQ;AAAA,UAC5B;AAAA,QACD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,iBAAiB,OAAO,QAAQ;AAC/C,cAAI,MAAM,KAAK;AACf,cAAI,aAAa,SAAS,QAAQ,IAAI;AACtC,cAAI,aAAa,UAAU,SAAS,IAAI;AACxC,cAAI,aAAa,KAAK,KAAK;AAC3B,cAAI,aAAa,KAAK,KAAK;AAC3B,cAAI,aAAa,WAAW,SAAS,QAAQ,MAAM,MAAM;AAEzD,cAAI,aAAa,SAAS,KAAK;AAC/B,cAAI,aAAa,WAAW,KAAK;AAEjC,cAAI,aAAa,SAAS,2BAA2B;AAAA,QACtD;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,YAAY,GAAG,GAAG,QAAQ;AACzC,cAAI,QAAQ,KAAK,SAAS,gBAAgB,OAAO,GAAG;AACpD,gBAAM,aAAa,aAAa,eAAe,IAAI,OAAO,IAAI,GAAG;AAEjE,iBAAO,YAAY,KAAK;AAExB,iBAAO;AAAA,QACR;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,gBAAgB,OAAO,SAAS;AAC/C,gBAAM,aAAa,SAAS,UAAU,QAAQ,YAAY,GAAG;AAAA,QAC9D;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,SAAS,GAAG,GAAG,OAAO,QAAQ,QAAQ;AACrD,cAAI,OAAO,KAAK,SAAS,gBAAgB,OAAO,MAAM;AAEtD,eAAK,aAAa,KAAK,CAAC;AACxB,eAAK,aAAa,KAAK,CAAC;AACxB,eAAK,aAAa,SAAS,KAAK;AAChC,eAAK,aAAa,UAAU,MAAM;AAElC,iBAAO,YAAY,IAAI;AAEvB,iBAAO;AAAA,QACR;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE;AAEF,YAAQ,UAAU;AAAA;AAAA;;;AC5LlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,QAAI,iBAAiB,WAAY;AAChC,eAASC,gBAAe,QAAQ,WAAW,SAAS;AACnD,wBAAgB,MAAMA,eAAc;AAEpC,aAAK,SAAS;AACd,aAAK,YAAY;AACjB,aAAK,UAAU;AAAA,MAChB;AAEA,mBAAaA,iBAAgB,CAAC;AAAA,QAC7B,KAAK;AAAA,QACL,OAAO,SAAS,SAAS;AACxB,eAAK,OAAO,YAAY,KAAK;AAAA,QAC9B;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE;AAEF,YAAQ,UAAU;AAAA;AAAA;;;AC7BlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAED,QAAI,UAAU;AAEd,QAAI,WAAW,uBAAuB,OAAO;AAE7C,QAAI,OAAO;AAEX,QAAI,QAAQ,uBAAuB,IAAI;AAEvC,QAAI,UAAU;AAEd,QAAI,WAAW,uBAAuB,OAAO;AAE7C,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,YAAQ,UAAU,EAAE,gBAAgB,SAAS,SAAS,aAAa,MAAM,SAAS,gBAAgB,SAAS,QAAQ;AAAA;AAAA;;;ACpBnH;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,2BAA2B,MAAM,MAAM;AAAE,UAAI,CAAC,MAAM;AAAE,cAAM,IAAI,eAAe,2DAA2D;AAAA,MAAG;AAAE,aAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AAAA,IAAM;AAE/O,aAAS,UAAU,UAAU,YAAY;AAAE,UAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAAE,cAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,MAAG;AAAE,eAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW,EAAE,aAAa,EAAE,OAAO,UAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAK,EAAE,CAAC;AAAG,UAAI,WAAY,QAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAAA,IAAY;AAE7e,QAAI,wBAAwB,SAAU,QAAQ;AAC7C,gBAAUC,wBAAuB,MAAM;AAEvC,eAASA,uBAAsB,WAAW,OAAO;AAChD,wBAAgB,MAAMA,sBAAqB;AAE3C,YAAI,QAAQ,2BAA2B,OAAOA,uBAAsB,aAAa,OAAO,eAAeA,sBAAqB,GAAG,KAAK,IAAI,CAAC;AAEzI,cAAM,OAAO;AAEb,cAAM,YAAY;AAClB,cAAM,QAAQ;AAEd,cAAM,UAAU,MAAM,MAAM,QAAQ,gCAAgC,MAAM;AAC1E,eAAO;AAAA,MACR;AAEA,aAAOA;AAAA,IACR,EAAE,KAAK;AAEP,QAAI,0BAA0B,SAAU,SAAS;AAChD,gBAAUC,0BAAyB,OAAO;AAE1C,eAASA,2BAA0B;AAClC,wBAAgB,MAAMA,wBAAuB;AAE7C,YAAI,SAAS,2BAA2B,OAAOA,yBAAwB,aAAa,OAAO,eAAeA,wBAAuB,GAAG,KAAK,IAAI,CAAC;AAE9I,eAAO,OAAO;AACd,eAAO,UAAU;AACjB,eAAO;AAAA,MACR;AAEA,aAAOA;AAAA,IACR,EAAE,KAAK;AAEP,QAAI,qBAAqB,SAAU,SAAS;AAC3C,gBAAUC,qBAAoB,OAAO;AAErC,eAASA,sBAAqB;AAC7B,wBAAgB,MAAMA,mBAAkB;AAExC,YAAI,SAAS,2BAA2B,OAAOA,oBAAmB,aAAa,OAAO,eAAeA,mBAAkB,GAAG,KAAK,IAAI,CAAC;AAEpI,eAAO,OAAO;AACd,eAAO,UAAU;AACjB,eAAO;AAAA,MACR;AAEA,aAAOA;AAAA,IACR,EAAE,KAAK;AAEP,YAAQ,wBAAwB;AAChC,YAAQ,0BAA0B;AAClC,YAAQ,qBAAqB;AAAA;AAAA;;;AClE7B;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,UAAU,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,WAAW,SAAU,KAAK;AAAE,aAAO,OAAO;AAAA,IAAK,IAAI,SAAU,KAAK;AAAE,aAAO,OAAO,OAAO,WAAW,cAAc,IAAI,gBAAgB,UAAU,QAAQ,OAAO,YAAY,WAAW,OAAO;AAAA,IAAK;AAI3Q,QAAI,yBAAyB;AAE7B,QAAI,0BAA0B,uBAAuB,sBAAsB;AAE3E,QAAI,aAAa;AAEjB,QAAI,cAAc,uBAAuB,UAAU;AAEnD,QAAI,cAAc;AAElB,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAa9F,aAAS,oBAAoB,SAAS;AAErC,UAAI,OAAO,YAAY,UAAU;AAChC,eAAO,8BAA8B,OAAO;AAAA,MAC7C,WAES,MAAM,QAAQ,OAAO,GAAG;AAC/B,YAAI,cAAc,CAAC;AACnB,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACxC,sBAAY,KAAK,oBAAoB,QAAQ,CAAC,CAAC,CAAC;AAAA,QACjD;AACA,eAAO;AAAA,MACR,WAES,OAAO,sBAAsB,eAAe,mBAAmB,kBAAkB;AACxF,eAAO,0BAA0B,OAAO;AAAA,MACzC,WAES,WAAW,QAAQ,YAAY,QAAQ,SAAS,YAAY,MAAM,SAAS,OAAO,eAAe,eAAe,mBAAmB,YAAY;AACtJ,eAAO;AAAA,UACN;AAAA,UACA,UAAU,GAAG,wBAAwB,SAAS,OAAO;AAAA,UACrD,UAAU,YAAY,QAAQ;AAAA,QAC/B;AAAA,MACD,WAES,OAAO,sBAAsB,eAAe,mBAAmB,mBAAmB;AACzF,eAAO;AAAA,UACN;AAAA,UACA,UAAU,GAAG,wBAAwB,SAAS,OAAO;AAAA,UACrD,UAAU,YAAY,QAAQ;AAAA,QAC/B;AAAA,MACD,WAES,WAAW,QAAQ,YAAY;AACtC,eAAO;AAAA,UACN;AAAA,UACA,UAAU,YAAY,QAAQ;AAAA,QAC/B;AAAA,MACD,WAAW,YAAY,OAAO,YAAY,cAAc,cAAc,QAAQ,OAAO,OAAO,YAAY,CAAC,QAAQ,UAAU;AAC1H,eAAO;AAAA,UACN;AAAA,UACA,UAAU,YAAY,QAAQ;AAAA,QAC/B;AAAA,MACD,OAAO;AACN,cAAM,IAAI,YAAY,wBAAwB;AAAA,MAC/C;AAAA,IACN;AAEA,aAAS,8BAA8B,QAAQ;AAC9C,UAAI,WAAW,SAAS,iBAAiB,MAAM;AAC/C,UAAI,SAAS,WAAW,GAAG;AAC1B,eAAO;AAAA,MACR,OAAO;AACN,YAAI,cAAc,CAAC;AACnB,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACzC,sBAAY,KAAK,oBAAoB,SAAS,CAAC,CAAC,CAAC;AAAA,QAClD;AACA,eAAO;AAAA,MACR;AAAA,IACD;AAEA,aAAS,0BAA0B,YAAY;AAC9C,UAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,aAAO;AAAA,QACN,SAAS;AAAA,QACT,UAAU,GAAG,wBAAwB,SAAS,UAAU;AAAA,QACxD,UAAU,YAAY,QAAQ;AAAA,QAC9B,aAAa,SAAS,cAAc;AACnC,qBAAW,aAAa,OAAO,OAAO,UAAU,CAAC;AAAA,QAClD;AAAA,MACD;AAAA,IACD;AAEA,YAAQ,UAAU;AAAA;AAAA;;;AC3GlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC5C,OAAO;AAAA,IACR,CAAC;AAED,QAAI,eAAe,2BAAY;AAAE,eAAS,iBAAiB,QAAQ,OAAO;AAAE,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,cAAI,aAAa,MAAM,CAAC;AAAG,qBAAW,aAAa,WAAW,cAAc;AAAO,qBAAW,eAAe;AAAM,cAAI,WAAW,WAAY,YAAW,WAAW;AAAM,iBAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,QAAG;AAAA,MAAE;AAAE,aAAO,SAAU,aAAa,YAAY,aAAa;AAAE,YAAI,WAAY,kBAAiB,YAAY,WAAW,UAAU;AAAG,YAAI,YAAa,kBAAiB,aAAa,WAAW;AAAG,eAAO;AAAA,MAAa;AAAA,IAAG,EAAE;AAEljB,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAIxJ,QAAI,eAAe,WAAY;AAC9B,eAASC,cAAa,KAAK;AAC1B,wBAAgB,MAAMA,aAAY;AAElC,aAAK,MAAM;AAAA,MACZ;AAEA,mBAAaA,eAAc,CAAC;AAAA,QAC3B,KAAK;AAAA,QACL,OAAO,SAAS,YAAY,GAAG;AAE9B,cAAI,EAAE,SAAS,yBAAyB;AACvC,gBAAI,KAAK,IAAI,SAAS,UAAU,KAAK,IAAI,UAAU,OAAO;AACzD,mBAAK,IAAI,SAAS,MAAM,KAAK;AAAA,YAC9B,OAAO;AACN,oBAAM,EAAE;AAAA,YACT;AAAA,UACD,OAAO;AACN,kBAAM;AAAA,UACP;AAEA,eAAK,IAAI,SAAS,WAAY;AAAA,UAAC;AAAA,QAChC;AAAA,MACD,GAAG;AAAA,QACF,KAAK;AAAA,QACL,OAAO,SAAS,gBAAgB,MAAM;AACrC,cAAI;AACH,gBAAI,SAAS,KAAK,MAAM,QAAW,SAAS;AAC5C,iBAAK,IAAI,SAAS,MAAM,IAAI;AAC5B,mBAAO;AAAA,UACR,SAAS,GAAG;AACX,iBAAK,YAAY,CAAC;AAElB,mBAAO,KAAK;AAAA,UACb;AAAA,QACD;AAAA,MACD,CAAC,CAAC;AAEF,aAAOA;AAAA,IACR,EAAE;AAEF,YAAQ,UAAU;AAAA;AAAA;;;ACrDlB;AAAA;AAEA,QAAI,YAAY;AAEhB,QAAI,aAAa,uBAAuB,SAAS;AAEjD,QAAI,SAAS;AAEb,QAAI,UAAU,uBAAuB,MAAM;AAE3C,QAAI,sBAAsB;AAE1B,QAAI,uBAAuB,uBAAuB,mBAAmB;AAErE,QAAI,cAAc;AAElB,QAAI,eAAe,uBAAuB,WAAW;AAErD,QAAI,uBAAuB;AAE3B,QAAI,wBAAwB,uBAAuB,oBAAoB;AAEvE,QAAI,sBAAsB;AAE1B,QAAI,uBAAuB,uBAAuB,mBAAmB;AAErE,QAAI,gBAAgB;AAEpB,QAAI,iBAAiB,uBAAuB,aAAa;AAEzD,QAAI,cAAc;AAElB,QAAI,YAAY;AAEhB,QAAI,aAAa,uBAAuB,SAAS;AAEjD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAM9F,QAAI,MAAM,SAASC,OAAM;AAAA,IAAC;AAY1B,QAAI,YAAY,SAASC,WAAU,SAAS,MAAM,SAAS;AAC1D,UAAI,MAAM,IAAI,IAAI;AAElB,UAAI,OAAO,YAAY,aAAa;AACnC,cAAM,MAAM,uCAAuC;AAAA,MACpD;AAGA,UAAI,qBAAqB,GAAG,sBAAsB,SAAS,OAAO;AAClE,UAAI,aAAa,CAAC;AAClB,UAAI,WAAW,WAAW;AAC1B,UAAI,gBAAgB,IAAI,eAAe,QAAQ,GAAG;AAGlD,UAAI,OAAO,SAAS,aAAa;AAChC,kBAAU,WAAW,CAAC;AAEtB,YAAI,CAAC,QAAQ,QAAQ;AACpB,kBAAQ,SAAS,kBAAkB;AAAA,QACpC;AAEA,YAAI,QAAQ,OAAO,EAAE,QAAQ,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO;AAAA,MAC5D;AAEA,aAAO;AAAA,IACR;AAGA,cAAU,YAAY,SAAUC,OAAM;AACrC,aAAO,WAAW,QAAQA,KAAI;AAAA,IAC/B;AAGA,SAAS,QAAQ,WAAW,SAAS;AACpC,UAAI,WAAW,QAAQ,eAAe,IAAI,GAAG;AAE5C,wBAAgB,WAAW,SAAS,IAAI;AAAA,MACzC;AAAA,IACD;AALS;AAMT,aAAS,gBAAgB,UAAUA,OAAM;AACxC,UAAI,UAAUA,KAAI,IAAI,IAAI,UAAUA,MAAK,YAAY,CAAC,IAAI,IAAI,UAAUA,MAAK,YAAY,CAAC,IAAI,SAAU,MAAM,SAAS;AACtH,YAAI,MAAM;AACV,eAAO,IAAI,cAAc,gBAAgB,WAAY;AAEpD,kBAAQ,OAAO,OAAO,QAAQ,SAAS,cAAc,SAAY,KAAK,QAAQ;AAE9E,cAAI,cAAc,GAAG,QAAQ,SAAS,IAAI,UAAU,OAAO;AAC3D,wBAAc,GAAG,qBAAqB,SAAS,UAAU;AACzD,cAAI,UAAU,SAASA,KAAI;AAC3B,cAAI,UAAU,OAAO,MAAM,SAAS,UAAU;AAC9C,cAAI,WAAW,KAAK,OAAO;AAE3B,iBAAO;AAAA,QACR,CAAC;AAAA,MACF;AAAA,IACD;AAGA,aAAS,OAAO,MAAM,SAAS,SAAS;AAEvC,aAAO,KAAK;AAEZ,UAAI,UAAU,IAAI,QAAQ,MAAM,OAAO;AAIvC,UAAI,CAAC,QAAQ,MAAM,GAAG;AACrB,cAAM,IAAI,YAAY,sBAAsB,QAAQ,YAAY,MAAM,IAAI;AAAA,MAC3E;AAGA,UAAI,UAAU,QAAQ,OAAO;AAI7B,iBAAW,GAAG,qBAAqB,SAAS,OAAO;AAGnD,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACxC,gBAAQ,CAAC,EAAE,WAAW,GAAG,QAAQ,SAAS,SAAS,QAAQ,CAAC,EAAE,OAAO;AAAA,MACtE;AAEA,aAAO;AAAA,IACR;AAEA,aAAS,oBAAoB;AAE5B,UAAI,WAAW,QAAQ,SAAS,GAAG;AAClC,eAAO;AAAA,MACR;AAGA,aAAO,OAAO,KAAK,WAAW,OAAO,EAAE,CAAC;AAAA,IACzC;AAIA,QAAI,UAAU,UAAU,SAAU,SAAS;AAC1C,WAAK,YAAY,GAAG,QAAQ,SAAS,KAAK,UAAU,OAAO;AAC3D,aAAO;AAAA,IACR;AAGA,QAAI,UAAU,QAAQ,SAAU,MAAM;AACrC,UAAI,SAAS,IAAI,MAAM,OAAO,CAAC,EAAE,KAAK,GAAG;AACzC,WAAK,WAAW,KAAK,EAAE,MAAM,OAAO,CAAC;AACrC,aAAO;AAAA,IACR;AAGA,QAAI,UAAU,OAAO,WAAY;AAEhC,UAAI,CAAC,KAAK,mBAAmB;AAC5B;AAAA,MACD;AAGA,UAAI,CAAC,MAAM,QAAQ,KAAK,iBAAiB,GAAG;AAC3C,aAAK,oBAAoB,CAAC,KAAK,iBAAiB;AAAA,MACjD;AAEA,UAAI;AACJ,eAAS,KAAK,KAAK,mBAAmB;AACrC,yBAAiB,KAAK,kBAAkB,CAAC;AACzC,YAAI,WAAW,GAAG,QAAQ,SAAS,KAAK,UAAU,eAAe,OAAO;AAExE,YAAI,QAAQ,UAAU,QAAQ;AAC7B,kBAAQ,SAAS,kBAAkB;AAAA,QACpC;AAEA,aAAK,cAAc,gBAAgB,WAAY;AAC9C,cAAI,OAAO,QAAQ;AACnB,cAAI,UAAU,WAAW,QAAQ,QAAQ,OAAO,YAAY,CAAC;AAC7D,cAAI,UAAU,OAAO,MAAM,SAAS,OAAO;AAE3C,iBAAO,gBAAgB,SAAS,OAAO;AAAA,QACxC,CAAC;AAAA,MACF;AAAA,IACD;AAGA,QAAI,UAAU,SAAS,WAAY;AAClC,UAAI,CAAC,KAAK,mBAAmB;AAC5B,cAAM,IAAI,YAAY,mBAAmB;AAAA,MAC1C;AAEA,UAAI,MAAM,QAAQ,KAAK,iBAAiB,GAAG;AAC1C,iBAAS,IAAI,GAAG,IAAI,KAAK,kBAAkB,QAAQ,KAAK;AACvD,iBAAO,KAAK,kBAAkB,CAAC,GAAG,KAAK,YAAY,KAAK,QAAQ;AAAA,QACjE;AAAA,MACD,OAAO;AACN,eAAO,KAAK,mBAAmB,KAAK,YAAY,KAAK,QAAQ;AAAA,MAC9D;AAEA,aAAO;AAAA,IACR;AAEA,QAAI,UAAU,YAAY,WAAW;AAGrC,aAAS,OAAO,kBAAkB,WAAW,SAAS;AACrD,mBAAa,GAAG,qBAAqB,SAAS,SAAS;AAEvD,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,kBAAU,CAAC,EAAE,WAAW,GAAG,QAAQ,SAAS,SAAS,UAAU,CAAC,EAAE,OAAO;AACzE,SAAC,GAAG,aAAa,SAAS,UAAU,CAAC,EAAE,OAAO;AAAA,MAC/C;AAEA,OAAC,GAAG,aAAa,SAAS,OAAO;AAEjC,UAAI,WAAW,iBAAiB;AAChC,UAAI,WAAW,IAAI,SAAS,iBAAiB,SAAS,WAAW,OAAO;AACxE,eAAS,OAAO;AAEhB,UAAI,iBAAiB,aAAa;AACjC,yBAAiB,YAAY;AAAA,MAC9B;AAAA,IACD;AAGA,QAAI,OAAO,WAAW,aAAa;AAClC,aAAO,YAAY;AAAA,IACpB;AAIA,QAAI,OAAO,WAAW,aAAa;AAClC,aAAO,GAAG,YAAY,SAAU,SAAS,SAAS;AACjD,YAAI,eAAe,CAAC;AACpB,eAAO,IAAI,EAAE,KAAK,WAAY;AAC7B,uBAAa,KAAK,IAAI;AAAA,QACvB,CAAC;AACD,eAAO,UAAU,cAAc,SAAS,OAAO;AAAA,MAChD;AAAA,IACD;AAGA,WAAO,UAAU;AAAA;AAAA;", "names": ["Barcode", "CODE39", "characterValue", "CODE128", "matchSetALength", "matchSetBLength", "matchSetC", "CODE128AUTO", "CODE128A", "CODE128B", "CODE128C", "require_CODE128", "require_constants", "encode", "EAN", "checksum", "EAN13", "checksum", "EAN8", "checksum", "EAN5", "EAN2", "UPC", "UPCE", "require_constants", "ITF", "checksum", "ITF14", "require_ITF", "MSI", "MSI10", "MSI11", "MSI1010", "MSI1110", "require_MSI", "pharmacode", "codabar", "GenericBarcode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "O<PERSON><PERSON><PERSON><PERSON>", "InvalidInputException", "InvalidElementException", "NoElementException", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "API", "JsBarcode", "name"]}